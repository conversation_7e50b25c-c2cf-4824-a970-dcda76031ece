<?xml version="1.0" encoding="UTF-8"?>
<!-- Base for standalone applications. Includes minimum set of plugins -->
<feature
        id="org.jkiss.dbeaver.app.base.feature"
        label="DBeaver Base Standalone Application"
        version="25.2.0.qualifier"
        provider-name="DBeaver Corp"
        plugin="org.jkiss.dbeaver.ui.app.standalone">

    <description>DBeaver Universal Database Manager base standalone application</description>

    <copyright>DBeaver Corp and others</copyright>
    <license url="https://dbeaver.io/product/dbeaver_license.txt"/>

    <includes id="org.jkiss.dbeaver.rcp.feature" version="0.0.0"/>

    <includes id="org.jkiss.dbeaver.runtime.feature" version="0.0.0"/>
    <includes id="org.jkiss.dbeaver.ui.feature" version="0.0.0"/>
    <includes id="org.jkiss.dbeaver.db.feature" version="0.0.0"/>
    <includes id="org.jkiss.dbeaver.db.ui.feature" version="0.0.0"/>
    <includes id="org.jkiss.dbeaver.ext.ai.feature" version="0.0.0"/>

    <!-- SWT additions -->
    <plugin id="org.jkiss.dbeaver.ui.swt.linux" os="linux" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.ui.swt.macos" os="macosx" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.ui.swt.windows" os="win32" version="0.0.0" />

    <!-- Logging -->

    <plugin id="org.jkiss.dbeaver.slf4j" version="0.0.0" />

    <!-- Additional plugins for standalone version -->

    <plugin id="org.eclipse.ui.ide.application" version="0.0.0" />
    <plugin id="org.apache.commons.cli" version="0.0.0" />
    <!-- Launcher -->
    <plugin id="org.jkiss.dbeaver.launcher" version="0.0.0"/>
    

</feature>
