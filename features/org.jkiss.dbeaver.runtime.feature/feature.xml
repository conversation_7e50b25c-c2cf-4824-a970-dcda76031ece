<?xml version="1.0" encoding="UTF-8"?>
<feature
        id="org.jkiss.dbeaver.runtime.feature"
        label="DBeaver Runtime"
        version="25.2.0.qualifier"
        provider-name="DBeaver Corp"
        plugin="org.jkiss.dbeaver.model">

    <description>Universal Database Manager Runtime</description>

    <copyright>DBeaver Corp and others</copyright>
    <license url="https://dbeaver.io/product/dbeaver_license.txt"/>

    <!-- 3rd party -->
    <plugin id="slf4j.api" version="0.0.0" />
    <!-- Bouncycastle -->
    <plugin id="bcpg" version="0.0.0" />
    <plugin id="bcprov" version="0.0.0" />
    <plugin id="bcpkix" version="0.0.0" />
    <plugin id="bcutil" version="0.0.0" />

    <plugin id="org.antlr.antlr4-runtime" version="0.0.0" />
    <plugin id="com.github.jsqlparser" version="0.0.0" />
    <plugin id="org.apache.commons.jexl" version="0.0.0" />
    <plugin id="com.google.gson" version="0.0.0"/>
    <plugin id="com.jcraft.jsch" version="0.0.0" />
    <plugin id="org.apache.commons.commons-logging" version="0.0.0" />

    <!-- Explicit deps because they are required by OSGI services -->
    <plugin id="org.objectweb.asm" version="0.0.0" />
    <plugin id="org.objectweb.asm.commons" version="0.0.0" />
    <plugin id="org.objectweb.asm.tree.analysis" version="0.0.0" />
    <plugin id="org.objectweb.asm.tree" version="0.0.0" />
    <plugin id="org.objectweb.asm.util" version="0.0.0" />
    <plugin id="org.apache.aries.spifly.dynamic.bundle" version="0.0.0" />

    <!-- our plugins -->

    <plugin id="org.jkiss.utils" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.model" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.model.cli" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.model.dashboard" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.model.jdbc" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.model.sql" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.model.sql.jdbc" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.model.lsm" version="0.0.0" />

    <plugin id="org.jkiss.dbeaver.registry" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.net.ssh" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.net.ssh.jsch" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.net.ssh.sshj" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.cmp.simple" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.data.transfer" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.tasks.native" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.model.erd" version="0.0.0" />

    <plugin id="org.jkiss.dbeaver.dpi.app" version="0.0.0" />
    <plugin id="org.jkiss.dbeaver.dpi.model" version="0.0.0" />

    <!-- GIS -->
    <plugin id="org.jkiss.dbeaver.data.gis" version="0.0.0" />
    <plugin id="org.jkiss.bundle.gis" version="0.0.0" />
    <plugin id="org.jkiss.bundle.sshj" version="0.0.0" />

</feature>
