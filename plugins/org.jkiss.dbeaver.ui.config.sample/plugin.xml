<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>
<!--
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2024 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
  -->

<plugin>

    <extension point="org.jkiss.dbeaver.workbenchHandler">
        <workbenchWindowInitializer class="org.jkiss.dbeaver.ui.config.sample.WorkbenchInitializerCreateSampleDatabase" order="3"/>
    </extension>

    <extension point="org.eclipse.ui.commands">
        <command id="org.jkiss.dbeaver.ext.sample.database.commands.create" name="%commands.create.name" description="%commands.create.description"/>
    </extension>

    <extension point="org.eclipse.ui.commandImages">
        <image commandId="org.jkiss.dbeaver.ext.sample.database.commands.create" icon="platform:/plugin/org.jkiss.dbeaver.ext.sqlite/icons/sqlite_icon.png"/>
    </extension>

    <extension point="org.eclipse.ui.menus">
        <menuContribution allPopups="false" locationURI="menu:help?after=installation_help">
            <command commandId="org.jkiss.dbeaver.ext.sample.database.commands.create"/>
        </menuContribution>
    </extension>

    <extension point="org.eclipse.ui.handlers">
        <handler class="org.jkiss.dbeaver.ui.config.sample.SampleDatabaseHandler" commandId="org.jkiss.dbeaver.ext.sample.database.commands.create"/>
    </extension>

</plugin>
