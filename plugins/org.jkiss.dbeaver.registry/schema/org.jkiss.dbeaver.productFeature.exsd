<?xml version='1.0' encoding='UTF-8'?>
<!-- Schema file written by PDE -->
<schema targetNamespace="org.jkiss.dbeaver.registry" xmlns="http://www.w3.org/2001/XMLSchema">
<annotation>
      <appInfo>
         <meta.schema plugin="org.jkiss.dbeaver.registry" id="org.jkiss.dbeaver.productFeature" name="Application feature"/>
      </appInfo>
      <documentation>
         Tasks
      </documentation>
   </annotation>

   <element name="extension">
      <annotation>
         <appInfo>
            <meta.element />
         </appInfo>
      </annotation>
   </element>

   <element name="task">
      <complexType>
         <attribute name="name" type="string" use="required">
            <annotation>
               <documentation>

               </documentation>
            </annotation>
         </attribute>
      </complexType>
   </element>

   <annotation>
      <appInfo>
         <meta.section type="since"/>
      </appInfo>
      <documentation>
      </documentation>
   </annotation>

</schema>
