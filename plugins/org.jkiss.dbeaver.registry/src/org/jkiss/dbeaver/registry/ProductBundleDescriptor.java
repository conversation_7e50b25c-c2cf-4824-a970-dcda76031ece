/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2024 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.jkiss.dbeaver.registry;

import org.eclipse.core.runtime.IConfigurationElement;
import org.jkiss.dbeaver.model.impl.AbstractDescriptor;
import org.jkiss.dbeaver.model.runtime.OSDescriptor;
import org.jkiss.dbeaver.model.runtime.OSDescriptorMatch;
import org.jkiss.utils.CommonUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * ProductBundleDescriptor
 */
public class ProductBundleDescriptor extends AbstractDescriptor {

    private final String id;
    private final String label;
    private final String description;
    private final List<OSDescriptorMatch> osMatches = new ArrayList<>();

    public ProductBundleDescriptor(IConfigurationElement config)
    {
        super(config);
        this.id = config.getAttribute(RegistryConstants.ATTR_ID);
        this.label = config.getAttribute(RegistryConstants.ATTR_LABEL);
        this.description = config.getAttribute(RegistryConstants.ATTR_DESCRIPTION);
        for (IConfigurationElement os : config.getChildren(RegistryConstants.TAG_OS)) {
            this.osMatches.add(new OSDescriptorMatch(
                os.getAttribute(RegistryConstants.ATTR_NAME),
                os.getAttribute(RegistryConstants.ATTR_ARCH),
                CommonUtils.toBoolean(os.getAttribute("exclude"))));
        }
    }

    public String getId() {
        return id;
    }

    public String getLabel() {
        return label;
    }

    public String getDescription() {
        return description;
    }

    public boolean matchesOS(OSDescriptor os) {
        for (OSDescriptorMatch match : osMatches) {
            if (!match.matches(os)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public String toString() {
        return id + " (" + label + ")";
    }
}
