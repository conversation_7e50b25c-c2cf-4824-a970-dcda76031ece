/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2025 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.jkiss.dbeaver.registry.driver;

import org.jkiss.code.NotNull;
import org.jkiss.dbeaver.DBException;
import org.jkiss.dbeaver.model.connection.DBPDriver;
import org.jkiss.dbeaver.model.connection.DBPNativeClientLocation;
import org.jkiss.dbeaver.model.runtime.DBRProgressMonitor;
import org.jkiss.dbeaver.registry.NativeClientDescriptor;
import org.jkiss.dbeaver.registry.NativeClientDistributionDescriptor;

import java.io.File;

/**
 * LocalNativeClientLocation
 */
public class RemoteNativeClientLocation implements DBPNativeClientLocation {
    private final NativeClientDescriptor clientDescriptor;
    private final DBPDriver driver;

    public RemoteNativeClientLocation(@NotNull NativeClientDescriptor clientDescriptor, @NotNull DBPDriver driver) {
        this.clientDescriptor = clientDescriptor;
        this.driver = driver;
    }

    @Override
    public String getName() {
        return clientDescriptor.getId();
    }

    @NotNull
    @Override
    public File getPath() {
        NativeClientDistributionDescriptor distribution = clientDescriptor.findDistribution(driver);
        if (distribution != null) {
            File driversHome = DriverDescriptor.getCustomDriversHome().toFile();
            return new File(driversHome, distribution.getTargetPath());
        }
        return new File(getName());
    }

    @NotNull
    @Override
    public String getDisplayName() {
        return clientDescriptor.getLabel();
    }

    @Override
    public boolean validateFilesPresence(@NotNull DBRProgressMonitor progressMonitor) throws DBException, InterruptedException {
        NativeClientDistributionDescriptor distribution = clientDescriptor.findDistribution(driver);
        if (distribution != null) {
            return distribution.downloadFiles(progressMonitor, this);
        }
        return false;
    }

    @Override
    public String toString() {
        return "Remote: " + clientDescriptor.getId();
    }
}
