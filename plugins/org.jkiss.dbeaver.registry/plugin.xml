<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>
    <extension-point id="org.jkiss.dbeaver.pluginService" name="%extension-point.org.jkiss.dbeaver.pluginService.name" schema="schema/org.jkiss.dbeaver.pluginService.exsd"/>

    <!-- Non UI extensions -->
    <extension-point id="org.jkiss.dbeaver.objectManager" name="%extension-point.org.jkiss.dbeaver.objectManager.name" schema="schema/org.jkiss.dbeaver.objectManager.exsd"/>
    <extension-point id="org.jkiss.dbeaver.mavenRepository" name="%extension-point.org.jkiss.dbeaver.mavenRepository.name" schema="schema/org.jkiss.dbeaver.mavenRepository.exsd"/>
    <extension-point id="org.jkiss.dbeaver.dataSourceProvider" name="%extension-point.org.jkiss.dbeaver.dataSourceProvider.name" schema="schema/org.jkiss.dbeaver.dataSourceProvider.exsd"/>
    <extension-point id="org.jkiss.dbeaver.dataSourceStorage" name="%extension-point.org.jkiss.dbeaver.dataSourceStorage.name" schema="schema/org.jkiss.dbeaver.dataSourceStorage.exsd"/>
    <extension-point id="org.jkiss.dbeaver.dataSourceAuth" name="%extension-point.org.jkiss.dbeaver.dataSourceAuth.name" schema="schema/org.jkiss.dbeaver.dataSourceAuth.exsd"/>
    <extension-point id="org.jkiss.dbeaver.dataSourceHandler" name="%extension-point.org.jkiss.dbeaver.dataSourceHandler.name" schema="schema/org.jkiss.dbeaver.dataSourceHandler.exsd"/>
    <extension-point id="org.jkiss.dbeaver.driverManager" name="%extension-point.org.jkiss.dbeaver.driverManager.name" schema="schema/org.jkiss.dbeaver.driverManager.exsd"/>
    <extension-point id="org.jkiss.dbeaver.resourceType" name="%extension-point.org.jkiss.dbeaver.resourceType.name" schema="schema/org.jkiss.dbeaver.resourceType.exsd"/>
    <extension-point id="org.jkiss.dbeaver.networkHandler" name="%extension-point.org.jkiss.dbeaver.networkHandler.name" schema="schema/org.jkiss.dbeaver.networkHandler.exsd"/>
    <extension-point id="org.jkiss.dbeaver.resources" name="%extension-point.org.jkiss.dbeaver.resources.name" schema="schema/org.jkiss.dbeaver.resources.exsd"/>
    <extension-point id="org.jkiss.dbeaver.product.bundles" name="%extension-point.org.jkiss.dbeaver.product.bundles.name" schema="schema/org.jkiss.dbeaver.product.bundles.exsd"/>
    <extension-point id="org.jkiss.dbeaver.fileSystem" name="%extension-point.org.jkiss.dbeaver.fileSystem.name" schema="schema/org.jkiss.dbeaver.fileSystem.exsd"/>
    <extension-point id="org.jkiss.dbeaver.language" name="%extension-point.org.jkiss.dbeaver.language.name" schema="schema/org.jkiss.dbeaver.language.exsd"/>
    <extension-point id="org.jkiss.dbeaver.task" name="%extension-point.org.jkiss.dbeaver.task.name" schema="schema/org.jkiss.dbeaver.task.exsd"/>
    <extension-point id="org.jkiss.dbeaver.productFeature" name="%extension-point.org.jkiss.dbeaver.productFeature.name" schema="schema/org.jkiss.dbeaver.productFeature.exsd"/>

    <!-- Language names should be written in their respective languages. Do not localize them -->
    <extension point="org.jkiss.dbeaver.language">
        <language code="en" label="English"/>
        <language code="fr" label="Français"/>
        <language code="de" label="Deutsch"/>
        <language code="it" label="Italiano"/>
        <language code="ja" label="日本語"/>
        <language code="ko" label="한국어"/>
        <language code="pt_BR" label="Português (BR)"/>
        <language code="ro" label="Română"/>
        <language code="ru" label="Русский"/>
        <language code="zh" label="简体中文"/>
        <language code="es" label="Español"/>
        <language code="tw" label="繁體中文"/>
        <language code="uk" label="Українська"/>
        <language code="ar" label="العربية"/>
    </extension>

    <extension point="org.jkiss.dbeaver.driverManager">
        <category id="sql" name="%driver.category.sql.name" description="%driver.category.sql.description" icon="#folder_database" rank="4" promoted="true"/>
        <category id="nosql" name="%driver.category.nosql.name" description="%driver.category.nosql.description" icon="#folder_database" rank="5" promoted="true"/>
        <category id="analytic" name="%driver.category.analytical.name" description="%driver.category.analytical.description" icon="#folder_database" rank="10" promoted="true"/>
        <category id="file" name="%driver.category.file.name" description="%driver.category.file.description" icon="#folder_database" rank="11" promoted="true"/>
        <category id="embedded" name="%driver.category.embedded.name" description="%driver.category.embedded.description" icon="#folder_database" rank="12" promoted="true"/>
        <category id="timeseries" name="%driver.category.timeseries.name" description="%driver.category.timeseries.description" icon="#folder_database" rank="15" promoted="true"/>
        <category id="hadoop" name="%driver.category.hadoop.name" description="%driver.category.hadoop.description" icon="#folder_database" rank="20" promoted="true"/>
        <category id="fulltext" name="%driver.category.fulltext.name" description="%driver.category.fulltext.description" icon="#folder_database" rank="22" promoted="true"/>
        <category id="graph" name="%driver.category.graph.name" description="%driver.category.graph.description" icon="#folder_database" rank="23" promoted="true"/>
    </extension>

    <extension point="org.jkiss.dbeaver.networkHandler">
        <handler
            type="proxy"
            id="socks_proxy"
            codeName="Proxy"
            label="%handler.socks_proxy.label"
            description="%handler.socks_proxy.description"
            desktop="true"
            secured="true"
            order="3"
            handlerClass="org.jkiss.dbeaver.model.impl.net.SocksProxyImpl"/>
    </extension>

    <extension point="org.jkiss.dbeaver.dataSourceProvider">
        <datasourceOrigin id="local" label="Local configuration" class="org.jkiss.dbeaver.registry.DataSourceOriginProviderLocal"/>
    </extension>

    <extension point="org.jkiss.dbeaver.dataSourceAuth">
        <authModel
            id="native"
            label="Database Native"
            description="Database native authentication"
            class="org.jkiss.dbeaver.model.impl.auth.AuthModelDatabaseNative"
            default="true">

            <propertyGroup label="Database user">
                <property id="user" label="User" type="string"/>
                <property id="password" label="Password" type="string" features="password"/>
            </propertyGroup>

        </authModel>
    </extension>

    <extension point="org.eclipse.core.contenttype.contentTypes">
        <content-type
                file-extensions="bm"
                id="org.jkiss.dbeaver.bookmark"
                name="Database Bookmarks"
                priority="normal"
                describer="org.jkiss.dbeaver.ui.resources.bookmarks.BookmarkContentTypeDescriber"/>

        <content-type file-extensions="lnk" id="org.jkiss.dbeaver.shortcut" name="Windows shortcuts" priority="normal">
            <describer class="org.eclipse.core.runtime.content.BinarySignatureDescriber">
                <!-- http://msdn.microsoft.com/en-us/library/dd871305%28PROT.10%29.aspx -->
                <parameter name="signature" value="4C,00,00,00,01,14,02,00,00,00,00,00,C0,00,00,00,00,00,00,46"/>
                <parameter name="offset" value="0"/>
            </describer>
        </content-type>
    </extension>

    <extension point="org.jkiss.dbeaver.resourceType">
        <type id="default"/>
    </extension>

    <extension point="org.jkiss.dbeaver.mavenRepository">
        <repository id="maven-central" name="%maven.respository.central" url="https://repo1.maven.org/maven2/" order="0"/>
        <repository id="oss.sonatype.org" snapshot="true" name="%maven.repository.sonatype" url="https://oss.sonatype.org/content/repositories/snapshots/" order="0"/>

        <!-- Non-HTTPS Maven Central is no longer alive. Hooray. -->
        <!--
                <repository id="maven-central-unsecure" name="%maven.respository.central.unsecure" url="http://central.maven.org/maven2/" order="99999"/>
        -->
    </extension>

</plugin>
