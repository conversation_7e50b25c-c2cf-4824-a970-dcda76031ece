
driver.category.analytical.description = Аналітичні бази даних, бази даних орієнтовані на стовпці
driver.category.analytical.name        = Аналітичні
driver.category.embedded.description   = Вбудовані (безсерверні) бази даних
driver.category.embedded.name          = Вбудовані
driver.category.fulltext.description   = Бази даних для повнотекстового пошуку
driver.category.fulltext.name          = Повнотекстовий пошук
driver.category.graph.description      = Графові бази даних
driver.category.graph.name             = Графові бази даних
driver.category.hadoop.description     = Бази даних на основі Hadoop для BigData
driver.category.hadoop.name            = Hadoop / BigData
driver.category.nosql.description      = NoSQL / Бази даних BigData
driver.category.nosql.name             = NoSQL
driver.category.sql.description        = Бази даних SQL (реляційні)
driver.category.sql.name               = SQL
driver.category.timeseries.description = Бази даних для роботи з часовими рядами
driver.category.timeseries.name        = Часові ряди

extension-point.org.jkiss.dbeaver.authProvider.name       = Постачальник аутентифікації
extension-point.org.jkiss.dbeaver.dataSourceAuth.name     = Модель аутентифікації джерела даних
extension-point.org.jkiss.dbeaver.dataSourceHandler       = Обробники підключення/відключення джерела даних
extension-point.org.jkiss.dbeaver.dataSourceProvider.name = Постачальник джерел даних
extension-point.org.jkiss.dbeaver.dataSourceStorage.name  = Зберігання конфігурації джерела даних
extension-point.org.jkiss.dbeaver.fileSystem.name         = Віртуальні файлові системи
extension-point.org.jkiss.dbeaver.language.name           = Мови
extension-point.org.jkiss.dbeaver.mavenRepository.name    = Налаштування репозиторіїв Maven
extension-point.org.jkiss.dbeaver.networkHandler.name     = Обробники мережі
extension-point.org.jkiss.dbeaver.objectManager.name      = Менеджери об’єктів бази даних
extension-point.org.jkiss.dbeaver.product.bundles.name    = Пакети продукту
extension-point.org.jkiss.dbeaver.productFeature.name     = Функції продукту
extension-point.org.jkiss.dbeaver.resourceHandler.name    = Обробники ресурсів
extension-point.org.jkiss.dbeaver.resourceType.name       = Типи ресурсів
extension-point.org.jkiss.dbeaver.resources.name          = Ресурси
extension-point.org.jkiss.dbeaver.task.name               = Завдання та типи завдань

handler.socks_proxy.description = Проксі SOCKS4/SOCKS5
handler.socks_proxy.label       = Проксі

meta.org.jkiss.dbeaver.registry.DataSourceDescriptor$ContextInfo.name.name      = Ім’я контексту
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.description.name           = Опис
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.name.name                  = Ім’я
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.origin.name                = Походження
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyAddress.name       = Адреса
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectTime.name   = Час підключення
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectType.name   = Тип підключення
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDatabase.name      = База даних
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriver.name        = Драйвер
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriverType.name    = Тип драйвера
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerDetails.name = Деталі
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerName.name    = Сервер
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyURL.name           = URL
meta.org.jkiss.dbeaver.registry.DriverDescriptor.category.name                  = Категорія драйвера
meta.org.jkiss.dbeaver.registry.DriverDescriptor.description.name               = Опис
meta.org.jkiss.dbeaver.registry.DriverDescriptor.driverClassName.name           = Ім’я класу драйвера
meta.org.jkiss.dbeaver.registry.DriverDescriptor.name.name                      = Ім’я драйвера
meta.org.jkiss.dbeaver.registry.DriverDescriptor.sampleURL.name                 = URL зразка
