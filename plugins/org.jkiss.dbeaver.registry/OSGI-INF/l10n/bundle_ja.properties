extension-point.org.jkiss.dbeaver.mavenRepository.name =Mavenリポジトリ設定
extension-point.org.jkiss.dbeaver.dataSourceProvider.name =データソースプロバイダ
extension-point.org.jkiss.dbeaver.resourceHandler.name =リソースハンドラ
extension-point.org.jkiss.dbeaver.networkHandler.name =ネットワークハンドラ
extension-point.org.jkiss.dbeaver.resources.name =リソース
extension-point.org.jkiss.dbeaver.product.bundles.name =製品バンドル
extension-point.org.jkiss.dbeaver.language.name =言語

handler.socks_proxy.description =SOCKS4 / SOCKS5プロキシ
handler.socks_proxy.label =SOCKSプロキシ

meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.name.name=名
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.description.name=説明
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriverType.name=ドライバタイプ
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyAddress.name=住所
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDatabase.name=データベース
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyURL.name=URL
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerName.name=サーバ
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriver.name=ドライバ
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectTime.name=接続時間
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectType.name=接続タイプ
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor$ContextInfo.name.name=コンテキスト名
meta.org.jkiss.dbeaver.registry.DriverDescriptor.category.name=ドライバカテゴリ
meta.org.jkiss.dbeaver.registry.DriverDescriptor.name.name=ドライバ名
meta.org.jkiss.dbeaver.registry.DriverDescriptor.description.name=説明
meta.org.jkiss.dbeaver.registry.DriverDescriptor.driverClassName.name=ドライバークラス
meta.org.jkiss.dbeaver.registry.DriverDescriptor.sampleURL.name=URL
