driver.category.analytical.description = Baze de date analitice, baze de date orientate pe coloane
driver.category.analytical.name        = Analitic
driver.category.embedded.description   = Baze de date încorporate (fără server).
driver.category.embedded.name          = Încorporat
driver.category.fulltext.description   = Baze de date de căutare full-text
driver.category.fulltext.name          = Căutare text integral
driver.category.graph.description      = Baze de date grafice
driver.category.graph.name             = Baze de date grafice
driver.category.hadoop.description     = Baze de date BigData bazate pe Hadoop
driver.category.hadoop.name            = Hadoop / BigData
driver.category.nosql.description      = Baze de date NoSQL / BigData
driver.category.nosql.name             = NoSQL
driver.category.sql.description        = Baze de date SQL (relaționale).
driver.category.sql.name               = SQL
driver.category.timeseries.description = Baze de date pentru serii temporale
driver.category.timeseries.name        = Serii temporale

extension-point.org.jkiss.dbeaver.authProvider.name       = Furnizor de autentificare
extension-point.org.jkiss.dbeaver.dataSourceAuth.name     = Model de autentificare DataSource
extension-point.org.jkiss.dbeaver.dataSourceHandler       = Managerii de conectare/deconectare DataSource
extension-point.org.jkiss.dbeaver.dataSourceProvider.name = Furnizor de surse de date
extension-point.org.jkiss.dbeaver.dataSourceStorage.name  = Stocarea configuraţiei DataSource
extension-point.org.jkiss.dbeaver.fileSystem.name         = Sisteme de fişiere virtuale
extension-point.org.jkiss.dbeaver.language.name           = Limbi
extension-point.org.jkiss.dbeaver.mavenRepository.name    = Configurarea depozitelor Maven
extension-point.org.jkiss.dbeaver.networkHandler.name     = Operatorii de reţea
extension-point.org.jkiss.dbeaver.objectManager.name      = Managerii de obiecte baze de date
extension-point.org.jkiss.dbeaver.product.bundles.name    = Pachetele de produse
extension-point.org.jkiss.dbeaver.productFeature.name     = Caracteristica produsului
extension-point.org.jkiss.dbeaver.resourceHandler.name    = Manipulatorii de resurse
extension-point.org.jkiss.dbeaver.resourceType.name       = Tipuri de resurse
extension-point.org.jkiss.dbeaver.resources.name          = Resurse
extension-point.org.jkiss.dbeaver.task.name               = Sarcini şi tipuri de sarcini

handler.socks_proxy.description = SOCKS4/SOCKS5 proxy
handler.socks_proxy.label       = Proxy

meta.org.jkiss.dbeaver.registry.DataSourceDescriptor$ContextInfo.name.name      = Numele contextului
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.description.name           = Descriere
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.name.name                  = Nume
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.origin.name                = Origine
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyAddress.name       = Adresa
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectTime.name   = Ora de conectare
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectType.name   = Tipul de conectare
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDatabase.name      = Baza de date
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriver.name        = Driver
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriverType.name    = Tipul de driver
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerDetails.name = Detalii
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerName.name    = Server
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyURL.name           = URL
meta.org.jkiss.dbeaver.registry.DriverDescriptor.category.name                  = Categoria driver-ului
meta.org.jkiss.dbeaver.registry.DriverDescriptor.description.name               = Descriere
meta.org.jkiss.dbeaver.registry.DriverDescriptor.driverClassName.name           = Clasa driver-ului
meta.org.jkiss.dbeaver.registry.DriverDescriptor.name.name                      = Numele driver-ului
meta.org.jkiss.dbeaver.registry.DriverDescriptor.sampleURL.name                 = URL
