# Translated By 2021 Ralic Lo (rali<PERSON><PERSON>@gmail.com)		
Bundle-Name	=	DBeaver Registry Plug-in
Bundle-Vendor	=	DBeaver Corp
extension-point.org.jkiss.dbeaver.dataSourceAuth.name	=	資料源認證模型
extension-point.org.jkiss.dbeaver.dataSourceProvider.name	=	資料源提供者
extension-point.org.jkiss.dbeaver.dataSourceStorage.name	=	資料源組態儲存
extension-point.org.jkiss.dbeaver.language.name	=	語言
extension-point.org.jkiss.dbeaver.mavenRepository.name	=	Maven 儲存庫組態
extension-point.org.jkiss.dbeaver.networkHandler.name	=	網路處理器
extension-point.org.jkiss.dbeaver.objectManager.name	=	資料庫物件管理器
extension-point.org.jkiss.dbeaver.product.bundles.name	=	產品包
extension-point.org.jkiss.dbeaver.resourceHandler.name	=	資源處理程序
extension-point.org.jkiss.dbeaver.resources.name	=	資源
extension-point.org.jkiss.dbeaver.task.name	=	任務和任務類型
handler.socks_proxy.description	=	SOCKS4/SOCKS5 代理
handler.socks_proxy.label	=	SOCKS 代理
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.description.name	=	描述
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.name.name	=	名稱
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.origin.name	=	起源
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyAddress.name	=	地址
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectTime.name	=	連線時間
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectType.name	=	連線類型
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDatabase.name	=	資料庫
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriver.name	=	驅動
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriverType.name	=	驅動類型
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerDetails.name	=	細節
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerName.name	=	伺服器
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyURL.name	=	URL
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor$ContextInfo.name.name	=	上下文名稱
meta.org.jkiss.dbeaver.registry.DriverDescriptor.category.name	=	驅動類別
meta.org.jkiss.dbeaver.registry.DriverDescriptor.description.name	=	描述
meta.org.jkiss.dbeaver.registry.DriverDescriptor.driverClassName.name	=	驅動類別
meta.org.jkiss.dbeaver.registry.DriverDescriptor.name.name	=	驅動名稱
meta.org.jkiss.dbeaver.registry.DriverDescriptor.sampleURL.name	=	URL
