
driver.category.analytical.description = Bancos de dados analíticos, bancos de dados orientados em colunas
driver.category.analytical.name        = Analítico
driver.category.embedded.description   = Bancos de dados incorporados (sem servidor)
driver.category.embedded.name          = Incorporado
driver.category.fulltext.description   = Bancos de dados de pesquisa full-text
driver.category.fulltext.name          = Pesquisa full-text
driver.category.graph.description      = Bancos de dados gráficos
driver.category.graph.name             = Bancos de dados gráficos
driver.category.hadoop.description     = Bancos de dados BigData baseados em Hadoop
driver.category.hadoop.name            = Hadoop / BigData
driver.category.nosql.description      = Bancos de dados NoSQL / BigData
driver.category.nosql.name             = NoSQL
driver.category.sql.description        = Bancos de dados SQL (relacional)
driver.category.sql.name               = SQL
driver.category.timeseries.description = Bancos de dados Timeseries
driver.category.timeseries.name        = Timeseries

extension-point.org.jkiss.dbeaver.authProvider.name       = Provedor de autenticação
extension-point.org.jkiss.dbeaver.dataSourceAuth.name     = Modelo de autenticação da fonte de dados
extension-point.org.jkiss.dbeaver.dataSourceHandler       = Manipuladores de conexão/desconexão com a fonte de dados
extension-point.org.jkiss.dbeaver.dataSourceProvider.name = Provedor da fonte de dados
extension-point.org.jkiss.dbeaver.dataSourceStorage.name  = Armazenamento de configuração da fonte de dados
extension-point.org.jkiss.dbeaver.fileSystem.name         = Sistemas de arquivos virtuais
extension-point.org.jkiss.dbeaver.language.name           = Linguagens
extension-point.org.jkiss.dbeaver.mavenRepository.name    = Configuração dos repositórios Maven
extension-point.org.jkiss.dbeaver.networkHandler.name     = Manipuladores de rede
extension-point.org.jkiss.dbeaver.objectManager.name      = Gerenciadores de objetos de banco de dados
extension-point.org.jkiss.dbeaver.product.bundles.name    = Pacotes de produtos
extension-point.org.jkiss.dbeaver.productFeature.name     = Recurso do produto
extension-point.org.jkiss.dbeaver.resourceHandler.name    = Manipuladores de recursos
extension-point.org.jkiss.dbeaver.resourceType.name       = Tipos de recurso
extension-point.org.jkiss.dbeaver.resources.name          = Recursos
extension-point.org.jkiss.dbeaver.task.name               = Tarefas e tipos de tarefas

handler.socks_proxy.description = Proxy SOCKS4/SOCKS5
handler.socks_proxy.label       = Proxy

meta.org.jkiss.dbeaver.registry.DataSourceDescriptor$ContextInfo.name.name      = Nome do contexto
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.description.name           = Descrição
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.name.name                  = Nome
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.origin.name                = Origem
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyAddress.name       = Endereço
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectTime.name   = Tempo da conexão
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectType.name   = Tipo da conexão
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDatabase.name      = Banco de dados
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriver.name        = Driver
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriverType.name    = Tipo de driver
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerDetails.name = Detalhes
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerName.name    = Servidor
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyURL.name           = URL
meta.org.jkiss.dbeaver.registry.DriverDescriptor.category.name                  = Categoria de driver
meta.org.jkiss.dbeaver.registry.DriverDescriptor.description.name               = Descrição
meta.org.jkiss.dbeaver.registry.DriverDescriptor.driverClassName.name           = Classe de driver
meta.org.jkiss.dbeaver.registry.DriverDescriptor.name.name                      = Nome do driver
meta.org.jkiss.dbeaver.registry.DriverDescriptor.sampleURL.name                 = URL
