# DBeaver - Universal Database Manager
# Copyright (C) 2010-2024 DBeaver Corp and others
# Copyright (C) 2016 <PERSON> (<EMAIL>)

extension-point.org.jkiss.dbeaver.dataSourceProvider.name      = DataSource-Provider
extension-point.org.jkiss.dbeaver.language.name                = Sprache
extension-point.org.jkiss.dbeaver.mavenRepository.name         = Maven Repositories Konfiguration
extension-point.org.jkiss.dbeaver.networkHandler.name          = Netzwerk-Handler
extension-point.org.jkiss.dbeaver.pluginService.name           = Plugin Service
extension-point.org.jkiss.dbeaver.product.bundles.name         = Produktbundle
extension-point.org.jkiss.dbeaver.resourceHandler.name         = Resourcen-Handler
extension-point.org.jkiss.dbeaver.resources.name               = Resourcen

handler.socks_proxy.description = Proxy SOCKS4/SOCKS5
handler.socks_proxy.label       = Proxy

meta.org.jkiss.dbeaver.registry.DataSourceDescriptor$ContextInfo.name.name    = Kontextname
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.description.name         = Beschreibung
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.name.name                = Name
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyAddress.name     = Addresse
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectTime.name = Verbindungszeit
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectType.name = Verbindungstyp
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDatabase.name    = Datenbank
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriver.name      = Treiber
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriverType.name  = Treibertyp
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerName.name  = Server
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyURL.name         = URL
meta.org.jkiss.dbeaver.registry.DriverDescriptor.category.name                = Treiberkategorie
meta.org.jkiss.dbeaver.registry.DriverDescriptor.description.name             = Beschreibung
meta.org.jkiss.dbeaver.registry.DriverDescriptor.driverClassName.name         = Treiberklasse
meta.org.jkiss.dbeaver.registry.DriverDescriptor.name.name                    = Treibername
meta.org.jkiss.dbeaver.registry.DriverDescriptor.sampleURL.name               = URL
