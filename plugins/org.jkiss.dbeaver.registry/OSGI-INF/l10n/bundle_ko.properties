# Copyright (C) 2019 <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
handler.socks_proxy.description = SOCKS4/SOCKS5 프록시
handler.socks_proxy.label = SOCKS 프록시

meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.name.name=이름
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.description.name=설명
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriverType.name=드라이버 유형
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyAddress.name=주소
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDatabase.name=데이터베이스
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyURL.name=URL
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerName.name=서버
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriver.name=드라이버
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectTime.name=연결 시간
meta.org.jkiss.dbe<PERSON>.registry.DataSourceDescriptor.propertyConnectType.name=연결 유형
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor$ContextInfo.name.name=컨텍스트명
meta.org.jkiss.dbeaver.registry.DriverDescriptor.category.name=드라이버 범주
meta.org.jkiss.dbeaver.registry.DriverDescriptor.name.name=드라이버명
meta.org.jkiss.dbeaver.registry.DriverDescriptor.description.name=설명
meta.org.jkiss.dbeaver.registry.DriverDescriptor.driverClassName.name=드라이버 클래스
meta.org.jkiss.dbeaver.registry.DriverDescriptor.sampleURL.name=URL
