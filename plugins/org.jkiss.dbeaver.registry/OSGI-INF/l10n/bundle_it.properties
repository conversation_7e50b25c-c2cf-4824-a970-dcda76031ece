extension-point.org.jkiss.dbeaver.language.name        = Lingue
extension-point.org.jkiss.dbeaver.mavenRepository.name = Configurazione repository Maven
extension-point.org.jkiss.dbeaver.resources.name       = Risorse

handler.socks_proxy.description = Proxy SOCKS4/SOCKS5

meta.org.jkiss.dbeaver.registry.DataSourceDescriptor$ContextInfo.name.name      = Nome del contesto
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.description.name           = Descrizione
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.name.name                  = Nome
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.origin.name                = Origine
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyAddress.name       = Indirizzo
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriverType.name    = Tipo di driver
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerDetails.name = Dettagli
meta.org.jkiss.dbeaver.registry.DriverDescriptor.category.name                  = Categoria driver
meta.org.jkiss.dbeaver.registry.DriverDescriptor.description.name               = Descrizione
meta.org.jkiss.dbeaver.registry.DriverDescriptor.name.name                      = Nome driver
