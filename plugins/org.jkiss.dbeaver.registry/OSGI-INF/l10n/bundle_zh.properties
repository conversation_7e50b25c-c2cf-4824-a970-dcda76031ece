# Copyright (C) 2017 <PERSON>, <PERSON><PERSON> (<EMAIL>)
# Copyright (C) 2012 Brook.Tran (<EMAIL>)
# Copyright (C) 2018 <PERSON> (<EMAIL>)

driver.category.analytical.description = 分析型数据库，面向列的数据库
driver.category.analytical.name        = 分析型
driver.category.embedded.description   = 嵌入式（无服务器）数据库
driver.category.embedded.name          = 嵌入式
driver.category.fulltext.description   = 全文检索数据库
driver.category.fulltext.name          = 全文搜索
driver.category.graph.description      = 图数据库
driver.category.graph.name             = 图数据库
driver.category.hadoop.description     = 基于 Hadoop 的大数据数据库
driver.category.hadoop.name            = Hadoop / 大数据
driver.category.nosql.description      = NoSQL / 大数据数据库
driver.category.sql.description        = SQL（关系）数据库
driver.category.timeseries.description = 时序数据库
driver.category.timeseries.name        = 时间序列

extension-point.org.jkiss.dbeaver.authProvider.name       = 身份验证提供程序
extension-point.org.jkiss.dbeaver.dataSourceAuth.name     = 数据源认证模型
extension-point.org.jkiss.dbeaver.dataSourceHandler       = 数据源 连接/断开处理程序
extension-point.org.jkiss.dbeaver.dataSourceProvider.name = 数据源提供者
extension-point.org.jkiss.dbeaver.dataSourceStorage.name  = 数据源配置存储
extension-point.org.jkiss.dbeaver.fileSystem.name         = 虚拟文件系统
extension-point.org.jkiss.dbeaver.language.name           = 语言
extension-point.org.jkiss.dbeaver.mavenRepository.name    = Maven 仓库配置
extension-point.org.jkiss.dbeaver.networkHandler.name     = 网络处理器
extension-point.org.jkiss.dbeaver.objectManager.name      = 数据库对象管理
extension-point.org.jkiss.dbeaver.product.bundles.name    = 产品包
extension-point.org.jkiss.dbeaver.productFeature.name     = 产品特性
extension-point.org.jkiss.dbeaver.resourceHandler.name    = 资源处理程序
extension-point.org.jkiss.dbeaver.resources.name          = 资源
extension-point.org.jkiss.dbeaver.task.name               = 任务和任务类型

handler.socks_proxy.description = SOCKS4/SOCKS5 代理
handler.socks_proxy.label       = SOCKS 代理

meta.org.jkiss.dbeaver.registry.DataSourceDescriptor$ContextInfo.name.name      = 上下文名称
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.description.name           = 描述
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.name.name                  = 名称
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.origin.name                = 来源
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyAddress.name       = 地址
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectTime.name   = 连接时间
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectType.name   = 连接类型
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDatabase.name      = 数据库
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriver.name        = 驱动
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriverType.name    = 驱动类型
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerDetails.name = 详情
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerName.name    = 服务器
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyURL.name           = URL
meta.org.jkiss.dbeaver.registry.DriverDescriptor.category.name                  = 驱动类别
meta.org.jkiss.dbeaver.registry.DriverDescriptor.description.name               = 描述
meta.org.jkiss.dbeaver.registry.DriverDescriptor.driverClassName.name           = 驱动类
meta.org.jkiss.dbeaver.registry.DriverDescriptor.name.name                      = 驱动名称
meta.org.jkiss.dbeaver.registry.DriverDescriptor.sampleURL.name                 = URL
extension-point.org.jkiss.dbeaver.resourceType.name=资源类型
driver.category.sql.name=SQL
driver.category.nosql.name=NoSQL