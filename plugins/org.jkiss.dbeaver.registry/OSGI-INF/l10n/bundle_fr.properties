# DBeaver - Universal Database Manager
# Copyright (C) 2010-2024 DBeaver Corp and others

extension-point.org.jkiss.dbeaver.mavenRepository.name = Config des dépôts Maven
extension-point.org.jkiss.dbeaver.dataSourceProvider.name = Fournisseur de Sources de données
extension-point.org.jkiss.dbeaver.resourceHandler.name = Handlers de ressources
extension-point.org.jkiss.dbeaver.networkHandler.name = Handlers de réseaux
extension-point.org.jkiss.dbeaver.resources.name = Ressources
extension-point.org.jkiss.dbeaver.product.bundles.name = Product bundles
extension-point.org.jkiss.dbeaver.language.name=Langages

handler.socks_proxy.description = Proxy SOCKS4/SOCKS5
handler.socks_proxy.label = Proxy SOCKS

meta.org.jkiss.dbeaver.registry.DataSourceDescriptor$ContextInfo.name.name                  = Nom du contexte
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.description.name                       = Description
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.name.name                              = Nom
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyAddress.name                   = Adresse
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectTime.name               = Heure de connexion
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectType.name               = Type de connexion
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDatabase.name                  = Base de données
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriver.name                    = Pilote
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriverType.name                = Type de pilote
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerName.name                = Serveur
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyURL.name                       = URL
meta.org.jkiss.dbeaver.registry.DriverDescriptor.category.name                              = Catégorie de pilote
meta.org.jkiss.dbeaver.registry.DriverDescriptor.description.name                           = Description
meta.org.jkiss.dbeaver.registry.DriverDescriptor.driverClassName.name                       = Classe de pilote
meta.org.jkiss.dbeaver.registry.DriverDescriptor.name.name                                  = Nom du pilote
meta.org.jkiss.dbeaver.registry.DriverDescriptor.sampleURL.name                             = URL
extension-point.org.jkiss.dbeaver.task.name=Tâches et types de tâche
language.zh.label=Chinois simplifié
language.tw.label=Chinois traditionnel
language.pt_BR.label=Portuguais
language.ro.label=Roumain
driver.category.embedded.name=Embarqué
driver.category.embedded.description=Bases de données embarquées (sans serveur)
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.origin.name=Origine
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerDetails.name=Détails
