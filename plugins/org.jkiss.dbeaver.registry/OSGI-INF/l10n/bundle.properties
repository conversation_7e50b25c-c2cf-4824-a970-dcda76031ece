extension-point.org.jkiss.dbeaver.authProvider.name = Authentication provider
extension-point.org.jkiss.dbeaver.mavenRepository.name = Maven repositories config
extension-point.org.jkiss.dbeaver.dataSourceProvider.name = DataSource provider
extension-point.org.jkiss.dbeaver.dataSourceStorage.name = DataSource configuration storage
extension-point.org.jkiss.dbeaver.dataSourceAuth.name = DataSource authentication model
extension-point.org.jkiss.dbeaver.dataSourceHandler = DataSource connect/disconnect handlers
extension-point.org.jkiss.dbeaver.objectManager.name = Database object managers
extension-point.org.jkiss.dbeaver.resourceType.name = Resource types
extension-point.org.jkiss.dbeaver.resourceHandler.name = Resource handlers
extension-point.org.jkiss.dbeaver.networkHandler.name = Network handlers
extension-point.org.jkiss.dbeaver.resources.name = Resources
extension-point.org.jkiss.dbeaver.product.bundles.name = Product bundles
extension-point.org.jkiss.dbeaver.fileSystem.name = Virtual file systems
extension-point.org.jkiss.dbeaver.language.name = Languages
extension-point.org.jkiss.dbeaver.task.name = Tasks and task types
extension-point.org.jkiss.dbeaver.productFeature.name = Product feature

driver.category.sql.name = SQL
driver.category.sql.description = SQL (relational) databases
driver.category.nosql.name = NoSQL
driver.category.nosql.description = NoSQL / BigData databases
driver.category.analytical.name = Analytical
driver.category.analytical.description = Analytical databases, column-oriented databases
driver.category.timeseries.name = Timeseries
driver.category.timeseries.description = Timeseries databases
driver.category.file.name = Files
driver.category.file.description = Flat files as databases
driver.category.embedded.name = Embedded
driver.category.embedded.description = Embedded (serverless) databases
driver.category.hadoop.name = Hadoop / BigData
driver.category.hadoop.description = Hadoop based BigData databases
driver.category.fulltext.name = Full-text search
driver.category.fulltext.description = Full-text search databases
driver.category.graph.name = Graph databases
driver.category.graph.description = Graph databases

handler.socks_proxy.description = SOCKS4/SOCKS5 proxy
handler.socks_proxy.label = Proxy

meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.name.name=Name
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.description.name=Description
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.origin.name=Origin
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriverType.name=Driver Type
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyAddress.name=Address
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDatabase.name=Database
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyURL.name=URL
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerName.name=Server
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerDetails.name=Details
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriver.name=Driver
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectTime.name=Connect Time
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectType.name=Connect Type
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor$ContextInfo.name.name=Context Name
meta.org.jkiss.dbeaver.registry.DriverDescriptor.category.name=Driver Category
meta.org.jkiss.dbeaver.registry.DriverDescriptor.name.name=Driver Name
meta.org.jkiss.dbeaver.registry.DriverDescriptor.description.name=Description
meta.org.jkiss.dbeaver.registry.DriverDescriptor.driverClassName.name=Driver Class
meta.org.jkiss.dbeaver.registry.DriverDescriptor.sampleURL.name=URL

