meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.description.name         = Описание
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.name.name                = Название
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyAddress.name     = Адрес
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectTime.name = Время Соединения
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyConnectType.name = Тип Соединения
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDatabase.name    = База Данных
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriver.name      = Драйвер
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyDriverType.name  = Тип Драйвера
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyServerName.name  = Сервер
meta.org.jkiss.dbeaver.registry.DataSourceDescriptor.propertyURL.name         = URL
meta.org.jkiss.dbeaver.registry.DriverDescriptor.category.name                = Категория Драйвера
meta.org.jkiss.dbeaver.registry.DriverDescriptor.description.name             = Описание
meta.org.jkiss.dbeaver.registry.DriverDescriptor.driverClassName.name         = Класс Драйвера
meta.org.jkiss.dbeaver.registry.DriverDescriptor.name.name                    = Имя Драйвера
meta.org.jkiss.dbeaver.registry.DriverDescriptor.sampleURL.name               = URL
