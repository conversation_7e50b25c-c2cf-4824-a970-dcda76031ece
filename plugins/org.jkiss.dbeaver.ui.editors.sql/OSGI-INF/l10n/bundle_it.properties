
category.sqleditor.description = Comandi Editor SQL
category.sqleditor.name        = Editor SQL

colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description  = Colore commento SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label        = Colore commento SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description = Sfondo testo SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label       = Sfondo disabilitato SQL

column.org.jkiss.dbeaver.ui.editors.columns.script.position.name = Posizione script

command.org.jkiss.dbeaver.core.sql.editor.console.name                   = Apri console SQL
command.org.jkiss.dbeaver.core.sql.editor.create.description             = Apri Nuovo Script SQL
command.org.jkiss.dbeaver.core.sql.editor.create.label                   = Crea script SQL
command.org.jkiss.dbeaver.core.sql.editor.create.name                    = Nuovo Script SQL
command.org.jkiss.dbeaver.core.sql.editor.open.description               = Apri Script SQL
command.org.jkiss.dbeaver.core.sql.editor.open.name                      = Script SQL
command.org.jkiss.dbeaver.core.sql.editor.recent.name                    = Script SQL recente
command.org.jkiss.dbeaver.ui.editors.sql.CollapseAllFoldings.name        = Collassa tutti i raggruppamenti
command.org.jkiss.dbeaver.ui.editors.sql.ExpandAllFoldings.name          = Espandi tutti i raggruppamenti
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description           = Chiudi le schede dei risultati
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name                  = Chiudi
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description       = Aggiungi o rimuovi commento multi linea
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description      = Aggiungi o rimuovi commento di singola riga
command.org.jkiss.dbeaver.ui.editors.sql.copy.query.name                 = Copia query selezionata
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.description    = Elimina file dello script corrente
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.name           = Elimina questo script
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description           = Carica da file
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name                  = Carica script SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description         = Visualizza piano di esecuzione
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name                = Visualizza piano di esecuzione
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description          = Esegui Script
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name                 = Esegui Script SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description       = Esegui Comando
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name              = Esegui Comando SQL
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description           = Salva su file
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name                  = Salva script SQL
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.name          = Mostra pannelli nelle schede dei risultati
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description = Mostra/nascondi pannello dei risultati
command.org.jkiss.dbeaver.ui.editors.sql.web.name                        = Cerca nel web
command.org.jkiss.dbeaver.ui.editors.text.content.format.description     = Formatta testo
command.org.jkiss.dbeaver.ui.editors.text.content.format.name            = Formatta contenuto

confirm.dangerous_sql.title = Conferma esecuzione di query pericolose
confirm.sql.group.name      = Editor SQL
confirm.sql.toggleMessage   = Non chiedermi di nuovo

editor.sql.name = Editor SQL

menu.database.sql.generate                                        = &Genera SQL
menu.org.jkiss.dbeaver.core.connection.sqleditor                  = Editor SQL
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label     = Esegui
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.extraPanels.label = Pannelli
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label        = File
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label      = Layout
menu.org.jkiss.dbeaver.ui.editors.sql.connection.separate.label   = Apri connessione separata
menu.sqleditor                                                    = Editor SQL
menu.sqleditor.open.default                                       = Comando di default

page.org.jkiss.dbeaver.preferences.main.sql.completion.description = Impostazioni auto completamento
page.org.jkiss.dbeaver.preferences.main.sql.completion.name        = Auto completamento
page.org.jkiss.dbeaver.preferences.main.sql.format.description     = Impostazioni formattazione SQL
page.org.jkiss.dbeaver.preferences.main.sql.resources.name         = Script
page.org.jkiss.dbeaver.preferences.main.sqleditor.description      = Impostazioni editor SQL
page.org.jkiss.dbeaver.preferences.main.sqleditor.name             = Editor SQL

pref.page.name.sql.completion = Auto completamento
pref.page.name.sql.dialects   = Dialetti SQL
pref.page.name.sql.editor     = Editor SQL
pref.page.name.sql.resources  = Script

sql.convert.label.line.delimiter.name = Delimitatore di linea
sql.convert.unformatted.text.name     = Testo non formattato

themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description = Editor SQL
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label       = Editor SQL

view.sql.results.title = Dati
