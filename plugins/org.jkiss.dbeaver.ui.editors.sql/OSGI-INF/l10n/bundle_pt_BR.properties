category.sqleditor.description = Comandos de editor SQL
category.sqleditor.name        = Editor SQL

colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.derived.foreground.description  = Cor do texto do alias de coluna SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.derived.foreground.label        = Coluna derivada SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.foreground.description          = Cor do nome de coluna SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.foreground.label                = Nome de coluna SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.description         = Cor de comando SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.label               = Comando SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description         = Cor de comentário SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label               = Comentário SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.composite.field.foreground.description = Cor de campo de tipo composto
colorDefinition.org.jkiss.dbeaver.sql.editor.color.composite.field.foreground.label       = Campo de tipo composto
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.description        = Cor de tipo de dados SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.label              = Tipo de dados SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.description       = Cor de delimitador de comando SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.label             = Delimitador SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description        = Cor do plano de fundo do texto em SQL desativado
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label              = Plano de fundo de SQL desativado
colorDefinition.org.jkiss.dbeaver.sql.editor.color.function.foreground.description        = Cor de function SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.function.foreground.label              = Function SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.description         = Cor de palavras-chave SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.label               = Palavras-chave SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.description          = Cor de número SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.label                = Número SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.description       = Cor de parâmetro SQL (?, :param, etc.)
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.label             = Parâmetro SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.schema.foreground.description          = Cor do nome de esquema SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.schema.foreground.label                = Nome de esquema SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.semanticError.foreground.description   = Cor do erro semântico SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.semanticError.foreground.label         = Erro semântico SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.sqlVariable.foreground.description     = Cor do nome de variável SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.sqlVariable.foreground.label           = Nome de variável SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.description          = Cor de strings SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.label                = Strings SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.alias.foreground.description     = Cor do alias de tabela SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.alias.foreground.label           = Alias de tabela SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.foreground.description           = Cor do nome de tabela SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.foreground.label                 = Nome de tabela SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.description            = Cor do plano de fundo do texto SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.label                  = Plano de fundo do texto SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.description            = Cor do texto SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.label                  = Texto SQL
colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.background.description          = Cor do plano de fundo da linha IndexScan
colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.background.label                = Plano de fundo da IndexScan
colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.foreground.description          = Cor da linha IndexScan
colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.foreground.label                = IndexScan
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.background.description          = Cor do plano de fundo da linha FullScan
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.background.label                = Plano de fundo da FullScan
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.foreground.description          = Cor da linha FullScan
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.foreground.label                = FullScan

column.org.jkiss.dbeaver.ui.editors.columns.script.position.name = Posição do script

command.org.jkiss.dbeaver.core.procedure.execute.description                   = Abrir novo console SQL com consulta de execução de stored procedures
command.org.jkiss.dbeaver.core.procedure.execute.name                          = Executar stored procedure
command.org.jkiss.dbeaver.core.sql.editor.console.description                  = Abrir novo console SQL. Nenhum arquivo de script será criado.
command.org.jkiss.dbeaver.core.sql.editor.console.name                         = Abrir console SQL
command.org.jkiss.dbeaver.core.sql.editor.create.description                   = Abre novo script SQL (cria um novo script)
command.org.jkiss.dbeaver.core.sql.editor.create.label                         = Criar script SQL
command.org.jkiss.dbeaver.core.sql.editor.create.name                          = Novo script SQL
command.org.jkiss.dbeaver.core.sql.editor.forSelection.description             = Abre um novo console SQL com consulta de leitura de dados
command.org.jkiss.dbeaver.core.sql.editor.forSelection.name                    = Ler dados no console do SQL
command.org.jkiss.dbeaver.core.sql.editor.open.default.description             = Comando padrão de abrir do editor SQL
command.org.jkiss.dbeaver.core.sql.editor.open.default.name                    = Comando padrão de abrir
command.org.jkiss.dbeaver.core.sql.editor.open.description                     = Abrir script SQL (existente ou novo)
command.org.jkiss.dbeaver.core.sql.editor.open.name                            = Abrir script SQL
command.org.jkiss.dbeaver.core.sql.editor.recent.description                   = Abre o último script SQL editado (para conexão ativa)
command.org.jkiss.dbeaver.core.sql.editor.recent.name                          = Script SQL recente
command.org.jkiss.dbeaver.core.sql.editor.showScripts.description              = Exibe scripts SQL existentes
command.org.jkiss.dbeaver.core.sql.editor.showScripts.name                     = Exibir scripts
command.org.jkiss.dbeaver.ui.editors.sql.CollapseAllFoldings.name              = Ocultar todos os agrupamentos
command.org.jkiss.dbeaver.ui.editors.sql.ExpandAllFoldings.name                = Expandir todos os agrupamentos
command.org.jkiss.dbeaver.ui.editors.sql.FoldingsEnabled.name                  = Agrupamentos habilitados
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description          = Auto-preenche nome de modelo
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name                 = Preencher nome de modelo
command.org.jkiss.dbeaver.ui.editors.sql.cancel.query.description              = Cancela a execução da consulta na aba ativa
command.org.jkiss.dbeaver.ui.editors.sql.cancel.query.name                     = Cancelar consulta ativa
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description                 = Fecha aba de resultados
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name                        = Fechar aba
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description             = Adiciona ou remove comentário de várias linhas
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name                    = Alternar comentário de bloco
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description            = Adiciona ou remove comentário de linha única
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name                   = Alternar comentário de linha
command.org.jkiss.dbeaver.ui.editors.sql.copy.query.description                = Efetua uma cópia da consulta sob o cursor no editor SQL
command.org.jkiss.dbeaver.ui.editors.sql.copy.query.name                       = Copiar consulta selecionada
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.description          = Exclui arquivo de script atual
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.name                 = Excluir este script
command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.description    = Desabilita auto-preenchimento, agrupamento, destaque de ocorrências de palavras\n(Se aplica automaticamente para arquivos de script extensos)
command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.name           = Desabilitar análise de sintaxe SQL
command.org.jkiss.dbeaver.ui.editors.sql.export.data.description               = Exportar dados retornados pela consulta atual
command.org.jkiss.dbeaver.ui.editors.sql.export.data.name                      = Exportar da consulta
command.org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet.description = Gera DDL com base no resultado
command.org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet.name        = DDL
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.description       = Posiciona o cursor no colchete correspondente
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.name              = Ir para o colchete correspondente
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.description                 = Carrega plano de execução do arquivo
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.name                        = Carregar plano de execução
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description     = Maximiza/normaliza painel de resultados
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name            = Maximizar painel resultados
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description      = Transforma em lista delimitada
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name             = Transformar em lista delimitada
command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.description     = Mostra cada conjunto de resultados em uma aba separada ou em uma aba rolável
command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.name            = Exibir resultados em uma ou múltiplas abas
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description           = Abre edição do objeto do banco de dados atual (destacado)
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name                  = Abrir declaração
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description                 = Carregar script SQL do sistema de arquivos
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name                        = Importar script SQL
command.org.jkiss.dbeaver.ui.editors.sql.query.next.description                = Mudar para a consulta seguinte
command.org.jkiss.dbeaver.ui.editors.sql.query.next.name                       = Consulta seguinte
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description                = Mudar para a consulta anterior
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name                       = Consulta anterior
command.org.jkiss.dbeaver.ui.editors.sql.rename.description                    = Renomear script SQL atual
command.org.jkiss.dbeaver.ui.editors.sql.rename.name                           = Renomear script SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description              = Seleciona e exibe todas as linhas (sem limite de tamanho de busca)
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name                     = Selecionar todas as linhas
command.org.jkiss.dbeaver.ui.editors.sql.run.count.description                 = Seleciona o número de linhas para consulta sob o cursor
command.org.jkiss.dbeaver.ui.editors.sql.run.count.name                        = Selecionar número de linhas
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description               = Explica o plano de execução
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name                      = Explicar o plano de execução
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description            = Seleciona valor da expressão SQL selecionada
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name                   = Avaliar expressão SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description                = Executa script
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name                       = Executar script SQL 
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition.description    = Executa o script a partir da posição do cursor
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition.name           = Executar script SQL da posição
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description             = Executa as instruções do script em abas de resultados separados
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name                    = Executar instruções em abas separadas
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description             = Executa instrução SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name                    = Executar instrução SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description          = Executa instrução SQL em uma nova aba
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name                 = Executar SQL em uma nova aba
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description                 = Salva script no sistema de arquivos
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name                        = Exportar script SQL
command.org.jkiss.dbeaver.ui.editors.sql.search.web.description                = Pesquisa a seleção com Google
command.org.jkiss.dbeaver.ui.editors.sql.search.web.label                      = Pesquisar a seleção com Google
command.org.jkiss.dbeaver.ui.editors.sql.search.web.name                       = Pesquisar a seleção com Google
command.org.jkiss.dbeaver.ui.editors.sql.show.log.description                  = Exibe o log de execução
command.org.jkiss.dbeaver.ui.editors.sql.show.log.name                         = Mostrar log de execução
command.org.jkiss.dbeaver.ui.editors.sql.show.outline.description              = Mostra/oculta visualização de estrutura do script
command.org.jkiss.dbeaver.ui.editors.sql.show.outline.name                     = Mostrar estrutura
command.org.jkiss.dbeaver.ui.editors.sql.show.output.description               = Mostra o console de saída do servidor
command.org.jkiss.dbeaver.ui.editors.sql.show.output.name                      = Mostrar saída do servidor
command.org.jkiss.dbeaver.ui.editors.sql.show.variables.description            = Mostra variáveis SQL ativas
command.org.jkiss.dbeaver.ui.editors.sql.show.variables.name                   = Mostrar variáveis SQL
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description              = Alterna painel ativo do editor SQL
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name                     = Alternar painel ativo
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description                 = Sincronização automática da conexão ativa com a seleção do navegador do banco de dados
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name                        = Auto-sincronizar conexão com o navegador
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description           = Define conexão ativa a partir da seleção do navegador de banco de dados
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name                  = Definir conexão do navegador
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.description         = Exibe painéis nas abas de resultado em vez do editor SQL
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.name                = Exibir painéis nas abas de resultado
command.org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab.description         = Fixa/desafixa aba de resultados
command.org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab.name                = Fixar/desafixar
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description       = Mostra/oculta painel de resultados
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name              = Alternar painel de resultados
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.description              = Alterna a disposição do editor (horizontal/vertical)
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.name                     = Alternar disposição do editor
command.org.jkiss.dbeaver.ui.editors.sql.web.description                       = Pesquisa texto selecionado na web
command.org.jkiss.dbeaver.ui.editors.sql.web.name                              = Pesquisar na web
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description                 = Alterna quebra automática de linha do editor de texto
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name                        = Alternar quebra de linha
command.org.jkiss.dbeaver.ui.editors.text.content.format.description           = Formata texto
command.org.jkiss.dbeaver.ui.editors.text.content.format.name                  = Formatação de conteúdo

confirm.close_result_tabs.message   = Há "{0}" abas de resultado desafixadas. Deseja fechar essas abas antes de executar a nova consulta?
confirm.close_result_tabs.tip       = Alerta sobre fechamento de abas de resultado
confirm.close_result_tabs.title     = Fechar abas de resultado adicionais
confirm.close_running_query.message = Há "{0}" consultas SQL em execução neste editor. Tem certeza que deseja cancelá-las e fechar o editor?
confirm.close_running_query.tip     = Cancela o fechamento do editor se existirem consultas em execução no editor
confirm.close_running_query.title   = Cancelar consultas ativas
confirm.dangerous_sql.message       = Você está prestes a executar uma instrução {0} sem uma cláusula WHERE em "{1}".\nPossível perda de dados. Tem certeza?
confirm.dangerous_sql.tip           = Alerta sobre execução de consulta que não contém uma cláusula WHERE
confirm.dangerous_sql.title         = Execução de consulta perigosa
confirm.drop_sql.message            = Você está prestes a executar uma instrução DROP TABLE.\nPossível perda de dados. Tem certeza?
confirm.drop_sql.tip                = Alerta sobre execução de consulta com DROP TABLE
confirm.drop_sql.title              = Execução de consulta DROP TABLE
confirm.save_sql_console.message    = Deseja salvar este console SQL como um novo script?
confirm.save_sql_console.tip        = Confirmação ao tentar salvar o console SQL
confirm.save_sql_console.title      = Salvar console SQL como um novo script
confirm.sql.group.name              = Editor SQL
confirm.sql.toggleMessage           = Não me pergunte novamente

context.org.jkiss.dbeaver.ui.editors.sql.name        = Contexto do editor SQL
context.org.jkiss.dbeaver.ui.editors.sql.script.name = Contexto do editor de Script SQL 

databaseScriptProblem.name = Problema no script de banco de dados

editor.sql.name         = Editor SQL
editor.sql.results.name = Resultados SQL

extension-point.org.jkiss.dbeaver.sql.covertname        = Conversões de texto SQL
extension-point.org.jkiss.dbeaver.sql.editorAddIns.name = Add-ins do editor SQL

menu.database.sql.generate                                        = &Gerar SQL
menu.org.jkiss.dbeaver.core.connection.sqleditor                  = Editor SQL
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label     = Executar
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.extraPanels.label = Painéis
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label        = Arquivo
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label      = Disposição
menu.org.jkiss.dbeaver.ui.editors.sql.connection.separate.label   = Abrir conexão separada
menu.sqleditor                                                    = Editor &SQL
menu.sqleditor.open.default                                       = Comando padrão

org.jkiss.dbeaver.toolBarConfiguration.sqlEditor.side.bottom = Barra inferior do editor SQL
org.jkiss.dbeaver.toolBarConfiguration.sqlEditor.side.top    = Barra superior do editor SQL
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.description     = Remove espaços em branco antes e depois
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.name            = Remover espaços

page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.name        = Editor de código
page.org.jkiss.dbeaver.preferences.main.sql.completion.description = Configurações de preenchimento de código
page.org.jkiss.dbeaver.preferences.main.sql.completion.name        = Preenchimento de código
page.org.jkiss.dbeaver.preferences.main.sql.dialects.name          = Configurações de dialetos
page.org.jkiss.dbeaver.preferences.main.sql.format.description     = Configurações de formatação SQL
page.org.jkiss.dbeaver.preferences.main.sql.format.name            = Formatação SQL
page.org.jkiss.dbeaver.preferences.main.sql.resources.name         = Scripts
page.org.jkiss.dbeaver.preferences.main.sql.templates.name         = Modelos
page.org.jkiss.dbeaver.preferences.main.sqleditor.description      = Configurações do editor SQL
page.org.jkiss.dbeaver.preferences.main.sqleditor.name             = Editor SQL
page.org.jkiss.dbeaver.preferences.main.sqlexecute.description     = Configurações de processamento de SQL
page.org.jkiss.dbeaver.preferences.main.sqlexecute.name            = Processamento de SQL

pref.page.name.sql.codeeditor = Editor de código
pref.page.name.sql.completion = Preenchimento de código
pref.page.name.sql.dialects   = Dialetos
pref.page.name.sql.editor     = Editor SQL
pref.page.name.sql.execute    = Processamento de SQL
pref.page.name.sql.format     = Formatação SQL
pref.page.name.sql.resources  = Scripts
pref.page.name.sql.templates  = Modelos

scriptEditorPropertyPage.name = Comportamento do editor SQL

semanticProblem.name = Problema de erro semântico

sql.convert.c.description                           = Converte texto SQL em formato C/C++
sql.convert.delphi.description                      = Converte texto SQL em formato Delphi
sql.convert.html.description                        = Converte texto SQL em formato HTML
sql.convert.java.description                        = Converte texto SQL em formato Java
sql.convert.label.keep.formatting.discription       = Mantém a formatação original (espaços em branco)
sql.convert.label.keep.formatting.name              = Manter a formatação
sql.convert.label.line.delimiter.delphi.discription = Delimitador para linhas de código fonte. Geralmente #13#10 ou espaço
sql.convert.label.line.delimiter.discription        = Delimitador para linhas de código fonte. Geralmente \\n ou espaço
sql.convert.label.line.delimiter.name               = Delimitador de linha
sql.convert.label.use.string.builder.description    = Usa o construtor de expressão em vez de concatenação de expressão
sql.convert.label.use.string.builder.name           = Usar construtor de expressão
sql.convert.python.description                      = Converte texto SQL em formato Python
sql.convert.unformatted.text.description            = Converte texto SQL em texto puro não formatado de linha simples
sql.convert.unformatted.text.name                   = Texto sem formatação
sql.plan.view.simple.name                           = Simples
sql.plan.view.simple.tip                            = Apresentação simples do plano de execução

themeElementCategory.org.jkiss.dbeaver.sql.plan.view.description       = Cores para o plano de consultas
themeElementCategory.org.jkiss.dbeaver.sql.plan.view.label             = Plano de consultas
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description = Editor SQL
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label       = Editor SQL

view.sql.results.title = Dados
