category.sqleditor.description                = Команды редактора SQL
category.sqleditor.name                       = Редактор SQL

editor.sql.name           = Редактор SQL
menu.database.sql.generate = Генерация SQL
menu.org.jkiss.dbeaver.core.connection.sqleditor              = Редактор SQL

command.org.jkiss.dbeaver.core.sql.editor.create.description                = Открыть новый SQL редактор
command.org.jkiss.dbeaver.core.sql.editor.create.name                       = Новый редактор SQL
command.org.jkiss.dbeaver.core.sql.editor.open.description                  = Открыть скрипт SQL
command.org.jkiss.dbeaver.core.sql.editor.open.name                         = Открыть SQL скрипт
command.org.jkiss.dbeaver.core.sql.editor.recent.description                = Открыть последний использованный SQL скрипт
command.org.jkiss.dbeaver.core.sql.editor.recent.name                       = Открыть последний скрипт
command.org.jkiss.dbeaver.core.sql.script.associate.description             = Связать скрип(ы) с базой данных
command.org.jkiss.dbeaver.core.sql.script.associate.name                    = Связать с базой данных
command.org.jkiss.dbeaver.core.show.in.explorer.name                        = Открыть ресурс в проводнике
command.org.jkiss.dbeaver.core.show.in.explorer.description                 = Открывает системный проводник и подсвечивает выделенный ресурс
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description       = Автоматическое дополнение имени шаблона
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name              = Дополнить имя шаблона
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description              = Закрыть вкладку результатов
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name                     = Закрыть вкладку
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description          = Вставить/удалить многострочный комментарий
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name                 = Блочный комментарий
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description         = Вставить/удалить однострочный комментарий
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name                = Однострочный комментарий
command.org.jkiss.dbeaver.ui.editors.sql.export.data.description            = Экспорт данных из текущего SQL запроса
command.org.jkiss.dbeaver.ui.editors.sql.export.data.name                   = Экспорт данных из запроса
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description  = Увеличить/уменьшить панель результатов
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name         = Увеличить панель результатов
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description   = Превратить в строку с разделителями
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name          = Превратить выделенный текст в строку/строки с разделителями
command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.name         = Показать неколько результатов в одной вкладке
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description        = Открыть указанный в SQL редакторе объект БД
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name               = Редактировать объект БД
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description              = Загрузить из файла
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name                     = Загрузить SQL скрипт
command.org.jkiss.dbeaver.ui.editors.sql.query.next.description             = Перейти к следующщему запросу
command.org.jkiss.dbeaver.ui.editors.sql.query.next.name                    = Следующий запрос
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description             = Перейти к предыдущему запросу
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name                    = Предыдущий запрос
command.org.jkiss.dbeaver.ui.editors.sql.rename.description                 = Переименовать текущий SQL скрипт
command.org.jkiss.dbeaver.ui.editors.sql.rename.name                        = Переименовать скрипт
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description           = Выбрать и показать все значения (без ограничения по числу строк)
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.name              = Удалить текущий файл
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.description       = Удалить текущий файл скрипта SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name                  = Выбрать все строки
command.org.jkiss.dbeaver.ui.editors.sql.run.count.description              = Показать число строк для запроса под курсором
command.org.jkiss.dbeaver.ui.editors.sql.run.count.name                     = Показать число строк
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description            = Получить план выполнения
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name                   = Получить план выполнения
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.name						= Загрузить план
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.description				= Загрузить план выполнения из файла
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description         = Выполнить текущее SQL выражени и показать результат
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name                = Выполнить SQL выражение
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description             = Выполнить скрипт
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name                    = Выполнить SQL скрипт
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description          = Выполнить операторы SQL скрипта параллельно в отдельных закладках
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name                 = Одновременное выполнение запросов
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description          = Выполнить SQL запрос
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name                 = Выполнить SQL запрос
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description       = Выполнить SQL выражение в новой закладке
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name              = Выполнить новое SQL выражение
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description              = Сохранить в файл
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name                     = Сохранить SQL скрипт
command.org.jkiss.dbeaver.ui.editors.sql.show.log.description               = Показать журнал запросов
command.org.jkiss.dbeaver.ui.editors.sql.show.log.name                      = Лог выполнения
command.org.jkiss.dbeaver.ui.editors.sql.show.output.description            = Показать консоль сообщений сервера
command.org.jkiss.dbeaver.ui.editors.sql.show.output.name                   = Консоль сообщений
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description           = Переключить активную панель SQL редактора
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name                  = Переключить панель
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description              = Автоматически синхронизировать соединение SQL редактора с навигатором БД
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name                     = Синхронизировать соединение с навигатором
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description        = Установить активное соеденение из навигатора БД
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name               = Соединение из навигатора
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description    = Показать/скрыть панель результатов
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name           = Переключить панель результатов
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description              = Включить/выключить перенос строк в текстовом редакторе
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name                     = Вкл/выкл перенос строк
command.org.jkiss.dbeaver.ui.editors.text.content.format.description        = Отформатировать текст
command.org.jkiss.dbeaver.ui.editors.text.content.format.name               = Форматировать текст
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.name                           = Обрезать пробелы
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.description                    = Обрезать ведущие и конечные пробелы

menu.sqleditor                                                = Редактор SQL
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label = Выполнить
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label    = Файл
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label  = Расположение

sql.convert.unformatted.text.description                                    = Преобразовать текст SQL в неформатированный однострочный, обычный текст
sql.convert.unformatted.text.name                                           = Неформатированный текст
sql.convert.java.description                                                = Преобразовать текст SQL в формат Java
sql.convert.c.description                                                   = Преобразовать текст SQL в формат C/C++
sql.convert.delphi.description                                              = Преобразовать текст SQL в формат Delphi
sql.convert.html.description                                                = Преобразовать текст SQL в формат HTML
sql.convert.label.keep.formatting.name                                      = Сохранить форматирование
sql.convert.label.keep.formatting.discription                               = Сохраняет исходное форматирование (пробелы)
sql.convert.label.line.delimiter.name                                       = Разделитель строк
sql.convert.label.line.delimiter.discription                                = Разделитель строк исходного кода. Обычно \\n или пробел 
sql.convert.label.line.delimiter.delphi.discription                         = Разделитель строк исходного кода. Обычно \#13#10 или пробел 

sql.formatter.default.name = Стандартный
sql.formatter.default.tip = SQL форматирование по умолчанию.
sql.formatter.compact.name = Компактный
sql.formatter.compact.tip = Компактное форматирование. Аналогично стандартному, но с более компактным выходом.
sql.formatter.external.name = Внешний
sql.formatter.external.tip = Внешняя программа. Использует настраиваемый исполняемый файл командной строки для форматирования запросов SQL.

sql.plan.view.simple.name=Простой
sql.plan.view.simple.tip=Простое представление плана выполнения

page.org.jkiss.dbeaver.preferences.main.sqleditor.name = Редактор SQL
page.org.jkiss.dbeaver.preferences.main.sqleditor.description = Настройки SQL редактора
page.org.jkiss.dbeaver.preferences.main.sql.completion.name = Авто-дополнение
page.org.jkiss.dbeaver.preferences.main.sql.completion.description = Настройки авто-дополнения кода
page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.name = Редактор кода
page.org.jkiss.dbeaver.preferences.main.sql.format.name = Форматирование
page.org.jkiss.dbeaver.preferences.main.sql.format.description = Настройки SQL форматирования
page.org.jkiss.dbeaver.preferences.main.sqlexecute.name = Выполнение SQL
page.org.jkiss.dbeaver.preferences.main.sqlexecute.description = Настройки выполнения SQL
page.org.jkiss.dbeaver.preferences.main.sql.templates.name = Шаблоны

keyword.org.jkiss.dbeaver.pref.keyword.sql.format.label = formatter query keyword indent substatement delimiter workbench upper lower пробел ключевое регистр перевод строка скобка запятая разделитель запрос отступ абзац
keyword.org.jkiss.dbeaver.pref.keyword.sql.completion.label = assistant proposal upper lower replace duplicate hide omit object schema catalog insert space table column sort server help alias from search global stored procedure помощник активация слово замещение дубликат пробел название колонка таблица схема каталог сервер справка поиск процедура
keyword.org.jkiss.dbeaver.pref.keyword.sql.codeeditor.label = cursor word folding quotes keyword extract слово совпадение блоки кавычки скобки регистр ключевое код
keyword.org.jkiss.dbeaver.pref.keyword.sql.sqlexecute.label = execute beep query log statement timeout parameters anonymous DDL variables script commit autocommit error rollback ignore fetch resultset cursor delimiter соединение сигнал звук запрос лог сервер таймаут параметры метка префикс коммит скрипт строка ошибка обработка выборка курсор редактор разделитель
keyword.org.jkiss.dbeaver.pref.keyword.sql.sqleditor.label = connection query auto-save autosave schema results error output message соединение активация запрос сохранение вкладка ошибка

pref.page.name.sql.editor             = Редактор SQL
pref.page.name.sql.codeeditor         = Редактор кода
pref.page.name.sql.execute            = Выполнение SQL
pref.page.name.sql.format             = Форматирование
pref.page.name.sql.completion         = Авто-дополнение
pref.page.name.sql.resources          = Скрипты

resourceHandler.ui.editors.sql.scripts.name = SQL скрипты

confirm.sql.group.name = Редактор SQL
confirm.close_running_query.title = Отменить выполнение запроса
confirm.close_running_query.message = В редакторе запущено "{0}" SQL запросов. Всё равно закрыть редактор?
confirm.sql.toggleMessage = Больше не спрашивать

confirm.close_result_tabs.title = Закрыть лишние вкладки с результатами
confirm.close_result_tabs.message = Открытых вкладок с результатами запросов: "{0}". Закрыть их перед выполнением нового запроса?

confirm.dangerous_sql.message = Вы собираетесь выполнить запрос {0} без условия WHERE для "{1}".\nВозможна потеря данных. Вы уверены?
confirm.dangerous_sql.title = Подтверждение выполнения опасного запроса

confirm.drop_sql.message = Вы собираетесь выполнить запрос DROP TABLE, который удаляет таблицы.\nЭто может привести к потери данных. Вы уверены?
confirm.drop_sql.title = Выполнить запрос удаления таблиц
