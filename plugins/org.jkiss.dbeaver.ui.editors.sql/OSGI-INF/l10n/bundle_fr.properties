
category.sqleditor.description = Editeur SQL
category.sqleditor.name        = Editeur SQL

colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.description   = Couleur de texte des commandes en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.label         = Couleur de texte des commandes en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description   = Couleur de texte des commentaires en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label         = Couleur de texte des commentaires en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.description  = Couleur de texte des types de données SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.label        = Couleur de texte des types de données SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.description = Couleur de texte des délimiteurs d'instructions en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.label       = Couleur de texte des délimiteurs en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description  = Couleur de fond du SQL désactivé
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label        = Couleur de fond du SQL désactivé
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.description   = Couleur de texte des mots-clefs SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.label         = Couleur de texte des mots-clefs SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.description    = Couleur de texte des nombres en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.label          = Couleur de texte des nombres en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.description = Couleur de texte des paramètres en SQL (?, :param, etc)
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.label       = Couleur de texte des paramètres en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.description    = Couleur de texte des chaînes de caractères en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.label          = Couleur de texte des chaînes de caractères en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.description      = Couleur de fond du texte en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.label            = Couleur de fond du texte en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.description      = Couleur de texte du texte en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.label            = Couleur de texte du texte en SQL

column.org.jkiss.dbeaver.ui.editors.columns.script.position.name = Position du script

command.org.jkiss.dbeaver.core.procedure.execute.description               = Ouvrir un nouvelle console SQL avec une requête d'exécution de procédure stockée
command.org.jkiss.dbeaver.core.procedure.execute.name                      = Exécuter la procédure stockée
command.org.jkiss.dbeaver.core.sql.editor.create.description               = Ouvrir un nouvel script SQL (créer un nouveau script)
command.org.jkiss.dbeaver.core.sql.editor.create.label                     = Nouveau script SQL
command.org.jkiss.dbeaver.core.sql.editor.create.name                      = Nouveau script SQL
command.org.jkiss.dbeaver.core.sql.editor.forSelection.description         = Ouvrir une nouvelle console SQL en lecture seule de requête
command.org.jkiss.dbeaver.core.sql.editor.forSelection.name                = Lire les données dans un console SQL
command.org.jkiss.dbeaver.core.sql.editor.open.description                 = Ouvrir un éditeur SQL (existant ou nouveau)
command.org.jkiss.dbeaver.core.sql.editor.open.name                        = Script SQL
command.org.jkiss.dbeaver.core.sql.editor.recent.description               = Ouvrir le dernier script SQL
command.org.jkiss.dbeaver.core.sql.editor.recent.name                      = Script SQL récent
command.org.jkiss.dbeaver.core.sql.script.associate.description            = Associer les scripts sélectionnés avec la source de données
command.org.jkiss.dbeaver.core.sql.script.associate.name                   = Associer à une source de données
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description      = Auto-complétion du nom de modèle
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name             = Auto-complétion du nom de modèle
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description             = Fermer l'onglet de résultats
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name                    = Fermer l'onglet
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description         = Ajouter ou enlever un commentaire sur plusieurs lignes
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name                = Commentaire sur plusieurs lignes
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description        = Ajouter ou supprimer un commentaire sur une ligne
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name               = Commentaire sur une ligne
command.org.jkiss.dbeaver.ui.editors.sql.export.data.description           = Exporter les données de la requête en cours
command.org.jkiss.dbeaver.ui.editors.sql.export.data.name                  = Exporter depuis la requête
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description = Agrandir/normaliser le panneau de résultats
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name        = Agrandir le panneau de résultats
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description  = Morpher vers la liste délimitée
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name         = Morpher vers la liste délimitée
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description       = Ouvrir l'editeur de la base de données courante (surlignée)
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name              = Ouvrir la Déclaration
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description             = Charger depuis un fichier
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name                    = Charger un script SQL
command.org.jkiss.dbeaver.ui.editors.sql.query.next.description            = Passer à la requête suivante
command.org.jkiss.dbeaver.ui.editors.sql.query.next.name                   = Requête suivante
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description            = Passer à la requête précédente
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name                   = Requête précédente
command.org.jkiss.dbeaver.ui.editors.sql.rename.description                = Renommer le script SQL en cours
command.org.jkiss.dbeaver.ui.editors.sql.rename.name                       = Renommer le script SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description          = Sélectionner et afficher toutes les lignes (sans limite de taille de récupération)
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name                 = Sélectionner toutes les lignes
command.org.jkiss.dbeaver.ui.editors.sql.run.count.description             = Sélectionner le nombre de lignes pour la requête sous le curseur
command.org.jkiss.dbeaver.ui.editors.sql.run.count.name                    = Sélectionner le nombre de lignes
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description           = Expliquer le plan d'exécution
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name                  = Expliquer le plan d'exécution
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description        = Sélectionner la valeur de l'expression SQL sélectionnée
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name               = Evaluer l'expression SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description            = Exécuter le script
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name                   = Exécuter le script SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description         = Exécuter les instructions du script dans un onglet de résultat à part
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name                = Exécuter les instructions dans un onglet à part
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description         = Exécuter l'instruction SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name                = Exécuter l'instruction SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description      = Exécuter l'instruction SQL dans un nouvel onglet
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name             = Exécuter le SQL dans un nouvel onglet
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description             = Sauvegarder dans un fichier
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name                    = Sauvegarder le script SQL
command.org.jkiss.dbeaver.ui.editors.sql.show.log.description              = Afficher le journal d'exécution SQL
command.org.jkiss.dbeaver.ui.editors.sql.show.log.name                     = Afficher le journal d'exécution
command.org.jkiss.dbeaver.ui.editors.sql.show.output.description           = Afficher la console de sortie serveur
command.org.jkiss.dbeaver.ui.editors.sql.show.output.name                  = Afficher la sortie serveur
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description          = Changer de panneau d'éditeur SQL actif
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name                 = Changer de panneau actif
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description             = Synchroniser automatiquement la connexion active avec la sélection du navigateur de bases de données
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name                    = Synchroniser la connexion avec le navigateur
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description       = Définir la connexion active depuis la sélection du navigateur de bases de données
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name              = Définir la connexion depuis le navigateur
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description   = Afficher/masquer le panneau de résultats
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name          = Panneau de résultats
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description             = Césure de mot douce dans l'éditeur de texte
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name                    = Retour à la ligne automatique
command.org.jkiss.dbeaver.ui.editors.text.content.format.description       = Formater le texte
command.org.jkiss.dbeaver.ui.editors.text.content.format.name              = Format du contenu

confirm.close_running_query.message = Il y a "{0}" requêtes SQl en cours d'exécution dans l'éditeur. Etes-vous sur de les annuler et fermer l'éditeur ?
confirm.close_running_query.tip     = Annule la fermeture de l'éditeur si des requêtes sont en cours d'exécution dans l'éditeur
confirm.close_running_query.title   = Annuler les requêtes en cours
confirm.dangerous_sql.message       = Vous êtes sur le point d''exécuter {0} instructions sans clause WHERE sur ''{1}''.\nPerte possible de données. Etes-vous sûr(e) ?
confirm.dangerous_sql.title         = Confirmer l'exécution de requête dangereuse

confirm.sql.group.name              = Éditeur SQL
confirm.sql.toggleMessage           = Se souvenir de mon choix

context.org.jkiss.dbeaver.ui.editors.sql.name       = Contexte d'éditeur SQL
context.org.jkiss.dbeaver.ui.editors.sql.scriptname = Contexte de l'éditeur de scripts SQL

editor.sql.name = Editeur SQL

extension-point.org.jkiss.dbeaver.sql.covertname = Conversions textuelles SQL

menu.database.sql.generate                                    = Générer du SQL
menu.org.jkiss.dbeaver.core.connection.sqleditor              = Editeur SQL
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label = Exécuter
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label    = Fichier
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label  = Agencement
menu.sqleditor                                                = Editeur &SQL

page.org.jkiss.dbeaver.preferences.main.sql.completion.name    = Complétion SQL
page.org.jkiss.dbeaver.preferences.main.sql.format.name        = Formatage SQL
page.org.jkiss.dbeaver.preferences.main.sql.templates.name     = Modèles
page.org.jkiss.dbeaver.preferences.main.sqleditor.description  = Paramètres de l'éditeur SQL
page.org.jkiss.dbeaver.preferences.main.sqleditor.name         = Editeur SQL
page.org.jkiss.dbeaver.preferences.main.sqlexecute.description = Paramètres de traitement SQL
page.org.jkiss.dbeaver.preferences.main.sqlexecute.name        = Traitement SQL

pref.page.name.sql.completion = Autocomplétion SQL
pref.page.name.sql.editor     = Editeur SQL
pref.page.name.sql.execute    = Exécuter la requête SQL
pref.page.name.sql.format     = Formattage SQL
pref.page.name.sql.templates  = Modèles

sql.convert.c.description                        = Convertir du SQL au format C/C++
sql.convert.delphi.description                   = Convertir du SQL au format Delphi
sql.convert.html.description                     = Convertir du SQL au format HTML
sql.convert.java.description                     = Convertir du SQL au format Java
sql.convert.label.keep.formatting.discription    = Conserver le formatage original (espaces)
sql.convert.label.keep.formatting.name           = Conserver le formatage
sql.convert.label.line.delimiter.name            = Délimiteur de ligne
sql.convert.label.use.string.builder.description = Utiliser un StringBuilder au lieu d'une chaine concaténée
sql.convert.label.use.string.builder.name        = Utiliser un StringBuilder
sql.convert.python.description                   = Convertir du SQL au format Python
sql.convert.unformatted.text.description         = Convertit les textes SQL en text simple non formaté
sql.convert.unformatted.text.name                = Texte non formaté

themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description = Editeur SQL
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label       = Editeur SQL

view.sql.results.title = Données
