extension-point.org.jkiss.dbeaver.sql.covertname = Перетворення SQL-тексту
extension-point.org.jkiss.dbeaver.sql.editorAddIns.name = Додатки для редактора SQL

menu.database.sql.generate = &Створити SQL
menu.org.jkiss.dbeaver.core.connection.sqleditor = Редактор SQL
menu.sqleditor.open.default = Стандартна команда
view.sql.results.title = Дані

editor.sql.name = Редактор SQL
editor.sql.results.name = Результати SQL

category.sqleditor.name = Редактор SQL
category.sqleditor.description = Команди редактора SQL

context.org.jkiss.dbeaver.ui.editors.sql.name = Контекст редактора SQL
context.org.jkiss.dbeaver.ui.editors.sql.script.name = Контекст редактора SQL Script

sql.convert.unformatted.text.description = Конвертує SQL-текст у форматований однорядковий звичайний текст
sql.convert.unformatted.text.name = Неформатований текст

sql.convert.java.description = Конвертує SQL-текст у формат Java
sql.convert.python.description = Конвертує SQL-текст у формат Python
sql.convert.c.description = Конвертує SQL-текст у формат C/C++
sql.convert.delphi.description = Конвертує SQL-текст у формат Delphi
sql.convert.html.description = Конвертує SQL-текст у формат HTML
sql.convert.label.keep.formatting.name = Зберегти форматування
sql.convert.label.keep.formatting.discription = Зберігає оригінальне форматування (пробіли)
sql.convert.label.line.delimiter.name = Роздільник рядка
sql.convert.label.line.delimiter.discription = Роздільник для рядків вихідного коду. Зазвичай \\n або пробіл
sql.convert.label.line.delimiter.delphi.discription = Роздільник для рядків вихідного коду. Зазвичай #13#10 або пробіл
sql.convert.label.use.string.builder.name = Використовувати StringBuilder
sql.convert.label.use.string.builder.description = Використовує StringBuilder замість конкатенації рядків

sql.plan.view.simple.name = Простий
sql.plan.view.simple.tip = Простий вигляд плану виконання

column.org.jkiss.dbeaver.ui.editors.columns.script.position.name = Позиція сценарію

command.org.jkiss.dbeaver.ui.editors.text.content.format.name = Формат вмісту
command.org.jkiss.dbeaver.ui.editors.text.content.format.description = Форматувати текст
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.name = Змінити макет редактора
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.description = Змінити макет редактора (горизонтальний/вертикальний)
command.org.jkiss.dbeaver.ui.editors.sql.web.name = Пошук в Інтернеті
command.org.jkiss.dbeaver.ui.editors.sql.web.description = Шукати обраний текст в Інтернеті
command.org.jkiss.dbeaver.ui.editors.sql.copy.query.name = Копіювати обраний запит
command.org.jkiss.dbeaver.ui.editors.sql.copy.query.description = Копіювати запит під курсором в редакторі SQL
command.org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet.name = DDL
command.org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet.description = Генерувати DDL на основі результатів
command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.name = Вимкнути синтаксичний аналізатор SQL
command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.description = Вимикає автодоповнення, згортання, виділення слів\n(Автоматично застосовується для великих сценаріїв)

command.org.jkiss.dbeaver.core.sql.editor.create.label = Створити SQL-сценарій
command.org.jkiss.dbeaver.core.sql.editor.open.name = Відкрити SQL-сценарій
command.org.jkiss.dbeaver.core.sql.editor.open.description = Відкрити SQL-сценарій (існуючий або новий)
command.org.jkiss.dbeaver.core.sql.editor.recent.name = Останній SQL-сценарій
command.org.jkiss.dbeaver.core.sql.editor.recent.description = Відкрити останній SQL-сценарій (для активного з’єднання)
command.org.jkiss.dbeaver.core.sql.editor.create.name = Новий SQL-сценарій
command.org.jkiss.dbeaver.core.sql.editor.create.description = Відкрити новий SQL-сценарій (створює новий сценарій)
command.org.jkiss.dbeaver.core.sql.editor.console.name = Відкрити SQL-консоль
command.org.jkiss.dbeaver.core.sql.editor.console.description = Відкрити нову SQL-консоль. Файл сценарію не буде створено.
command.org.jkiss.dbeaver.core.sql.editor.open.default.name = Стандартна команда відкриття
command.org.jkiss.dbeaver.core.sql.editor.open.default.description = Стандартна команда відкриття редактора SQL
command.org.jkiss.dbeaver.core.sql.editor.forSelection.name = Зчитати дані в SQL-консолі
command.org.jkiss.dbeaver.core.sql.editor.forSelection.description = Відкрити нову SQL-консоль із запитом для читання даних
command.org.jkiss.dbeaver.core.procedure.execute.name = Виконати збережену процедуру
command.org.jkiss.dbeaver.core.procedure.execute.description = Відкрити нову SQL-консоль із запитом на виконання збереженої процедури

command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name = Виконати SQL-запит
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description = Виконати SQL-запит
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name = Виконати SQL у новій вкладці
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description = Виконати SQL-запит в новій вкладці
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition.name = Виконати SQL-сценарій з позиції
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition.description = Виконати сценарій з позиції курсора
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name = Виконати SQL-сценарій
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description = Виконати сценарій
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name = Виконати запити в окремих вкладках
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description = Виконати оператори сценарію в окремих вкладках результатів
command.org.jkiss.dbeaver.ui.editors.sql.run.count.name = Вибрати кількість рядків
command.org.jkiss.dbeaver.ui.editors.sql.run.count.description = Вибрати кількість рядків для запиту під курсором
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name = Вибрати всі рядки
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description = Вибрати і відобразити всі рядки (без обмеження розміру вибірки)
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name = Обчислити SQL-вираз
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description = Вибрати значення обраного SQL-виразу
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name = Пояснити план виконання
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description = Пояснити план виконання
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.name = Завантажити план виконання
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.description = Завантажити план виконання із файлу
command.org.jkiss.dbeaver.ui.editors.sql.query.next.name = Наступний запит
command.org.jkiss.dbeaver.ui.editors.sql.query.next.description = Перейти до наступного запиту
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name = Попередній запит
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description = Перейти до попереднього запиту
command.org.jkiss.dbeaver.ui.editors.sql.cancel.query.name = Скасувати активний запит
command.org.jkiss.dbeaver.ui.editors.sql.cancel.query.description = Скасувати виконання запиту в активній вкладці
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.name = Перейти до відповідної дужки
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.description = Помістити курсор на відповідну дужку
command.org.jkiss.dbeaver.ui.editors.sql.ExpandAllFoldings.name = Розгорнути всі згортки
command.org.jkiss.dbeaver.ui.editors.sql.CollapseAllFoldings.name = Згорнути всі згортки
command.org.jkiss.dbeaver.ui.editors.sql.FoldingsEnabled.name = Згортки увімкнено

command.org.jkiss.dbeaver.ui.editors.sql.show.output.name=Показати вивід сервера
command.org.jkiss.dbeaver.ui.editors.sql.show.output.description=Показати консоль виводу сервера
command.org.jkiss.dbeaver.ui.editors.sql.show.log.name=Показати журнал виконання
command.org.jkiss.dbeaver.ui.editors.sql.show.log.description=Показати журнал виконання SQL
command.org.jkiss.dbeaver.ui.editors.sql.show.variables.name=Показати змінні SQL
command.org.jkiss.dbeaver.ui.editors.sql.show.variables.description=Показати активні змінні SQL
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.name=Показати панелі вкладок результатів
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.description=Показати панелі вкладок результатів замість панелі редактора SQL
command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.name= Показати результати в одній/кількох вкладках
command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.description=Показати кожний набір результатів в окремій вкладці або на одній прокручуваній вкладці
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name=Перемкнути панель результатів
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description=Показати/приховати панель результатів
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name=Максимізувати панель результатів
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description=Максимізувати/нормалізувати панель результатів
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name=Перемкнути активну панель
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description=Перемкнути активну панель редактора SQL
command.org.jkiss.dbeaver.ui.editors.sql.export.data.name=Експорт з запиту
command.org.jkiss.dbeaver.ui.editors.sql.export.data.description=Експортувати дані, повернені поточним запитом
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name=Відкрити визначення
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description=Відкрити редактор поточного (виділеного) об’єкта бази даних
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name=Імпорт SQL-скрипта
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description=Завантажити SQL-скрипт з файлової системи
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name=Експорт SQL-скрипта
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description=Зберегти скрипт у файлову систему
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name=Морф до роздільного списку
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description=Морф до роздільного списку
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.name=Обрізати пробіли
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.description=Обрізати пробіли на кінці та початку
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name=Перемкнути рядковий коментар
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description=Додати чи видалити рядковий коментар
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name=Перемкнути блочний коментар
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description=Додати чи видалити багаторядковий коментар
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name=Перемкнути перенесення слова
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description=Перемкнути м’яке перенесення текстового редактора
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name=Завершити шаблон
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description=Автоматично завершити назву шаблону
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name=Встановити з’єднання з навігатора
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description=Встановити активне з’єднання з вибором навігатора баз даних
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name=Авто-синхронізація з’єднання з навігатора
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description=Автоматична синхронізація активного з’єднання з вибором навігатора баз даних
menu.org.jkiss.dbeaver.ui.editors.sql.connection.separate.label=Відкрити окреме з’єднання
command.org.jkiss.dbeaver.ui.editors.sql.rename.name=Перейменувати SQL-скрипт
command.org.jkiss.dbeaver.ui.editors.sql.rename.description=Перейменувати поточний SQL-скрипт
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.name=Видалити цей скрипт
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.description=Видалити поточний файл скрипта
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name=Закрити
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description=Закрити вкладку результатів
command.org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab.name=Закріпити/відкріпити
command.org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab.description=Закріпити/відкріпити вкладку результатів

command.org.jkiss.dbeaver.ui.editors.sql.search.web.label=Пошук обраного за допомогою Google
command.org.jkiss.dbeaver.ui.editors.sql.search.web.name=Пошук обраного за допомогою Google
command.org.jkiss.dbeaver.ui.editors.sql.search.web.description=Пошук обраного за допомогою Google

menu.sqleditor=&Редагування SQL
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label=Виконати
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label=Файл
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label=Макет
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.extraPanels.label=Панелі

themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label=Редагування SQL
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description=Редагування SQL

colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.label=Колір ключових слів SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.description=Колір ключових слів SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.label=Колір типів даних SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.description=Колір типів даних SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.label=Колір рядкових констант SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.description=Колір рядкових констант SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.label=Колір чисел SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.description=Колір чисел SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label=Колір коментарів SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description=Колір коментарів SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.label=Колір роздільника SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.description=Колір роздільника SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.label=Колір команд SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.description=Колір команд SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.label=Колір параметрів SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.description=Колір параметрів SQL (наприклад, ?, :param)
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.label=Колір тексту SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.description=Колір тексту SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.label=Фон тексту SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.description=Фон тексту SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label=Фон вимкненого тексту SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description=Фон вимкненого тексту SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.foreground.label=Колір назв таблиць SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.alias.foreground.label=Колір псевдонімів таблиць SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.foreground.label=Колір назв стовпців SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.derived.foreground.label=Колір похідних назв стовпців SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.schema.foreground.label=Колір назв схем SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.semanticError.foreground.label=Колір помилок семантики SQL

keyword.org.jkiss.dbeaver.pref.keyword.sql.format.label=форматер ключових слів запиту регістр табуляція індент аргумент роздільник робоча область верхній нижній змішаний рядок
keyword.org.jkiss.dbeaver.pref.keyword.sql.completion.label=активація асистент затримка набору пропозиція верхній нижній регістр замінити дублювати приховати короткий довгий пропустити об’єкт схема каталог вставка пробіл таблиця стовпець сортування сервер довідка псевдонім з пошуку глобальний збережений процедура
keyword.org.jkiss.dbeaver.pref.keyword.sql.codeeditor.label=курсор слово згортання кавички подвійні дужки конвертувати регістр ключових слів видобути джерело
keyword.org.jkiss.dbeaver.pref.keyword.sql.sqlexecute.label=недійсний виконати сигнал виконання запиту оновити активний очистити журнал вираз тайм-аут параметри анонімний позначити префікс DDL змінні сценарій фіксація автозавершення рядок обробки помилок відкликання ігнорувати вилучити набір результату курсор редактор роздільник
keyword.org.jkiss.dbeaver.pref.keyword.sql.sqleditor.label=підключення окремий активація запит автозбереження схема результати помилки орієнтація виведення віктор повідомлення

page.org.jkiss.dbeaver.preferences.main.sqleditor.name=Редагування SQL
page.org.jkiss.dbeaver.preferences.main.sqleditor.description=Налаштування редактора SQL
page.org.jkiss.dbeaver.preferences.main.sql.completion.name=Автодоповнення
page.org.jkiss.dbeaver.preferences.main.sql.completion.description=Налаштування автодоповнення
page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.name=Редактор коду
page.org.jkiss.dbeaver.preferences.main.sql.format.name=Форматування
page.org.jkiss.dbeaver.preferences.main.sql.format.description=Налаштування форматування SQL
page.org.jkiss.dbeaver.preferences.main.sql.dialects.name=Налаштування діалектів
page.org.jkiss.dbeaver.preferences.main.sql.resources.name=Сценарії
page.org.jkiss.dbeaver.preferences.main.sqlexecute.name=Виконання SQL
page.org.jkiss.dbeaver.preferences.main.sqlexecute.description=Налаштування виконання SQL
page.org.jkiss.dbeaver.preferences.main.sql.templates.name=Шаблони

pref.page.name.sql.editor = Редактор SQL
pref.page.name.sql.completion = Автодоповнення коду
pref.page.name.sql.codeeditor = Редактор коду
pref.page.name.sql.dialects = Діалекти
pref.page.name.sql.execute = Виконання SQL
pref.page.name.sql.format = Форматування
pref.page.name.sql.resources = Скрипти
pref.page.name.sql.templates = Шаблони

databaseScriptProblem.name = Проблема скрипту бази даних
scriptEditorPropertyPage.name = Поведінка редактора SQL

confirm.sql.group.name = Редактор SQL
confirm.close_running_query.title = Скасувати запущені запити
confirm.close_running_query.tip = Скасовує закриття редактора, якщо в ньому є запущені запити
confirm.close_running_query.message = У цьому редакторі запущено "{0}" запитів SQL. Ви впевнені, що хочете їх скасувати і закрити редактор?

confirm.sql.toggleMessage = Більше не запитувати

confirm.close_result_tabs.title = Закрити додаткові вкладки результатів
confirm.close_result_tabs.message = Є "{0}" неприкріплені вкладки результатів. Ви хочете закрити ці вкладки перед виконанням нового запиту?
confirm.close_result_tabs.tip = Попереджає про закриття відкритих вкладок результатів

confirm.dangerous_sql.message = Ви збираєтеся виконати {0} запит без умови WHERE на "{1}". \nМожлива втрата даних. Ви впевнені?
confirm.dangerous_sql.title = Виконати небезпечні запити
confirm.dangerous_sql.tip = Попереджає про виконання запиту, який не містить умови WHERE

confirm.drop_sql.message = Ви збираєтеся виконати DROP TABLE. \nМожлива втрата даних. Ви впевнені?
confirm.drop_sql.title = Виконати запити на видалення таблиць
confirm.drop_sql.tip = Попереджає про виконання запиту DROP TABLE

org.jkiss.dbeaver.toolBarConfiguration.sqlEditor.side.top = Верхня панель інструментів редактора SQL
org.jkiss.dbeaver.toolBarConfiguration.sqlEditor.side.bottom = Нижня панель інструментів редактора SQL