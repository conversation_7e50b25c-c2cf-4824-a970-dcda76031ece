extension-point.org.jkiss.dbeaver.sql.covertname =SQLテキスト変換

menu.database.sql.generate=SQLの生成(&G)
menu.org.jkiss.dbeaver.core.connection.sqleditor = SQLエディタ

view.sql.results.title=データ

editor.sql.name=SQLエディタ

category.sqleditor.name=SQLエディタ
category.sqleditor.description=SQLエディタコマンド

context.org.jkiss.dbeaver.ui.editors.sql.name =SQLエディタコンテキスト
context.org.jkiss.dbeaver.ui.editors.sql.script.name =SQLスクリプトエディタコンテキスト

sql.convert.unformatted.text.description =SQLテキストをフォーマットされていない単一行のプレーンテキストに変換します。
sql.convert.unformatted.text.name        =フォーマットされていないテキスト

column.org.jkiss.dbeaver.ui.editors.columns.script.position.name =スクリプトの位置

command.org.jkiss.dbeaver.ui.editors.text.content.format.name = コンテンツフォーマット
command.org.jkiss.dbeaver.ui.editors.text.content.format.description = テキストの書式設定
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.name = エディタのレイアウトを切り替え
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.description = エディタのレイアウトを切り替える (水平/垂直)

command.org.jkiss.dbeaver.core.sql.editor.create.label =SQLスクリプトの作成
command.org.jkiss.dbeaver.core.sql.editor.open.name=SQLエディタ
command.org.jkiss.dbeaver.core.sql.editor.open.description=SQLエディタを開く（既存または新規）
command.org.jkiss.dbeaver.core.sql.editor.recent.name=最近のSQLエディタ
command.org.jkiss.dbeaver.core.sql.editor.recent.description=最新のSQLスクリプトを開く
command.org.jkiss.dbeaver.core.sql.editor.create.name=新しいSQLエディタ
command.org.jkiss.dbeaver.core.sql.editor.create.description=新しいSQLエディタを開く（新しいスクリプトを作成する）
command.org.jkiss.dbeaver.core.sql.editor.console.name=SQLコンソールを開く
command.org.jkiss.dbeaver.core.sql.editor.console.description=新しいSQLコンソールを開きます。スクリプトファイルは作成されません。
command.org.jkiss.dbeaver.core.sql.editor.forSelection.name=SQLコンソールでデータを読み込む
command.org.jkiss.dbeaver.core.sql.editor.forSelection.description=データ読み込みクエリで新しいSQLコンソールを開く
command.org.jkiss.dbeaver.core.procedure.execute.name=ストアドプロシージャを実行する
command.org.jkiss.dbeaver.core.procedure.execute.description=ストアドプロシージャのクエリを実行して新しいSQLコンソールを開く
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name=SQL文を実行する
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description=SQL文を実行する
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name=新しいタブでSQLを実行する
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description=新しいタブでSQL文を実行する
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name=SQLスクリプトを実行する
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description=スクリプトを実行する
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name=個別のタブでステートメントを実行する
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description=別の結果タブでスクリプトのステートメントを実行する
command.org.jkiss.dbeaver.ui.editors.sql.run.count.name=行数の選択
command.org.jkiss.dbeaver.ui.editors.sql.run.count.description=カーソル下のクエリの行数を選択
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name=すべての行を選択
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description=すべての行を選択して表示する（フェッチサイズ制限なし）
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name=SQL式の評価
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description=選択したSQL式の値の選択
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name=実行計画を説明する
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description=実行計画を説明する
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.name=実行計画を読み込む
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.description=ファイルから実行計画を読み込む
command.org.jkiss.dbeaver.ui.editors.sql.query.next.name=次のクエリ
command.org.jkiss.dbeaver.ui.editors.sql.query.next.description=次のクエリに切り替えます
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name=以前のクエリ
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description=前のクエリに切り替える

command.org.jkiss.dbeaver.ui.editors.sql.show.output.name=サーバーの出力を表示する
command.org.jkiss.dbeaver.ui.editors.sql.show.output.description=サーバーの出力コンソールを表示する
command.org.jkiss.dbeaver.ui.editors.sql.show.log.name=実行ログを表示する
command.org.jkiss.dbeaver.ui.editors.sql.show.log.description=SQL実行ログを表示する
command.org.jkiss.dbeaver.ui.editors.sql.show.variables.name=SQL変数を表示
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.name=結果タブにパネルを表示する
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.description=SQLエディタパネルではなく結果タブにパネルを表示します

command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name=結果パネルの切り替え
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description=結果パネルの表示/非表示
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name=結果パネルを最大化する
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description=結果パネルを最大化/正規化する
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name=アクティブパネルを切り替える
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description=アクティブなSQLエディタパネルを切り替える
command.org.jkiss.dbeaver.ui.editors.sql.export.data.name=クエリからエクスポート
command.org.jkiss.dbeaver.ui.editors.sql.export.data.description=現在のクエリによって返されたデータをエクスポートする
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name=公開宣言
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description=現在の（強調表示されている）データベースオブジェクトのオープンエディタ
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name=SQLスクリプトをロードする
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description=ファイルからロードする
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name=SQLスクリプトを保存する
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description=ファイルに保存
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name=区切りリストに変換
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description=区切りリストに変換
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.name=空白をトリムする
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.description=前後の空白をトリムします
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name=行コメントの切り替え
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description=一行コメントを追加または削除する
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name=ブロックコメントの切り替え
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description=複数行のコメントを追加または削除する
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name=ワードラップの切り替え
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description=テキストエディタのソフトワードラッピングを切り替えます。
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name=完全なテンプレート名
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description=テンプレート名の自動完成
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name=ナビゲータから接続を設定する
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description=データベースナビゲータ選択からアクティブな接続を設定する
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name=ナビゲータとの自動同期接続
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description=データベースナビゲータ選択とのアクティブな接続の自動同期
command.org.jkiss.dbeaver.ui.editors.sql.rename.name=SQLスクリプトの名前を変更する
command.org.jkiss.dbeaver.ui.editors.sql.rename.description=現在のSQLスクリプトの名前を変更する
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.name=このスクリプトを削除する
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.description=現在のスクリプトファイルを削除する
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name=タブを閉じる
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description=結果タブを閉じる

command.org.jkiss.dbeaver.ui.editors.sql.search.web.label=選択個所をGoogleで検索する
command.org.jkiss.dbeaver.ui.editors.sql.search.web.name=選択個所をGoogleで検索します
command.org.jkiss.dbeaver.ui.editors.sql.search.web.description=選択個所をGoogleで検索します

menu.sqleditor=SQLエディタ
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label =実行する
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label =ファイル
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label =レイアウト
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.extraPanels.label = パネル

themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label =SQLエディタ
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description =SQLエディタ

colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.label =SQLキーワードの色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.description =SQLキーワードの色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.label =SQLデータ型の色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.description =SQLデータ型の色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.label =SQL文字列の色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.description =SQL文字列の色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.label =SQL番号の色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.description =SQL番号の色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label =SQLコメントの色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description =SQLコメントの色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.label =SQLデリミタのフォアグラウンド
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.description =SQL文の区切り文字の前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.label =SQLコマンドフォアグラウンド
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.description =制御コマンドフォアグラウンド
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.label =SQLパラメータフォアグラウンド
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.description =SQLパラメータ（？、：paramなど）のフォアグラウンド
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.label =SQLテキストフォアグラウンド
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.description =SQLテキストフォアグラウンド
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.label =SQLテキストの背景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.description =SQLテキストの背景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label =SQLが無効になっている背景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description =SQLテキストの背景

page.org.jkiss.dbeaver.preferences.main.sqleditor.name = SQLエディタ
page.org.jkiss.dbeaver.preferences.main.sqleditor.description =SQLエディタの設定
page.org.jkiss.dbeaver.preferences.main.sql.completion.name = SQLの完了/折りたたみ
page.org.jkiss.dbeaver.preferences.main.sql.completion.description = コード補完設定
page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.name = コードエディタ
page.org.jkiss.dbeaver.preferences.main.sql.format.name = SQL書式設定
page.org.jkiss.dbeaver.preferences.main.sql.format.description = SQLフォーマッターの設定
page.org.jkiss.dbeaver.preferences.main.sql.dialects.name = SQLダイアレクトの設定
page.org.jkiss.dbeaver.preferences.main.sql.resources.name = スクリプト
page.org.jkiss.dbeaver.preferences.main.sqlexecute.name = SQL処理
page.org.jkiss.dbeaver.preferences.main.sqlexecute.description =SQL処理設定
page.org.jkiss.dbeaver.preferences.main.sql.templates.name = テンプレート
pref.page.name.sql.completion         = SQL補完
pref.page.name.sql.codeeditor         = コードエディタ
pref.page.name.sql.editor             = SQLエディタ
pref.page.name.sql.dialects           = SQLダイアレクト
pref.page.name.sql.execute            = SQL処理
pref.page.name.sql.format             = SQL書式設定
pref.page.name.sql.resources          = スクリプト
pref.page.name.sql.templates          = テンプレート

confirm.sql.group.name = SQLエディタ
confirm.close_running_query.title = 実行中のクエリをキャンセルする
confirm.sql.toggleMessage = 次回から表示しない

confirm.close_result_tabs.title = 結果タブを閉じる

confirm.dangerous_sql.title = 危険なクエリの実行を確認する
