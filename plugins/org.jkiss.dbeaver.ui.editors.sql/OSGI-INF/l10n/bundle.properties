extension-point.org.jkiss.dbeaver.sql.covertname = SQL text conversions
extension-point.org.jkiss.dbeaver.sql.editorAddIns.name = SQL Editor add-ins

menu.database.sql.generate = &Generate SQL
menu.org.jkiss.dbeaver.core.connection.sqleditor = SQL Editor
menu.sqleditor.open.default = Default command
view.sql.results.title=Data

editor.sql.name=SQL Editor
editor.sql.results.name=SQL Results

category.sqleditor.name=SQL Editor
category.sqleditor.description=SQL Editor Commands

context.org.jkiss.dbeaver.ui.editors.sql.name = SQL Editor Context
context.org.jkiss.dbeaver.ui.editors.sql.script.name = SQL Script Editor Context

sql.convert.unformatted.text.description = Converts SQL text into unformatted single line plain text
sql.convert.unformatted.text.name        = Unformatted text

sql.convert.java.description = Convert SQL text to Java format
sql.convert.python.description = Convert SQL text to Python format
sql.convert.c.description = Convert SQL text to C/C++ format
sql.convert.delphi.description = Convert SQL text to Delphi format
sql.convert.html.description = Convert SQL text to HTML format
sql.convert.label.keep.formatting.name = Keep formatting
sql.convert.label.keep.formatting.discription = Keeps original formatting (whitespaces)
sql.convert.label.line.delimiter.name = Line delimiter
sql.convert.label.line.delimiter.discription = Delimiter for source code lines. Usually \\n or space
sql.convert.label.line.delimiter.delphi.discription = Delimiter for source code lines. Usually #13#10 or space
sql.convert.label.use.string.builder.name = Use StringBuilder
sql.convert.label.use.string.builder.description = Uses StringBuilder instead of String concatenation

sql.plan.view.simple.name=Simple
sql.plan.view.simple.tip=Simple execution plan presentation

column.org.jkiss.dbeaver.ui.editors.columns.script.position.name = Script position

command.org.jkiss.dbeaver.ui.editors.text.content.format.name=Content Format
command.org.jkiss.dbeaver.ui.editors.text.content.format.description=Format text
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.name=Toggle editor layout
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.description=Toggle editor layout (horizontal/vertical)
command.org.jkiss.dbeaver.ui.editors.sql.web.name=Search in web
command.org.jkiss.dbeaver.ui.editors.sql.web.description=Search selected text in web
command.org.jkiss.dbeaver.ui.editors.sql.copy.query.name=Copy selected query
command.org.jkiss.dbeaver.ui.editors.sql.copy.query.description=Performs a copy of the query under the cursor in the SQL editor
command.org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet.name=DDL
command.org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet.description=Generate result set based DDL
command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.name = Disable SQL syntax parser
command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.description = Disable autocomplete, folding, word occurrences highlighting\n(Automatically applied for large script files)

command.org.jkiss.dbeaver.core.sql.editor.create.label = Create SQL script
command.org.jkiss.dbeaver.core.sql.editor.open.name=Open SQL script
command.org.jkiss.dbeaver.core.sql.editor.open.description=Open SQL script (existing or new)
command.org.jkiss.dbeaver.core.sql.editor.recent.name=Last edited SQL script
command.org.jkiss.dbeaver.core.sql.editor.recent.description=Open last edited SQL script (for active connection)
command.org.jkiss.dbeaver.core.sql.editor.create.name=New SQL script
command.org.jkiss.dbeaver.core.sql.editor.create.description=Open new SQL script (creates new script)
command.org.jkiss.dbeaver.core.sql.editor.console.name=Open SQL console
command.org.jkiss.dbeaver.core.sql.editor.console.description=Open new SQL console. No script file will be created.
command.org.jkiss.dbeaver.core.sql.editor.showScripts.name=Show scripts
command.org.jkiss.dbeaver.core.sql.editor.showScripts.description=Show existing SQL scripts
command.org.jkiss.dbeaver.core.sql.editor.open.default.name=Default open command
command.org.jkiss.dbeaver.core.sql.editor.open.default.description=Default SQL editor open command
command.org.jkiss.dbeaver.core.sql.editor.forSelection.name=Read data in SQL console
command.org.jkiss.dbeaver.core.sql.editor.forSelection.description=Open new SQL console with data read query
command.org.jkiss.dbeaver.core.procedure.execute.name=Execute Stored Procedure
command.org.jkiss.dbeaver.core.procedure.execute.description=Open new SQL console with execute stored procedure query

command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name=Execute SQL query
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description=Execute SQL statement
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name=Execute SQL in new tab
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description=Execute SQL query in a new tab
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition.name=Execute SQL script from the position
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition.description=Execute script from the cursor position
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name=Execute SQL script
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description=Execute script
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name=Execute queries in separate tabs
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description=Execute script's statements in separate results tabs
command.org.jkiss.dbeaver.ui.editors.sql.run.count.name=Select row count
command.org.jkiss.dbeaver.ui.editors.sql.run.count.description=Select row count for query under cursor
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name=Select all rows
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description=Select and show all rows (no fetch size limit)
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name=Evaluate SQL expression
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description=Select value of selected SQL expression
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name=Explain Execution Plan
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description=Explain execution plan
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.name=Load Execution Plan
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.description=Load execution plan from file
command.org.jkiss.dbeaver.ui.editors.sql.query.next.name=Next query
command.org.jkiss.dbeaver.ui.editors.sql.query.next.description=Switch to the next query
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name=Previous query
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description=Switch to the previous query
command.org.jkiss.dbeaver.ui.editors.sql.cancel.query.name=Cancel active query
command.org.jkiss.dbeaver.ui.editors.sql.cancel.query.description=Cancels execution of the query in the active tab
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.name=Go to matching bracket
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.description=Position cursor on the matching bracket
command.org.jkiss.dbeaver.ui.editors.sql.selectToMatchingBracket.name=Select to the matching bracket
command.org.jkiss.dbeaver.ui.editors.sql.selectToMatchingBracket.description=Select text from current bracket to the matching bracket
command.org.jkiss.dbeaver.ui.editors.sql.ExpandAllFoldings.name = Expand All Foldings
command.org.jkiss.dbeaver.ui.editors.sql.CollapseAllFoldings.name = Collapse All Foldings
command.org.jkiss.dbeaver.ui.editors.sql.FoldingsEnabled.name = Foldings Enabled

command.org.jkiss.dbeaver.ui.editors.sql.show.output.name=Show server output
command.org.jkiss.dbeaver.ui.editors.sql.show.output.description=Show server output console
command.org.jkiss.dbeaver.ui.editors.sql.show.outline.name=Toggle outline
command.org.jkiss.dbeaver.ui.editors.sql.show.outline.description=Show/hide script outline view
command.org.jkiss.dbeaver.ui.editors.sql.show.log.name=Show execution log
command.org.jkiss.dbeaver.ui.editors.sql.show.log.description=Show SQL execution log
command.org.jkiss.dbeaver.ui.editors.sql.show.variables.name=Show SQL variables
command.org.jkiss.dbeaver.ui.editors.sql.show.variables.description=Show active SQL variables
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.name=Show panels in result tabs
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.description=Show panels in result tabs instead of SQL editor pane
command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.name= Show multiple results in a single tab
command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.description=Show each result set in a separate tab or on one scrollable tab
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name=Toggle results panel
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description=Show/hide results panel
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name=Maximize results panel
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description=Maximize/normalize results panel
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name=Switch active panel
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description=Switch active SQL editor panel
command.org.jkiss.dbeaver.ui.editors.sql.export.data.name=Export from Query
command.org.jkiss.dbeaver.ui.editors.sql.export.data.description=Export data returned by current query
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name=Open Declaration
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description=Open editor of current (highlighted) database object
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name=Import SQL script
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description=Load SQL script from file system
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name=Export SQL script
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description=Save script to file system
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name=Morph to delimited list
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description=Morph to delimited list
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.name=Trim spaces
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.description=Trim trailing and leading spaces
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name=Toggle Line Comment
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description=Add or remove single line comment
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name=Toggle Block Comment
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description=Add or remove multi line comment
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name=Toggle Word Wrap
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description=Toggles text editor soft word wrap
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name=Complete template name
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description=Auto-complete template name
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name=Set connection from navigator
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description=Set active connection from database navigator selection
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name=Auto-sync connection with navigator
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description=Auto-sync active connection with database navigator selection
menu.org.jkiss.dbeaver.ui.editors.sql.connection.separate.label=Open separate connection
command.org.jkiss.dbeaver.ui.editors.sql.rename.name=Rename SQL Script
command.org.jkiss.dbeaver.ui.editors.sql.rename.description=Rename current SQL script
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.name=Delete this script
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.description=Delete current script file
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name=Close
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description=Close results tab
command.org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab.name=Pin/unpin
command.org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab.description=Pin/unpin results tab

command.org.jkiss.dbeaver.ui.editors.sql.search.web.label =Search selected with Google
command.org.jkiss.dbeaver.ui.editors.sql.search.web.name=Search selected with Google
command.org.jkiss.dbeaver.ui.editors.sql.search.web.description=Search selected with Google

command.org.jkiss.dbeaver.ui.editors.sql.refresh.current.schema.name=Refresh current schema
command.org.jkiss.dbeaver.ui.editors.sql.refresh.current.schema.description=Refresh current schema from database connection

command.org.jkiss.dbeaver.ui.editors.sql.refresh.all.schemas.name=Refresh all schemas
command.org.jkiss.dbeaver.ui.editors.sql.refresh.all.schemas.description=Refresh all schemas in the active catalog

menu.sqleditor=&SQL Editor
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label = Execute
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label = File
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label = Layout
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.extraPanels.label = Panels
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.context.label = Context

themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label = SQL Editor
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description = SQL Editor

colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.label = SQL keyword color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.description = SQL keyword color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.label = SQL datatype color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.description = SQL datatype color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.function.foreground.label = SQL function color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.function.foreground.description = SQL function color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.label = SQL string color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.description = SQL string color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.label = SQL number color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.description = SQL number color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label = SQL comment color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description = SQL comment color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.label = SQL delimiter foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.description = SQL statement delimiter foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.label = SQL command foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.description = Control command foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.label = SQL parameter foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.description = SQL parameter (?, :param, etc) foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.label = SQL text foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.description = SQL text foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.label = SQL text background
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.description = SQL text background
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label = SQL disabled background
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description = SQL text background
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.foreground.label = SQL table name foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.foreground.description = SQL table name foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.alias.foreground.label = SQL table alias foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.alias.foreground.description = SQL table alias foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.foreground.label = SQL column name foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.foreground.description = SQL column name foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.derived.foreground.label = SQL derived column foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.derived.foreground.description = SQL column alias foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.schema.foreground.label = SQL schema name foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.schema.foreground.description = SQL schema name foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.semanticError.foreground.label = SQL semantic error foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.semanticError.foreground.description = SQL semantic error foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.sqlVariable.foreground.label = SQL variable name foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.sqlVariable.foreground.description = SQL variable name foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.composite.field.foreground.label = Composite type field foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.composite.field.foreground.description = Composite type field foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.aiSuggestion.foreground.label = AI suggestion foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.aiSuggestion.foreground.description = AI suggestion foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.aiSuggestion.background.label = AI suggestion background
colorDefinition.org.jkiss.dbeaver.sql.editor.color.aiSuggestion.background.description = AI suggestion background

themeElementCategory.org.jkiss.dbeaver.sql.plan.view.label = Query Plan
themeElementCategory.org.jkiss.dbeaver.sql.plan.view.description = Colors For Query Plan

colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.background.label = IndexScan Background
colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.background.description = IndexScan Row Background
colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.foreground.label = IndexScan foreground
colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.foreground.description = IndexScan Row Foreground
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.background.label = FullScan background
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.background.description = FullScan Row Background
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.foreground.label = FullScan foreground
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.foreground.description = FullScan Row Foreground

keyword.org.jkiss.dbeaver.pref.keyword.sql.format.label = formatter query keyword case tab space indent substatement comma delimiter workbench upper lower mixed line
keyword.org.jkiss.dbeaver.pref.keyword.sql.completion.label = assistant activation Hippie delay typing proposal upper lower case replace duplicate hide short long omit object schema catalog insert space table column sort server help alias from search global stored procedure completion autocompletion qualified
keyword.org.jkiss.dbeaver.pref.keyword.sql.codeeditor.label = cursor word folding quotes double brackets convert keyword case extract source
keyword.org.jkiss.dbeaver.pref.keyword.sql.sqlexecute.label = invalidate execute beep sound query refresh active clear log statement timeout parameters anonymous mark prefix DDL variables script commit autocommit line error handling rollback ignore fetch resultset cursor editor delimiter
keyword.org.jkiss.dbeaver.pref.keyword.sql.sqleditor.label = connection separate activation query auto-save autosave schema results error orientation output viewer message

page.org.jkiss.dbeaver.preferences.main.sqleditor.name = SQL Editor
page.org.jkiss.dbeaver.preferences.main.sqleditor.description = SQL editor settings
page.org.jkiss.dbeaver.preferences.main.sql.completion.name = Code Completion
page.org.jkiss.dbeaver.preferences.main.sql.completion.description = Code Completion settings
page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.name = Code Editor
page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.description = SQL code eitor settings
page.org.jkiss.dbeaver.preferences.main.sql.format.name = Formatting
page.org.jkiss.dbeaver.preferences.main.sql.format.description = SQL Formatting settings
page.org.jkiss.dbeaver.preferences.main.sql.dialects.name = Dialect Settings
page.org.jkiss.dbeaver.preferences.main.sql.resources.name = Scripts
page.org.jkiss.dbeaver.preferences.main.sqlexecute.name = SQL Processing
page.org.jkiss.dbeaver.preferences.main.sqlexecute.description = SQL processing settings
page.org.jkiss.dbeaver.preferences.main.sql.templates.name = Templates

pref.page.name.sql.editor             = SQL Editor
pref.page.name.sql.completion         = Code Completion
pref.page.name.sql.codeeditor         = Code Editor
pref.page.name.sql.dialects           = Dialects
pref.page.name.sql.execute            = SQL Processing
pref.page.name.sql.format             = Formatting
pref.page.name.sql.resources          = Scripts
pref.page.name.sql.templates          = Templates

databaseScriptProblem.name=Database script problem
semanticProblem.name=Semantic error problem
scriptEditorPropertyPage.name = SQL Editor behavior

confirm.sql.group.name = SQL Editor
confirm.close_running_query.title = Cancel running queries
confirm.close_running_query.tip = Cancels the closure of the editor if there are running queries in the editor
confirm.close_running_query.message = There are "{0}" running SQL queries in this editor. Are you sure you want to cancel them and close the editor?
confirm.sql.toggleMessage = Do not ask me again

confirm.close_result_tabs.title = Close extra result tabs
confirm.close_result_tabs.message = There are "{0}" unpinned result tabs. Do you want to close these tabs before executing new query?
confirm.close_result_tabs.tip = Warns about opened result tabs closing

confirm.dangerous_sql.message = You are about to execute {0} statement without a WHERE clause on "{1}".\nPossible data loss. Are you sure?
confirm.dangerous_sql.title = Execute dangerous queries
confirm.dangerous_sql.tip = Warns about the execution of the query that does not contain a WHERE clause

confirm.drop_sql.message = You are about to execute DROP TABLE statement.\nPossible data loss. Are you sure?
confirm.drop_sql.title = Execute drop table queries
confirm.drop_sql.tip = Warns about the DROP TABLE query execution

confirm.save_sql_console.title = Save SQL console as a new script
confirm.save_sql_console.tip = Confirmation when trying to save the SQL console
confirm.save_sql_console.message = Do you want to save this SQL console as a new script?

org.jkiss.dbeaver.toolBarConfiguration.sqlEditor.side.top = SQL Editor top toolbar
org.jkiss.dbeaver.toolBarConfiguration.sqlEditor.side.bottom = SQL Editor bottom toolbar
