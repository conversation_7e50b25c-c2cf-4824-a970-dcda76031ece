category.sqleditor.description                        = SQL-Editor
category.sqleditor.name                               = SQL-Editor

editor.sql.name           = SQL-Editor

extension-point.org.jkiss.dbeaver.sql.covertname               = SQL-Textkonvertierungen

sql.convert.unformatted.text.description = Konvertiert SQL-Text in unformatierten einzeiligen Klartext
sql.convert.unformatted.text.name        = Unformatierter Text
view.sql.results.title        = Daten
menu.database.sql.generate                                    = SQL generieren
menu.org.jkiss.dbeaver.core.connection.sqleditor              = SQL-Editor

column.org.jkiss.dbeaver.ui.editors.columns.script.position.name = Skriptposition

command.org.jkiss.dbeaver.core.sql.editor.create.description                = Neues SQL-Skript öffnen (Neues Skript erstellen)
command.org.jkiss.dbeaver.core.sql.editor.create.label                      = SQL-Skript erstellen
command.org.jkiss.dbeaver.core.sql.editor.create.name                       = Neues SQL-Skript
command.org.jkiss.dbeaver.core.sql.editor.forSelection.description          = Öffnen einer neuen SQL-Konsole als schreibgeschützte Abfrage
command.org.jkiss.dbeaver.core.sql.editor.forSelection.name                 = Daten in SQL-Konsole abfragen
command.org.jkiss.dbeaver.core.procedure.execute.description                = Öffnen Sie eine neue SQL-Konsole mit der Ausführung einer Stored Procedure Abfrage.
command.org.jkiss.dbeaver.core.procedure.execute.name                       = Ausführung Stored Procedure
command.org.jkiss.dbeaver.core.sql.editor.open.description                  = SQL-Editor öffnen (bestehenden oder neuen)
command.org.jkiss.dbeaver.core.sql.editor.open.name                         = SQL-Skript
command.org.jkiss.dbeaver.core.sql.editor.recent.description                = Zuletzt geöffnetes SQL-Skript öffnen
command.org.jkiss.dbeaver.core.sql.editor.recent.name                       = Letztes SQL-Skript
command.org.jkiss.dbeaver.core.sql.script.associate.description             = Ausgewählte(s) Skript(e) mit der Datenquelle verknüpfen
command.org.jkiss.dbeaver.core.sql.script.associate.name                    = Mit Datenquelle verknüpfen
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description       = Auto-Complete Vorlagenname
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name              = Auto-Complete Vorlagenname
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description              = Ergebnistab schließen
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name                     = Tab schließen
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description          = Mehrzeilenkommentar hinzufügen oder entfernen
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name                 = Blockkommentar umschalten
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description         = Einzeilenkommentar hinzufügen oder entfernen
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name                = Kommentarzeile ein-/ausblenden
command.org.jkiss.dbeaver.ui.editors.sql.export.data.description            = Abfragedaten der aktuellen Abfrage exportieren
command.org.jkiss.dbeaver.ui.editors.sql.export.data.name                   = Abfragedaten exportieren
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description  = Maximieren/normalisieren der Ergebnisanzeige
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name         = Ergebnisanzeige maximieren
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description   = Umwandlung in eine abgegrenzte Liste
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name          = Umwandlung in eine abgegrenzte Liste
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description        = Editor des aktuellen (markierten) Datenbankobjekts öffnen
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name               = Definition öffnen
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description              = Aus Datei laden
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name                     = SQL-Skript laden
command.org.jkiss.dbeaver.ui.editors.sql.query.next.description             = Zur nächsten Abfrage springen
command.org.jkiss.dbeaver.ui.editors.sql.query.next.name                    = Nächste Abfrage
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description             = Zur vorherigen Abfrage springen
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name                    = Vorherige Abfrage
command.org.jkiss.dbeaver.ui.editors.sql.rename.description                 = Aktuelles SQL-Skript umbennen
command.org.jkiss.dbeaver.ui.editors.sql.rename.name                        = SQL-Skript umbennen
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description           = Auswahl und Anzeige aller Zeilen (ohne Größenbegrenzung)
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name                  = Alle Zeilen auswählen
command.org.jkiss.dbeaver.ui.editors.sql.run.count.description              = Zeilenanzahl für Abfrage unterhalb des Cursors abfragen
command.org.jkiss.dbeaver.ui.editors.sql.run.count.name                     = Zeilenanzahl abfragen
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description            = Erklärung des Umsetzungsplans
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name                   = Erklärung des Umsetzungsplans
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description         = Wert des markierten SQL-Ausdrucks auswählen
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name                = SQL-Ausdruck auswerten
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description             = Skript ausführen
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name                    = SQL-Skript ausführen
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description          = Ausführen der Skriptanweisungen in einer separaten Ergebnisregisterkarte
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name                 = Statements mit separaten Ergebnis-Tabs ausführen
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description          = SQL-Statement ausführen
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name                 = SQL-Statement ausführen
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description       = SQL-Statement in einer neuen Registerkarte ausführen
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name              = SQL-Statement in neu Tab ausführen
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description              = In Datei speichern
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name                     = SQL-Skript speichern
command.org.jkiss.dbeaver.ui.editors.sql.show.log.description               = SQL-Ausführungsprotokoll anzeigen
command.org.jkiss.dbeaver.ui.editors.sql.show.log.name                      = Ausführungsprotokoll anzeigen
command.org.jkiss.dbeaver.ui.editors.sql.show.output.description            = Serverausgabekonsole anzeigen
command.org.jkiss.dbeaver.ui.editors.sql.show.output.name                   = Serverausgabe anzeigen
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description           = Ändern des aktiven SQL-Editor-Panels
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name                  = Aktuelles Panel ändern
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description              = Aktive Verbindung automatisch mit Datenbanknavigator synchronisieren\r\n
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name                     = Verbindung automatisch mit Navigator synchronisieren
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description        = Aktuelle Verbindung auf Auswahl des Datenbankavigators setzen
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name               = Verbindung vom Navigator setzen
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description    = Ergebnisanzeige ein-/ausblenden
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name           = Ergebnisanzeige ein-/ausblenden
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description              = Zeilenumbruchen ein- bzw. ausschalten
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name                     = Zeilenumbruch umschalten
command.org.jkiss.dbeaver.ui.editors.text.content.format.description        = Text formatieren
command.org.jkiss.dbeaver.ui.editors.text.content.format.name               = Inhaltsformatierung

menu.sqleditor                                                = &SQL-Editor
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label = Ausführen
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label    = Datei
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label  = Layout

themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description     = SQL-Editor
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label           = SQL-Editor

colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.description            = Textfarbe Steuerbefehl
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.label                  = Textfarbe SQL-Befehl
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description            = SQL-Kommentarfarbe
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label                  = SQL-Kommentarfarbe
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.description           = SQL-Datentypfarbe
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.label                 = SQL-Datentypfarbe
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.description          = Textfarbe SQL-Statementtrennzeichen
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.label                = Textfarbe SQL-Trennzeichen
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description           = Hintergrundfarbe SQL-Text
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label                 = Hintergrundfarbe deaktivierte SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.description            = SQL-Schlüsselwortfarbe
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.label                  = SQL-Schlüsselwortfarbe
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.description             = SQL-Zahlenfarbe
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.label                   = SQL-Zahlenfarbe
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.description          = Vordergrundfarbe SQL-Parameter (?, :param, etc)
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.label                = Textfarbe SQL-Parameter
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.description             = Farbe SQL-String
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.label                   = Farbe SQL-String
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.description               = SQL-Hintergrundtextfarbe
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.label                     = SQL-Hintergrundtextfarbe
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.description               = SQL-Textfarbe
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.label                     = SQL-Textfarbe

page.org.jkiss.dbeaver.preferences.main.sql.completion.name         = SQL-Vervollständigung / Faltung
page.org.jkiss.dbeaver.preferences.main.sql.format.name             = SQL-Formatierung
page.org.jkiss.dbeaver.preferences.main.sql.resources.name          = Skripte
page.org.jkiss.dbeaver.preferences.main.sql.templates.name          = Vorlagen
page.org.jkiss.dbeaver.preferences.main.sqleditor.name              = SQL-Editor
page.org.jkiss.dbeaver.preferences.main.sqleditor.description = Einstellungen SQL-Editor
page.org.jkiss.dbeaver.preferences.main.sqlexecute.name             = SQL-Verarbeitung
page.org.jkiss.dbeaver.preferences.main.sqlexecute.description = Einstellungen SQL-Verarbeitung

pref.page.name.sql.completion         = SQL-Autovervollständigen
pref.page.name.sql.editor             = SQL-Editor
pref.page.name.sql.execute            = SQL-Ausführung
pref.page.name.sql.format             = SQL-Formatierung
pref.page.name.sql.templates          = Vorlagen
command.org.jkiss.dbeaver.core.sql.editor.console.name=SQL Konsole öffnen

confirm.close_running_query.message = In diesem Editor laufen "{0}" SQL-Abfragen. Sollen diese abgebrochen werden und der Editor geschlossen werden?
confirm.close_running_query.title = Laufende Abfragen abbrechen
confirm.sql.toggleMessage = Nicht mehr nachfragen

confirm.dangerous_sql.message = Sie sind dabei, {0} Anweisung ohne WHERE-Klausel auf "{1}" auszuführen.\r\nMöglicher Datenverlust droht. Trotzdem ausführen?
confirm.dangerous_sql.title = Ausführung einer gefährlichen Abfrage bestätigen
