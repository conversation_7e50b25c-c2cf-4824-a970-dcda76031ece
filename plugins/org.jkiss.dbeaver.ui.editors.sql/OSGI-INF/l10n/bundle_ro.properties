extension-point.org.jkiss.dbeaver.sql.covertname = Conversii de text SQL
extension-point.org.jkiss.dbeaver.sql.editorAddIns.name = Add-in-uri Editor SQL

menu.database.sql.generate = &Generează SQL
menu.org.jkiss.dbeaver.core.connection.sqleditor = Editor SQL
menu.sqleditor.open.default = Comanda implicită
view.sql.results.title = Date

editor.sql.name = Editor SQL
editor.sql.results.name = Rezultate SQL

category.sqleditor.name = Editor SQL
category.sqleditor.description = Comenzi Editor SQL

context.org.jkiss.dbeaver.ui.editors.sql.name = Context Editor SQL
context.org.jkiss.dbeaver.ui.editors.sql.script.name = Context Editor Script SQL

sql.convert.unformatted.text.description = Converteşte textul SQL în text simplu neformatat pe o singură linie
sql.convert.unformatted.text.name        = Text neformatat

sql.convert.java.description = Convertiţi textul SQL în format Java
sql.convert.python.description = Convertiţi textul SQL în format Python
sql.convert.c.description = Convertiţi textul SQL în format C/C++
sql.convert.delphi.description = Convertiţi textul SQL în format Delphi
sql.convert.html.description = Convertiţi textul SQL în format HTML
sql.convert.label.keep.formatting.name = Continuaţi să formataţi
sql.convert.label.keep.formatting.discription = Păstrează formatarea originală (spaţii albe)
sql.convert.label.line.delimiter.name = Delimitator de linie
sql.convert.label.line.delimiter.discription = Delimitator pentru liniile de cod sursă. De obicei \\n sau spaţiu
sql.convert.label.line.delimiter.delphi.discription = Delimitator pentru liniile de cod sursă. De obicei #13#10 sau spaţiu
sql.convert.label.use.string.builder.name = Utilizaţi StringBuilder
sql.convert.label.use.string.builder.description = Utilizează StringBuilder în loc de concatenarea String

sql.plan.view.simple.name = Simplu
sql.plan.view.simple.tip = Prezentarea simplă a planului de execuţie

column.org.jkiss.dbeaver.ui.editors.columns.script.position.name = Poziţia scriptului

command.org.jkiss.dbeaver.ui.editors.text.content.format.name = Format de conţinut
command.org.jkiss.dbeaver.ui.editors.text.content.format.description = Formataţi textul
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.name = Comutaţi aspectul editorului
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.description = Comutaţi aspectul editorului (orizontal/vertical)
command.org.jkiss.dbeaver.ui.editors.sql.web.name = Caută pe web
command.org.jkiss.dbeaver.ui.editors.sql.web.description = Căutaţi textul selectat pe web
command.org.jkiss.dbeaver.ui.editors.sql.copy.query.name = Copiaţi interogarea selectată
command.org.jkiss.dbeaver.ui.editors.sql.copy.query.description = Efectuează o copie a interogării sub cursorul din editorul SQL
command.org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet.name = DDL
command.org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet.description = Generaţi DDL bazat pe setul de rezultate
command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.name = Dezactivaţi analizatorul de sintaxă SQL
command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.description = Dezactivează completarea automată, plierea, evidenţierea apariţiilor cuvintelor\n(Aplicat automat pentru fişierele script mari)

command.org.jkiss.dbeaver.core.sql.editor.create.label = Creaţi script SQL
command.org.jkiss.dbeaver.core.sql.editor.open.name = Deschideţi scriptul SQL
command.org.jkiss.dbeaver.core.sql.editor.open.description = Deschideţi scriptul SQL (existent sau nou)
command.org.jkiss.dbeaver.core.sql.editor.recent.name = Script SQL recent
command.org.jkiss.dbeaver.core.sql.editor.recent.description = Deschideţi cel mai recent script SQL (pentru conexiune activă)
command.org.jkiss.dbeaver.core.sql.editor.create.name = Script SQL nou
command.org.jkiss.dbeaver.core.sql.editor.create.description = Deschideţi un nou script SQL (creează un nou script)
command.org.jkiss.dbeaver.core.sql.editor.console.name = Deschideţi consola SQL
command.org.jkiss.dbeaver.core.sql.editor.console.description = Deschideţi o nouă consolă SQL. Nu va fi creat niciun fişier script.
command.org.jkiss.dbeaver.core.sql.editor.open.default.name = Comanda de deschidere implicită
command.org.jkiss.dbeaver.core.sql.editor.open.default.description = Comanda de deschidere a editorului SQL implicit
command.org.jkiss.dbeaver.core.sql.editor.forSelection.name = Citiţi datele în consola SQL
command.org.jkiss.dbeaver.core.sql.editor.forSelection.description = Deschideţi o nouă consolă SQL cu interogare de citire a datelor
command.org.jkiss.dbeaver.core.procedure.execute.name = Executaţi procedura stocată
command.org.jkiss.dbeaver.core.procedure.execute.description = Deschideţi o nouă consolă SQL cu executarea unei interogări de procedură stocată

command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name = Executaţi interogarea SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description = Executaţi instrucţiunea SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name = Executaţi SQL într-o filă nouă
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description = Executaţi interogarea SQL într-o filă nouă
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition.name = Executaţi scriptul SQL din poziţie
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition.description = Executaţi scriptul din poziţia cursorului
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name = Executaţi scriptul SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description = Executaţi scriptul
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name = Executaţi interogări în file separate
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description = Executaţi instrucţiunile scriptului în file separate de rezultate
command.org.jkiss.dbeaver.ui.editors.sql.run.count.name = Selectaţi numărul de rânduri
command.org.jkiss.dbeaver.ui.editors.sql.run.count.description = Selectaţi numărul de rânduri pentru interogare sub cursor
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name = Selectaţi toate rândurile
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description = Selectaţi şi afişaţi toate rândurile (fără limită de dimensiune de preluare)
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name = Evaluaţi expresia SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description = Selectaţi valoarea expresiei SQL selectate
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name = Explicaţi planul de execuţie
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description = Explicaţi planul de execuţie
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.name = Încărcare plan de execuţie
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.description = Încărcaţi planul de execuţie din fişier
command.org.jkiss.dbeaver.ui.editors.sql.query.next.name = Următoarea interogare
command.org.jkiss.dbeaver.ui.editors.sql.query.next.description = Treceţi la următoarea interogare
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name = Interogare anterioară
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description = Comutaţi la interogarea anterioară
command.org.jkiss.dbeaver.ui.editors.sql.cancel.query.name = Anulaţi interogarea activă
command.org.jkiss.dbeaver.ui.editors.sql.cancel.query.description = Anulează execuţia interogării în fila activă
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.name = Accesaţi paranteza potrivită
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.description = Poziţionaţi cursorul pe paranteza potrivită
command.org.jkiss.dbeaver.ui.editors.sql.ExpandAllFoldings.name = Extindeţi toate pliurile
command.org.jkiss.dbeaver.ui.editors.sql.CollapseAllFoldings.name = Restrângeţi toate pliurile
command.org.jkiss.dbeaver.ui.editors.sql.FoldingsEnabled.name = Plieri activate

command.org.jkiss.dbeaver.ui.editors.sql.show.output.name = Afişează ieşirea serverului
command.org.jkiss.dbeaver.ui.editors.sql.show.output.description = Afişaţi consola de ieşire a serverului
command.org.jkiss.dbeaver.ui.editors.sql.show.outline.name = Comutaţi contur
command.org.jkiss.dbeaver.ui.editors.sql.show.outline.description = Afişaţi/ascundeţi vizualizarea schiţă a scriptului
command.org.jkiss.dbeaver.ui.editors.sql.show.log.name = Afişează jurnalul de execuţie
command.org.jkiss.dbeaver.ui.editors.sql.show.log.description = Afişaţi jurnalul de execuţie SQL
command.org.jkiss.dbeaver.ui.editors.sql.show.variables.name = Afişaţi variabilele SQL
command.org.jkiss.dbeaver.ui.editors.sql.show.variables.description = Afişaţi variabilele SQL active
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.name = Afişaţi panourile în filele de rezultate
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.description = Afişaţi panourile în filele de rezultate în loc de panoul editorului SQL
command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.name =   Afişaţi rezultatele într-o singură file/mai multe file
command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.description = Afişaţi fiecare set de rezultate într-o filă separată sau într-o filă care poate fi derulată
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name = Comutaţi panoul de rezultate
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description = Afişaţi/ascundeţi panoul cu rezultate
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name = Maximizaţi panoul de rezultate
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description = Maximizaţi/normalizaţi panoul de rezultate
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name = Comutaţi panoul activ
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description = Comutaţi panoul editor SQL activ
command.org.jkiss.dbeaver.ui.editors.sql.export.data.name = Exportaţi din interogare
command.org.jkiss.dbeaver.ui.editors.sql.export.data.description = Exportaţi datele returnate de interogarea curentă
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name = Declaraţie deschisă
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description = Deschideţi editorul obiectului de bază de date curent (evidenţiat).
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name = Import script SQL
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description = Încărcaţi scriptul SQL din sistemul de fişiere
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name = Exportaţi scriptul SQL
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description = Salvaţi scriptul în sistemul de fişiere
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name = Transformaţi în listă delimitată
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description = Transformaţi în listă delimitată
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.name = Tăiaţi spaţiile
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.description = Tăiaţi spaţiile de sus şi de conducere
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name = Comentaţi rândul
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description = Adăugaţi sau eliminaţi comentariul pe o singură linie
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name = Comentaţi blocul
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description = Adăugaţi sau eliminaţi comentariul pe mai multe rânduri
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name = Comutaţi împachetarea cuvintelor
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description = Comută editorul de text încadrarea textului
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name = Numele complet al şablonului
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description = Numele şablonului de completare automată
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name = Setaţi conexiunea din navigator
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description = Setaţi conexiunea activă din selectarea navigatorului bazei de date
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name = Sincronizare automată a conexiunii cu navigatorul
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description = Sincronizare automată a conexiunii active cu selectarea navigatorului bazei de date
menu.org.jkiss.dbeaver.ui.editors.sql.connection.separate.label = Deschideţi conexiunea separată
command.org.jkiss.dbeaver.ui.editors.sql.rename.name = Redenumiţi Scriptul SQL
command.org.jkiss.dbeaver.ui.editors.sql.rename.description = Redenumiţi scriptul SQL curent
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.name = Ştergeţi acest script
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.description = Ştergeţi fişierul script curent
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name = Închide
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description = Închideţi fila cu rezultate
command.org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab.name = Fixaţi/Anulaţi fixarea
command.org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab.description = Fila cu rezultate Fixaţi/Anulaţi fixarea

command.org.jkiss.dbeaver.ui.editors.sql.search.web.label =Search selected with Google
command.org.jkiss.dbeaver.ui.editors.sql.search.web.name = Căutare selectată cu Google
command.org.jkiss.dbeaver.ui.editors.sql.search.web.description = Căutare selectată cu Google

menu.sqleditor = &Editor SQL
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label = A executa
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label = Fişier
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label = Aspect
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.extraPanels.label = Panouri

themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label = Editor SQL
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description = Editor SQL

colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.label = Culoarea cuvântului cheie SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.description = Culoarea cuvântului cheie SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.label = Culoarea tipului de date SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.description = Culoarea tipului de date SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.label = Culoarea şirului de caractere SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.description = Culoarea şirului SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.label = Culoarea numărului SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.description = Culoarea numărului SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label = Culoarea comentariului SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description = Culoarea comentariului SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.label = Primul plan delimitator SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.description = Primul plan delimitator de instrucţiuni SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.label = Primul plan al comenzii SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.description = Primul plan al comenzii de control
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.label = Primul plan al parametrului SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.description = Primul plan parametrul SQL (?, :param, etc).
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.label = Primul plan text SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.description = Primul plan text SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.label = Fundal text SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.description = Fundal text SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label = Fundal SQL dezactivat
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description = Fundal text SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.foreground.label = Numele tabelului SQL în prim-plan
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.foreground.description = Numele tabelului SQL în prim-plan
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.alias.foreground.label = Alias de tabel SQL în prim-plan
colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.alias.foreground.description = Alias de tabel SQL în prim-plan
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.foreground.label = Numele coloanei SQL în prim-plan
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.foreground.description = Numele coloanei SQL în prim-plan
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.derived.foreground.label = Primul plan coloană derivată SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.derived.foreground.description = Alias de coloană SQL în prim-plan
colorDefinition.org.jkiss.dbeaver.sql.editor.color.schema.foreground.label = Primul plan al numelui schemei SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.schema.foreground.description = Primul plan al numelui schemei SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.semanticError.foreground.label = Primul plan al erorii semantice SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.semanticError.foreground.description = Primul plan al erorii semantice SQL


keyword.org.jkiss.dbeaver.pref.keyword.sql.format.label = formatter query keyword case tab space indent substatement comma delimiter workbench upper lower mixed line
keyword.org.jkiss.dbeaver.pref.keyword.sql.completion.label = assistant activation delay typing proposal upper lower case replace duplicate hide short long omit object schema catalog insert space table column sort server help alias from search global stored procedure
keyword.org.jkiss.dbeaver.pref.keyword.sql.codeeditor.label = cursor word folding quotes double brackets convert keyword case extract source
keyword.org.jkiss.dbeaver.pref.keyword.sql.sqlexecute.label = invalidate execute beep sound query refresh active clear log statement timeout parameters anonymous mark prefix DDL variables script commit autocommit line error handling rollback ignore fetch resultset cursor editor delimiter
keyword.org.jkiss.dbeaver.pref.keyword.sql.sqleditor.label = connection separate activation query auto-save autosave schema results error orientation output viewer message

page.org.jkiss.dbeaver.preferences.main.sqleditor.name = Editor SQL
page.org.jkiss.dbeaver.preferences.main.sqleditor.description = Setările editorului SQL
page.org.jkiss.dbeaver.preferences.main.sql.completion.name = Completarea codului
page.org.jkiss.dbeaver.preferences.main.sql.completion.description = Setări de completare a codului
page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.name = Editor de coduri
page.org.jkiss.dbeaver.preferences.main.sql.format.name = Formatare
page.org.jkiss.dbeaver.preferences.main.sql.format.description = Setări de formatare SQL
page.org.jkiss.dbeaver.preferences.main.sql.dialects.name = Setări de dialect
page.org.jkiss.dbeaver.preferences.main.sql.resources.name = Scripturi
page.org.jkiss.dbeaver.preferences.main.sqlexecute.name = Procesare SQL
page.org.jkiss.dbeaver.preferences.main.sqlexecute.description = Setări de procesare SQL
page.org.jkiss.dbeaver.preferences.main.sql.templates.name = Şabloane

pref.page.name.sql.editor             = Editor SQL
pref.page.name.sql.completion         = Completarea codului
pref.page.name.sql.codeeditor         = Editor de coduri
pref.page.name.sql.dialects           = Dialectele
pref.page.name.sql.execute            = Procesare SQL
pref.page.name.sql.format             = Formatare
pref.page.name.sql.resources          = Scripturi
pref.page.name.sql.templates          = Şabloane

databaseScriptProblem.name = Problemă cu scriptul bazei de date
scriptEditorPropertyPage.name = Comportamentul Editorului SQL

confirm.sql.group.name = Editor SQL
confirm.close_running_query.title = Anulaţi interogările în curs
confirm.close_running_query.tip = Anulează închiderea editorului dacă există interogări care rulează în editor
confirm.close_running_query.message = Există "{0}" care rulează interogări SQL în acest editor. Sigur doriţi să le anulaţi şi să închideţi editorul?
confirm.sql.toggleMessage = Nu mă întreba din nou

confirm.close_result_tabs.title = Închideţi filele de rezultate suplimentare
confirm.close_result_tabs.message = Există "{0}" file cu rezultate anulate. Doriţi să închideţi aceste file înainte de a executa o nouă interogare?
confirm.close_result_tabs.tip = Avertizează despre închiderea filelor cu rezultate deschise

confirm.dangerous_sql.message = Sunteţi pe cale să executaţi instrucţiunea {0} fără o clauză WHERE pe "{1}".\nPosibilă pierdere de date. Esti sigur?
confirm.dangerous_sql.title = Executaţi interogări periculoase
confirm.dangerous_sql.tip = Avertizează despre execuţia interogării care nu conţine o clauză WHERE

confirm.drop_sql.message = Sunteţi pe cale să executaţi instrucţiunea DROP TABLE.\nPosibilă pierdere de date. Esti sigur?
confirm.drop_sql.title = Executaţi interogări în tabelul drop
confirm.drop_sql.tip = Avertizează despre executarea interogării DROP TABLE

org.jkiss.dbeaver.toolBarConfiguration.sqlEditor.side.top = Bara de instrumente de sus a Editorului SQL
org.jkiss.dbeaver.toolBarConfiguration.sqlEditor.side.bottom = Bara de instrumente din partea de jos a Editorului SQL
