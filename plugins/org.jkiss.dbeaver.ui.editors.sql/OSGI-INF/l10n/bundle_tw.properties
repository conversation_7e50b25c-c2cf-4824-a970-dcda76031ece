# Translated By 2023 <PERSON><PERSON><PERSON> (<EMAIL>)

menu.database.sql.generate =產生SQL(&G)
menu.org.jkiss.dbeaver.core.connection.sqleditor = SQL編輯器
menu.sqleditor.open.default =預設命令
view.sql.results.title =資料

editor.sql.name = SQL編輯器

category.sqleditor.name = SQL編輯器
category.sqleditor.description = SQL編輯器命令

context.org.jkiss.dbeaver.ui.editors.sql.name = SQL編輯器上下文
context.org.jkiss.dbeaver.ui.editors.sql.script.name = SQL腳本編輯器上下文

sql.convert.unformatted.text.description =將SQL文字轉換為無格式的單行純文字
sql.convert.unformatted.text.name =未格式化的文字

sql.convert.java.description =將SQL文字轉換為Java格式
sql.convert.c.description =將SQL文字轉換為C/C++格式
sql.convert.delphi.description =將SQL文字轉換為Delphi格式
sql.convert.html.description =將SQL文字轉換為HTML格式
sql.convert.label.keep.formatting.name =保持格式
sql.convert.label.keep.formatting.discription =保留原始格式（空格）
sql.convert.label.line.delimiter.name =行定界符
sql.convert.label.line.delimiter.discription =來源碼行的分隔字元。通常\\n或空格
sql.convert.label.line.delimiter.delphi.discription =來源碼行的分隔字元。通常是＃13＃10或空格
sql.convert.label.use.string.builder.name =使用StringBuilder
sql.convert.label.use.string.builder.description =使用StringBuilder而不是字串拼接
sql.plan.view.simple.name =簡單
sql.plan.view.simple.tip =簡單的執行計劃演示

column.org.jkiss.dbeaver.ui.editors.columns.script.position.name =腳本位置

command.org.jkiss.dbeaver.ui.editors.text.content.format.name =內容格式
command.org.jkiss.dbeaver.ui.editors.text.content.format.description =格式化文字
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.name =切換編輯器配置
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.description =切換編輯器配置（水平/垂直）
command.org.jkiss.dbeaver.ui.editors.sql.web.name =在網絡中搜索
command.org.jkiss.dbeaver.ui.editors.sql.web.description =在網絡中搜索選定的文字

command.org.jkiss.dbeaver.core.sql.editor.create.label =建立SQL腳本
command.org.jkiss.dbeaver.core.sql.editor.open.name =開啟SQL腳本
command.org.jkiss.dbeaver.core.sql.editor.open.description =開啟SQL腳本（現有或新的）
command.org.jkiss.dbeaver.core.sql.editor.recent.name =最近的SQL腳本
command.org.jkiss.dbeaver.core.sql.editor.recent.description =開啟最新的SQL腳本（用於活動連接）
command.org.jkiss.dbeaver.core.sql.editor.create.name =新的SQL腳本
command.org.jkiss.dbeaver.core.sql.editor.create.description =開啟新的SQL腳本（建立新腳本）
command.org.jkiss.dbeaver.core.sql.editor.console.name =開啟SQL主控台
command.org.jkiss.dbeaver.core.sql.editor.console.description =開啟新的SQL主控台。不會建立任何腳本檔案。
command.org.jkiss.dbeaver.core.sql.editor.open.default.name =預設開啟命令
command.org.jkiss.dbeaver.core.sql.editor.open.default.description =預設的SQL編輯器開啟命令
command.org.jkiss.dbeaver.core.sql.editor.forSelection.name =在SQL主控台中讀取資料
command.org.jkiss.dbeaver.core.sql.editor.forSelection.description =使用資料讀取查詢開啟新的SQL主控台
command.org.jkiss.dbeaver.core.procedure.execute.name =執行存儲過程
command.org.jkiss.dbeaver.core.procedure.execute.description =使用執行存儲過程查詢開啟新的SQL主控台

command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name =執行SQL語句
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description =執行SQL語句
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name =在新選項卡中執行SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description =在新選項卡中執行SQL語句
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name =執行SQL腳本
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description =執行腳本
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name =在單獨的選項卡中執行語句
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description =在單獨的結果選項卡中執行腳本的語句
command.org.jkiss.dbeaver.ui.editors.sql.run.count.name =選擇行數
command.org.jkiss.dbeaver.ui.editors.sql.run.count.description =選擇游標下的行數進行查詢
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name =選擇所有行
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description =選擇並顯示所有行（無讀取大小限制）
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name =評估SQL表達式
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description =選擇所選SQL表達式的值
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name =解釋執行計劃
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description =解釋執行計劃
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.name =載入執行計劃
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.description =從檔案載入執行計劃
command.org.jkiss.dbeaver.ui.editors.sql.query.next.name =下一個查詢
command.org.jkiss.dbeaver.ui.editors.sql.query.next.description =切換到下一個查詢
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name =上一個查詢
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description =切換到上一個查詢
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.name =轉到匹配的括號
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.description =定位光標在匹配的括號上
command.org.jkiss.dbeaver.ui.editors.sql.ExpandAllFoldings.name =展開所有折疊
command.org.jkiss.dbeaver.ui.editors.sql.CollapseAllFoldings.name =折疊所有折疊
command.org.jkiss.dbeaver.ui.editors.sql.FoldingsEnabled.name =啟用折疊
command.org.jkiss.dbeaver.ui.editors.sql.show.output.name =顯示服務器輸出
command.org.jkiss.dbeaver.ui.editors.sql.show.output.description =顯示服務器輸出主控台
command.org.jkiss.dbeaver.ui.editors.sql.show.log.name =顯示執行日誌
command.org.jkiss.dbeaver.ui.editors.sql.show.log.description =顯示SQL執行日誌

command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name =切換結果面板
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description =顯示/隱藏結果面板
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name =最大化結果面板
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description =最大化/標準化結果面板
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name =切換活動面板
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description =切換活動的SQL編輯器面板
command.org.jkiss.dbeaver.ui.editors.sql.export.data.name =從查詢匯出
command.org.jkiss.dbeaver.ui.editors.sql.export.data.description =匯出當前查詢返回的資料
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name =開啟聲明
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description =開啟當前（突出顯示）資料庫對象的編輯器
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name =匯入SQL腳本
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description =從檔案系統載入SQL腳本
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name =匯出SQL腳本
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description =將腳本保存到檔案系統
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name =變形到定界列表
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description =變形到定界列表
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.name =修剪空間
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.description =修剪尾部和前導空格
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name =切換行註釋
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description =新增或刪除單行註釋
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name =切換塊註釋
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description =新增或刪除多行註釋
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name =切換自動換行
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description =切換文字編輯器的自動換行
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name =完整的樣板名稱
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description =自動完成的樣板名稱
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name =設定來自導航器的連接
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description =通過資料庫導航器選擇設定活動連接
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name =與導航器自動同步連接
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description =使用資料庫導航器選擇自動同步活動連接
menu.org.jkiss.dbeaver.ui.editors.sql.connection.separate.label =開啟單獨的連接
command.org.jkiss.dbeaver.ui.editors.sql.rename.name =重新命名SQL腳本
command.org.jkiss.dbeaver.ui.editors.sql.rename.description =重新命名當前的SQL腳本
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.name =刪除該腳本
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.description =刪除當前腳本檔案
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name =關閉頁籤
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description =關閉結果頁籤
command.org.jkiss.dbeaver.ui.editors.sql.search.web.label =使用Google選擇搜索
command.org.jkiss.dbeaver.ui.editors.sql.search.web.name =使用Google選擇的搜索
command.org.jkiss.dbeaver.ui.editors.sql.search.web.description =使用Google選擇的搜索

menu.sqleditor =SQL編輯器(&S)
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label =執行
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label =檔案
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label =配置

themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label = SQL編輯器
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description = SQL編輯器

colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.label = SQL關鍵字顏色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.description = SQL關鍵字顏色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.label = SQL資料類型顏色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.description = SQL資料類型顏色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.label = SQL字串顏色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.description = SQL字串顏色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.label = SQL編號顏色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.description = SQL編號顏色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label = SQL註釋顏色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description = SQL註釋顏色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.label = SQL分隔字元前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.description = SQL語句分隔字元前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.label = SQL命令前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.description =控制命令前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.label = SQL參數前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.description = SQL參數( ?,: 等）前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.label = SQL文字前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.description = SQL文字前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.label = SQL文字背景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.description = SQL文字背景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label = SQL停用背景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description = SQL文字背景

keyword.org.jkiss.dbeaver.pref.keyword.sql.format.label =格式化器 查詢 關鍵字 大小寫 選項卡 空間 縮排 子語句 逗號 分隔字元 工作台 大寫 小寫 混合 行
keyword.org.jkiss.dbeaver.pref.keyword.sql.completion.label =助理 激活 延遲 鍵入 建議 大寫 小寫 替換 重複 隱藏 短 長 省略 物件 架構 目錄 插入 空白 表格 欄位 排序 服務器 幫助 別名 來自 搜索 全局 已存儲的 步驟流程
keyword.org.jkiss.dbeaver.pref.keyword.sql.codeeditor.label =光標詞折疊引號雙括號轉換關鍵字大小寫提取源
keyword.org.jkiss.dbeaver.pref.keyword.sql.sqlexecute.label = 廢止 執行 蜂鳴聲 查詢 刷新 活躍 清除 日誌 陳述 超時 參數 匿名 標記 前綴 DDL 變量 腳本 提交 自動提交 行 錯誤 處理 回滾 忽略 獲取 結果 遊標 編輯器 定界符
keyword.org.jkiss.dbeaver.pref.keyword.sql.sqleditor.label =連接 單獨的 激活 查詢 自動保存 架構 結果 錯誤 方向 輸出 檢視器 訊息

page.org.jkiss.dbeaver.preferences.main.sqleditor.name = SQL編輯器
page.org.jkiss.dbeaver.preferences.main.sqleditor.description = SQL編輯器設定
page.org.jkiss.dbeaver.preferences.main.sql.completion.name =程式碼補全
page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.name =程式碼編輯器
page.org.jkiss.dbeaver.preferences.main.sql.format.name =格式化
page.org.jkiss.dbeaver.preferences.main.sql.dialects.name =語言設定
page.org.jkiss.dbeaver.preferences.main.sql.resources.name =腳本
page.org.jkiss.dbeaver.preferences.main.sqlexecute.name = SQL處理
page.org.jkiss.dbeaver.preferences.main.sqlexecute.description = SQL處理設定
page.org.jkiss.dbeaver.preferences.main.sql.templates.name =樣板

pref.page.name.sql.editor = SQL編輯器
pref.page.name.sql.completion =程式碼補全
pref.page.name.sql.codeeditor =程式碼編輯器
pref.page.name.sql.dialects =方言語法
pref.page.name.sql.execute = SQL處理
pref.page.name.sql.format =格式化
pref.page.name.sql.resources =腳本
pref.page.name.sql.templates =樣板
