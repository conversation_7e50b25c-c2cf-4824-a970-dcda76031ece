# Copyright (C) 2019 <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)

view.sql.results.title=데이터
menu.database.sql.generate=SQL 생성
menu.org.jkiss.dbeaver.core.connection.sqleditor = SQL 편집기

editor.sql.name=SQL 편집기

category.sqleditor.name=SQL 편집기
category.sqleditor.description=SQL 편집기 명령어

context.org.jkiss.dbeaver.ui.editors.sql.name = SQL 편집기 컨텍스트
context.org.jkiss.dbeaver.ui.editors.sql.script.name = SQL 스크립트 편집기 컨텍스트

sql.convert.unformatted.text.description = SQL문 서식없는 1줄로 변환
sql.convert.unformatted.text.name        = 서식없는 텍스트

sql.convert.java.description = SQL문 Java 서식으로 변환
sql.convert.c.description = SQL문 C/C++ 서식으로 변환
sql.convert.delphi.description = SQL문 Delphi 서식으로 변환
sql.convert.html.description = SQL문 HTML 서식으로 변환
sql.convert.label.keep.formatting.name = 서식 유지
sql.convert.label.keep.formatting.discription = 본래 서식 유지 (공백/여백)
sql.convert.label.line.delimiter.name = 라인 구분자
sql.convert.label.line.delimiter.discription = 소스코드 라인 구분자. 보통 \\n 또는 공백.
sql.convert.label.line.delimiter.delphi.discription = 소스코드 라인 구분자. 보통 #13#10 또는 공백
sql.convert.label.use.string.builder.name = StringBuilder 사용
sql.convert.label.use.string.builder.description = 문자열 연결 대신 StringBuilder 사용

sql.formatter.default.name = 서식 기본값
sql.formatter.default.tip = SQL 서식 기본값
sql.formatter.compact.name = Compact 서식
sql.formatter.compact.tip = Compact SQL 서식. 기본 서식과 유사하지만 더 compact하게
sql.formatter.external.name = External formatter
sql.formatter.external.tip = External formatter. Uses configurable command-line executable to format SQL queries

sql.plan.view.simple.name=Simple
sql.plan.view.simple.tip=Simple execution plan presentation

column.org.jkiss.dbeaver.ui.editors.columns.script.position.name = Script position

command.org.jkiss.dbeaver.ui.editors.text.content.format.name=Content Format
command.org.jkiss.dbeaver.ui.editors.text.content.format.description=Format text

command.org.jkiss.dbeaver.core.sql.editor.create.label = 스크립트 생성
command.org.jkiss.dbeaver.core.sql.editor.open.name=SQL 편집기
command.org.jkiss.dbeaver.core.sql.editor.open.description=SQL 편집기 열기 (기존 편집기 또는 새로 열기)
command.org.jkiss.dbeaver.core.sql.editor.recent.name=최근 SQL 편집기
command.org.jkiss.dbeaver.core.sql.editor.recent.description=가장 최근 SQL 스크립트 열기
command.org.jkiss.dbeaver.core.sql.editor.create.name=새 SQL 편집기
command.org.jkiss.dbeaver.core.sql.editor.create.description=새 SQL 편집기 열기 (새 스크립트 생성)
#command.org.jkiss.dbeaver.core.sql.editor.forSelection.name=SQL 콘솔 데이터 읽기
#command.org.jkiss.dbeaver.core.sql.editor.forSelection.description=Open new SQL console with data read query
command.org.jkiss.dbeaver.core.procedure.execute.name=저장 프로시저 실행
command.org.jkiss.dbeaver.core.procedure.execute.description=저장 프로시저 실행 가능한 새 SQL 콘솔 열기
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name=SQL문 실행
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description=SQL문 실행
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name=새 탭에서 SQL문 실행
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description=새 탭에서 SQL문 실행
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name=SQL 스크립트 실행
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description=스크립트 안 SQL문 모두 실행
#command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name=Execute Statements In Separate Tabs
#command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description=Execute script's statements in separate results tabs
command.org.jkiss.dbeaver.ui.editors.sql.run.count.name=로우 건수 조회
command.org.jkiss.dbeaver.ui.editors.sql.run.count.description=현재 커서가 위치한 쿼리 결과의 로우 건수 조회
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name=전체 결과 조회
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description=FETCH 크기 제한없이 모두 조회
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name=Evaluate SQL expression
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description=Select value of selected SQL expression
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name=실행계획 보기
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description=실행계획 보기
command.org.jkiss.dbeaver.ui.editors.sql.query.next.name=다음 쿼리
command.org.jkiss.dbeaver.ui.editors.sql.query.next.description=다음 쿼리로 전환
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name=이전 쿼리
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description=이전 쿼리로 전환
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.name=매칭되는 괄호로 이동
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.description=매칭되는 괄호로 커서 이동

command.org.jkiss.dbeaver.ui.editors.sql.show.output.name=서버 출력 내용 보기
command.org.jkiss.dbeaver.ui.editors.sql.show.output.description=서버 출력 내용을 콘솔에서 보기
command.org.jkiss.dbeaver.ui.editors.sql.show.log.name=실행 로그 보기
command.org.jkiss.dbeaver.ui.editors.sql.show.log.description=SQL 실행 로그 보기

command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name=결과 패널 보기/숨기기
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description=결과 패널 보기/숨기기
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name=결과 패널 최대화
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description=결과 패널 최대화/되돌리기
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name=Switch active panel
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description=Switch active SQL editor panel
command.org.jkiss.dbeaver.ui.editors.sql.export.data.name=쿼리 결과 내보내기
command.org.jkiss.dbeaver.ui.editors.sql.export.data.description=현재 쿼리 결과 데이터를 추출
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name=Open Declaration
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description=Open editor of current (highlighted) database object
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name=SQL 스크립트 불러오기
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description=스크립트 파일로부터 내용을 불러옵니다.
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name=SQL 스크립트 저장
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description=스크립트 파일 저장
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name=Morph to delimited list
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description=Morph to delimited list
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name=한 줄 주석
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description=한 줄을 주석 처리/해제
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name=블록 주석
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description=선택된 블록을 주석 처리/해제
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name=자동 줄바꿈
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description=보기 좋게 줄바꿈 처리/해제
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name=Complete template name
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description=Auto-complete template name
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name=Set connection from navigator
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description=Set active connection from database navigator selection
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name=데이터베이스 네비게이터의 DB 커넥션과 자동 동기화
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description=데이터베이스 네비게이터의 DB 커넥션과 자동 동기화
command.org.jkiss.dbeaver.ui.editors.sql.rename.name=SQL 스크립트 이름변경
command.org.jkiss.dbeaver.ui.editors.sql.rename.description=열려있는 SQL 스크립트 이름변경
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name=탭 닫기
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description=결과 탭 닫기

menu.sqleditor=&SQL 편집기
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label = 실행
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label = 파일
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label = 레이아웃

themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label = SQL 편집기
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description = SQL 편집기

colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.label = SQL keyword color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.description = SQL keyword color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.label = SQL datatype color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.description = SQL datatype color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.label = SQL string color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.description = SQL string color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.label = SQL number color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.description = SQL number color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label = SQL comment color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description = SQL comment color
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.label = SQL delimiter foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.description = SQL statement delimiter foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.label = SQL command foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.description = Control command foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.label = SQL parameter foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.description = SQL parameter (?, :param, etc) foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.label = SQL text foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.description = SQL text foreground
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.label = SQL text background
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.description = SQL text background
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label = SQL disabled background
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description = SQL text background

themeElementCategory.org.jkiss.dbeaver.sql.plan.view.label = 실행계획
themeElementCategory.org.jkiss.dbeaver.sql.plan.view.description = 실행계획 색상

colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.background.label = IndexScan 배경색
colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.background.description = IndexScan 행 배경색
colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.foreground.label = IndexScan 글씨색
colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.foreground.description = IndexScan 행 글씨색
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.background.label = FullScan 배경색
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.background.description = FullScan 행 배경색
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.foreground.label = FullScan 글씨색
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.foreground.description = FullScan 행 글씨색

page.org.jkiss.dbeaver.preferences.main.sqleditor.name = SQL 편집기
page.org.jkiss.dbeaver.preferences.main.sql.completion.name = SQL 자동완성/폴딩
page.org.jkiss.dbeaver.preferences.main.sql.format.name = SQL 포맷 설정
page.org.jkiss.dbeaver.preferences.main.sql.resources.name = 스크립트
page.org.jkiss.dbeaver.preferences.main.sqlexecute.name = SQL 실행
page.org.jkiss.dbeaver.preferences.main.sql.templates.name = 템플릿

pref.page.name.sql.completion         = SQL 자동완성
pref.page.name.sql.editor             = SQL 편집기
pref.page.name.sql.execute            = SQL 실행
pref.page.name.sql.format             = SQL 포맷
pref.page.name.sql.resources          = 스크립트
pref.page.name.sql.templates          = 템플릿
