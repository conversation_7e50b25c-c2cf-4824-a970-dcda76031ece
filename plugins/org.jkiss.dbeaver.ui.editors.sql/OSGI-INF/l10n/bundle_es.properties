
category.sqleditor.description = Comandos de Editor SQL
category.sqleditor.name        = Editor SQL

colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.description   = Color de texto de los comandos de control
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.label         = Color de texto de los comandos SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description   = Color de texto de los comentarios SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label         = Color de texto de los comentarios SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.description  = Color de texto de los tipos de dato SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.label        = Color de texto de los tipos de dato SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.description = Color de texto de los delimitadores de comando SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.label       = Color de texto de los delimitadores SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description  = Color de fondo del texto desactivado en SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label        = Color de fondo del SQL desactivado
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.description   = Color de texto de palabras clave SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.label         = Color de texto de palabras clave SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.description    = Color de texto de números SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.label          = Color de texto de números SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.description = Color de texto de parámetros SQL (?, :param, etc.)
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.label       = Color de texto de parámetros SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.description    = Color de texto de cadenas SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.label          = Color de texto de cadenas SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.description      = Color de fondo del texto SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.label            = Color de fondo del texto SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.description      = Color de texto del texto SQL
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.label            = Color de texto del texto SQL

column.org.jkiss.dbeaver.ui.editors.columns.script.position.name = Posición del script

command.org.jkiss.dbeaver.core.sql.editor.create.description               = Abrir un nuevo script SQL (crear nuevo script)
command.org.jkiss.dbeaver.core.sql.editor.create.label                     = Crear script SQL
command.org.jkiss.dbeaver.core.sql.editor.create.name                      = Nuevo script SQL
command.org.jkiss.dbeaver.core.sql.editor.forSelection.description         = Abrir una nueva consola SQL con consulta de lectura de datos
command.org.jkiss.dbeaver.core.sql.editor.forSelection.name                = Leer datos en la consola SQL
command.org.jkiss.dbeaver.core.sql.editor.open.description                 = Abrir script SQL (existente o nuevo)
command.org.jkiss.dbeaver.core.sql.editor.open.name                        = Script SQL
command.org.jkiss.dbeaver.core.sql.editor.recent.description               = Abrir el script SQL más reciente
command.org.jkiss.dbeaver.core.sql.editor.recent.name                      = Script SQL reciente
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description      = Auto-completado de nombre de plantilla
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name             = Nombre completo de plantilla
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description             = Cerrar pestaña de resultados
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name                    = Cerrar pestaña
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description         = Agregar o quitar comentario de múltiples líneas
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name                = Alternar Comentario de Bloque
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description        = Agregar o quitar comentario de línea única
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name               = Alternar Comentario de Línea
menu.org.jkiss.dbeaver.ui.editors.sql.connection.separate.label            = Abrir conexión separada
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.description      = Borrar el archivo de script actual
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.name             = Borrar este script
command.org.jkiss.dbeaver.ui.editors.sql.export.data.description           = Exportar datos retornados por la consulta actual
command.org.jkiss.dbeaver.ui.editors.sql.export.data.name                  = Exportar Desde Consulta
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.description   = Posicionar el cursor en el paréntesis emparejado
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.name          = Ir a paréntesis emparejado
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.description             = Cargar plan de ejecución desde archivo
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.name                    = Cargar plan de ejecución
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description = Maximizar/normalizar panel de resultados
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name        = Maximizar panel de resultados
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description  = Transformar a lista delimitada
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name         = Transformar a lista delimitada
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description       = Abrir editor del objeto de base de datos actual (resaltado)
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name              = Abrir Declaración
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description             = Cargar script SQL desde un archivo
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name                    = Importar script SQL
command.org.jkiss.dbeaver.ui.editors.sql.query.next.description            = Cambiar a la consulta siguiente
command.org.jkiss.dbeaver.ui.editors.sql.query.next.name                   = Consulta siguiente
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description            = Cambiar a la consulta anterior
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name                   = Consulta anterior
command.org.jkiss.dbeaver.ui.editors.sql.rename.description                = Renombrar script SQL actual
command.org.jkiss.dbeaver.ui.editors.sql.rename.name                       = Renombrar Script SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description          = Seleccionar y mostrar todas las filas (sin límite de tamaño de búsqueda)
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name                 = Seleccionar todas las filas
command.org.jkiss.dbeaver.ui.editors.sql.run.count.description             = Seleccionar conteo de filas para la consulta bajo el cursor
command.org.jkiss.dbeaver.ui.editors.sql.run.count.name                    = Seleccionar conteo de filas
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description           = Visualizar plan de ejecución
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name                  = Visualizar plan de ejecución
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description        = Seleccionar valor de la expresión SQL seleccionada
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name               = Evaluar expresión SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description            = Ejecutar script
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name                   = Ejecutar Script SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description         = Ejecutar comandos del script en pestañas de resultado separadas
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name                = Ejecutar comandos en pestañas separadas
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description         = Ejecutar comando SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name                = Ejecutar Comando SQL
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description      = Ejecutar comando SQL en una pestaña nueva
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name             = Ejecutar SQL en una pestaña nueva
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description             = Grabar script a archivo
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name                    = Exportar script SQL
command.org.jkiss.dbeaver.ui.editors.sql.search.web.description            = Buscar seleccionado con Google
command.org.jkiss.dbeaver.ui.editors.sql.search.web.label                  = Buscar seleccionado con Google
command.org.jkiss.dbeaver.ui.editors.sql.search.web.name                   = Buscar seleccionado con Google
command.org.jkiss.dbeaver.ui.editors.sql.show.log.description              = Mostrar log de ejecución SQL
command.org.jkiss.dbeaver.ui.editors.sql.show.log.name                     = Mostrar log de ejecución
command.org.jkiss.dbeaver.ui.editors.sql.show.output.description           = Mostrar la consola de salida del servidor
command.org.jkiss.dbeaver.ui.editors.sql.show.output.name                  = Mostrar salida del servidor
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description          = Cambiar panel de editor SQL activo
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name                 = Cambiar panel activo
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description             = Auto-sincronizar conexión activa con la selección del navegador de base de datos
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name                    = Auto-sincronizar conexión con el navegador
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description       = Definir conexión activa a partir de la selección del navegador de base de datos
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name              = Definir conexión a partir del navegador
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description   = Mostrar/ocultar el panel de resultados
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name          = Alternar el panel de resultados
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.description          = Alternar diseño del editor (horizontal/vertical)
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.name                 = Alternar diseño del editor
command.org.jkiss.dbeaver.ui.editors.sql.web.description                   = Busca el texto seleccionado en la web
command.org.jkiss.dbeaver.ui.editors.sql.web.name                          = Buscar en la web
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description             = Alterna el ajuste suave de palabras del editor de texto
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name                    = Alternar ajuste de palabras
command.org.jkiss.dbeaver.ui.editors.text.content.format.description       = Formatear texto
command.org.jkiss.dbeaver.ui.editors.text.content.format.name              = Formato de contenido

context.org.jkiss.dbeaver.ui.editors.sql.name        = Contexto de Editor SQL
context.org.jkiss.dbeaver.ui.editors.sql.script.name = Contexto de Editor de Script SQL

editor.sql.name = Editor SQL

extension-point.org.jkiss.dbeaver.sql.covertname = Conversiones de texto SQL

menu.database.sql.generate                                    = &Generar SQL
menu.org.jkiss.dbeaver.core.connection.sqleditor = Editor SQL

menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label = Ejecutar
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label    = Archivo
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label  = Diseño
menu.sqleditor                                                = Editor &SQL

page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.name = Editor de código
page.org.jkiss.dbeaver.preferences.main.sql.completion.name = Autocompletar SQL
page.org.jkiss.dbeaver.preferences.main.sql.dialects.name   = Ajustes de dialectos
page.org.jkiss.dbeaver.preferences.main.sql.format.name     = Formatear SQL
page.org.jkiss.dbeaver.preferences.main.sql.resources.name  = Scripts
page.org.jkiss.dbeaver.preferences.main.sql.templates.name  = Plantillas
page.org.jkiss.dbeaver.preferences.main.sqleditor.name      = Editor SQL
page.org.jkiss.dbeaver.preferences.main.sqleditor.description = Ajustes del editor SQL
page.org.jkiss.dbeaver.preferences.main.sqlexecute.name     = Procesamiento de SQL
page.org.jkiss.dbeaver.preferences.main.sqlexecute.description = Ajustes del procesamiento de SQL

pref.page.name.sql.codeeditor = Editor de código
pref.page.name.sql.completion = Autocompletar SQL
pref.page.name.sql.dialects   = Dialectos
pref.page.name.sql.editor     = Editor SQL
pref.page.name.sql.execute    = Ejecutar la consulta SQL
pref.page.name.sql.format     = Formatear SQL
pref.page.name.sql.resources  = Scripts
pref.page.name.sql.templates  = Plantillas

sql.convert.c.description                           = Convierte texto SQL a formato C/C++
sql.convert.delphi.description                      = Convierte texto SQL a formato Delphi
sql.convert.html.description                        = Convierte texto SQL a formato HTML
sql.convert.java.description                        = Convierte texto SQL a formato Java
sql.convert.label.keep.formatting.discription       = Mantiene el formato original (espacios en blanco)
sql.convert.label.keep.formatting.name              = Mantener el formato
sql.convert.label.line.delimiter.delphi.discription = Delimitador de líneas de código fuente. Generalmente #13#10 o espacio
sql.convert.label.line.delimiter.discription        = Delimitador de líneas de código fuente. Generalmente \\n o espacio
sql.convert.label.line.delimiter.name               = Delimitador de línea
sql.convert.label.use.string.builder.description    = Usa StringBuilder en vez de concatenación de cadenas
sql.convert.label.use.string.builder.name           = Usar StringBuilder
sql.convert.unformatted.text.description            = Convierte texto SQL en texto plano sin formato
sql.convert.unformatted.text.name                   = Texto sin formato
sql.plan.view.simple.name                           = Simple
sql.plan.view.simple.tip                            = Presentación simple de plan de ejecución

themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description = Editor SQL
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label       = Editor SQL

view.sql.results.title = Datos

confirm.sql.group.name = Editor de SQL
confirm.close_result_tabs.message = Hay "{0}" pestañas de resultados sin anclar. ¿Quiere cerrar estas pestañas antes de ejecutar la nueva consulta?
confirm.close_result_tabs.title = Cerrar pestañas de resultados adicionales

confirm.close_running_query.message = Hay "{0}" consultas SQL ejecutándose en este editor. ¿Está seguro que quiere cancelarlas y cerrar el editor?
confirm.close_running_query.title = Cancelar consultas en progreso
confirm.sql.toggleMessage = No preguntarme de nuevo

confirm.dangerous_sql.message = Está a punto de ejecutar una instrucción {0} sin una cláusula WHERE en "{1}".\nPosible pérdida de datos. ¿Está seguro?
confirm.dangerous_sql.title = Confirmar ejecución de consulta peligrosa
