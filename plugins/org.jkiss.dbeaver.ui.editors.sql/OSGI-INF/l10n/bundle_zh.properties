# Copyright (C) 2017 <PERSON>, <PERSON><PERSON> (l<PERSON><PERSON><EMAIL>)
# Copyright (C) 2012 Brook.Tran (<EMAIL>)
# Copyright (C) 2018 <PERSON> (<EMAIL>)

category.sqleditor.description = SQL编辑器命令
category.sqleditor.name        = SQL编辑器

colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.description   = 控制命令前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.label         = SQL 命令前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description   = SQL 注释颜色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label         = SQL 注释颜色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.description  = SQL 数据类型颜色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.label        = SQL 数据类型颜色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.description = SQL 语句分隔符前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.label       = SQL 分隔符前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description  = SQL 文本背景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label        = SQL 禁用背景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.description   = SQL 关键字颜色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.label         = SQL 关键字颜色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.description    = SQL 数字颜色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.label          = SQL 数字颜色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.description = SQL 参数 (?, :param 等) 前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.label       = SQL 参数前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.description    = SQL 字符串颜色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.label          = SQL 字符串颜色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.description      = SQL 文本背景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.label            = SQL 文本背景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.description      = SQL 文本前景
colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.label            = SQL 文本前景

column.org.jkiss.dbeaver.ui.editors.columns.script.position.name = 脚本位置

command.org.jkiss.dbeaver.core.procedure.execute.description                = 打开包含存储过程执行语句的 SQL 终端
command.org.jkiss.dbeaver.core.procedure.execute.name                       = 执行存储过程
command.org.jkiss.dbeaver.core.sql.editor.console.description               = 打开新的 SQL 控制台。不会创建任何脚本文件。
command.org.jkiss.dbeaver.core.sql.editor.console.name                      = 打开 SQL 控制台
command.org.jkiss.dbeaver.core.sql.editor.create.description                = 打开新 SQL 编辑器(新建脚本)
command.org.jkiss.dbeaver.core.sql.editor.create.label                      = 创建 SQL 脚本
command.org.jkiss.dbeaver.core.sql.editor.create.name                       = 新建 SQL 编辑器
command.org.jkiss.dbeaver.core.sql.editor.forSelection.description          = 打开包含数据读取语句的新 SQL 控制台
command.org.jkiss.dbeaver.core.sql.editor.forSelection.name                 = 在 SQL 控制台中读数据
command.org.jkiss.dbeaver.core.sql.editor.open.default.description          = 默认的 SQL 编辑器打开命令
command.org.jkiss.dbeaver.core.sql.editor.open.default.name                 = 默认打开命令
command.org.jkiss.dbeaver.core.sql.editor.open.description                  = 打开 SQL 编辑器(已存在或新建)
command.org.jkiss.dbeaver.core.sql.editor.open.name                         = SQL 编辑器
command.org.jkiss.dbeaver.core.sql.editor.recent.description                = 打开近期 SQL 脚本
command.org.jkiss.dbeaver.core.sql.editor.recent.name                       = 近期 SQL 编辑器
command.org.jkiss.dbeaver.core.sql.script.associate.description             = 将选中的脚本与数据源关联
command.org.jkiss.dbeaver.core.sql.script.associate.name                    = 关联数据源
command.org.jkiss.dbeaver.ui.editors.sql.CollapseAllFoldings.name           = 折叠全部
command.org.jkiss.dbeaver.ui.editors.sql.ExpandAllFoldings.name             = 展开全部
command.org.jkiss.dbeaver.ui.editors.sql.FoldingsEnabled.name               = 折叠已启用
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description       = 自动补全模板名称
command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name              = 补全模板名称
command.org.jkiss.dbeaver.ui.editors.sql.cancel.query.description           = 取消在活动选项卡中执行查询
command.org.jkiss.dbeaver.ui.editors.sql.cancel.query.name                  = 取消活动查询
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description              = 关闭结果标签页
command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name                     = 关闭标签页
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description          = 添加或删除多行注释
command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name                 = 切换块注释
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description         = 添加或删除单行注释
command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name                = 切换行注释
command.org.jkiss.dbeaver.ui.editors.sql.copy.query.description             = 在 SQL 编辑器中复制活动查询
command.org.jkiss.dbeaver.ui.editors.sql.copy.query.name                    = 复制活动查询
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.description       = 删除当前脚本文件
command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.name              = 删除该脚本
command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.name        = 禁用 SQL 语法解析器
command.org.jkiss.dbeaver.ui.editors.sql.export.data.description            = 导出当前查询返回的数据
command.org.jkiss.dbeaver.ui.editors.sql.export.data.name                   = 导出查询结果
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.description    = 定位光标到匹配的括号上
command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.name           = 转到匹配的括号
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.description              = 从文件加载执行计划
command.org.jkiss.dbeaver.ui.editors.sql.load.plan.name                     = 加载执行计划
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description  = 最大化/标准化结果面板
command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name         = 最大化结果面板
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description   = 变换为带分隔符的列表
command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name          = 变换为带分隔符的列表
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description        = 打开当前(高亮)数据库对象的编辑器
command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name               = 打开声明
command.org.jkiss.dbeaver.ui.editors.sql.open.file.description              = 从文件导入
command.org.jkiss.dbeaver.ui.editors.sql.open.file.name                     = 导入 SQL 脚本
command.org.jkiss.dbeaver.ui.editors.sql.query.next.description             = 切换到下一个查询
command.org.jkiss.dbeaver.ui.editors.sql.query.next.name                    = 下一个查询
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description             = 切换到上一个查询
command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name                    = 上一个查询
command.org.jkiss.dbeaver.ui.editors.sql.rename.description                 = 重命名当前 SQL 脚本
command.org.jkiss.dbeaver.ui.editors.sql.rename.name                        = 重命名 SQL 脚本
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description           = 选择并显示所有行(没有获取大小限制)
command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name                  = 选择所有行
command.org.jkiss.dbeaver.ui.editors.sql.run.count.description              = 为下面的查询选择行数
command.org.jkiss.dbeaver.ui.editors.sql.run.count.name                     = 选择行数
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description            = 解释执行计划
command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name                   = 解释执行计划
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description         = 计算选中的 SQL 表达式的值
command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name                = 计算 SQL 表达式的值
command.org.jkiss.dbeaver.ui.editors.sql.run.script.description             = 执行脚本
command.org.jkiss.dbeaver.ui.editors.sql.run.script.name                    = 执行 SQL 脚本
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition.description = 从光标位置开始执行脚本
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition.name        = 从该位置执行 SQL 脚本
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description          = 在单独的结果标签页中执行脚本的语句
command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name                 = 在单独的标签页中执行语句
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description          = 执行 SQL 语句
command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name                 = 执行 SQL 语句
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description       = 在新标签页中执行 SQL 语句
command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name              = 在新标签页中执行 SQL
command.org.jkiss.dbeaver.ui.editors.sql.save.file.description              = 保存至文件
command.org.jkiss.dbeaver.ui.editors.sql.save.file.name                     = 保存 SQL 脚本
command.org.jkiss.dbeaver.ui.editors.sql.search.web.description             = 使用谷歌搜索选定的内容
command.org.jkiss.dbeaver.ui.editors.sql.search.web.label                   = 使用谷歌搜索选定的内容
command.org.jkiss.dbeaver.ui.editors.sql.search.web.name                    = 使用谷歌搜索选定的内容
command.org.jkiss.dbeaver.ui.editors.sql.show.log.description               = 显示 SQL 执行日志
command.org.jkiss.dbeaver.ui.editors.sql.show.log.name                      = 显示执行日志
command.org.jkiss.dbeaver.ui.editors.sql.show.output.description            = 显示服务器控制台输出
command.org.jkiss.dbeaver.ui.editors.sql.show.output.name                   = 显示服务器输出
command.org.jkiss.dbeaver.ui.editors.sql.show.variables.description         = 显示活动的 SQL 变量
command.org.jkiss.dbeaver.ui.editors.sql.show.variables.name                = 显示 SQL 变量
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description           = 切换活动 SQL 编辑器面板
command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name                  = 切换活动面板
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description              = 根据数据库导航器的选择自动同步活动连接
command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name                     = 自动同步连接与导航
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description        = 根据数据库导航器的选择设置活动连接
command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name               = 从导航设置连接
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.description      = 在结果标签中显示面板，而不是SQL编辑器窗格
command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.name             = 在结果标签页中显示面板
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description    = 显示/隐藏结果面板
command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name           = 切换结果面板
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.description           = 切换编辑器布局（水平/垂直）
command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.name                  = 切换编辑器布局
command.org.jkiss.dbeaver.ui.editors.sql.web.description                    = 在网络中搜索选中的文本
command.org.jkiss.dbeaver.ui.editors.sql.web.name                           = 在网络中搜索
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description              = 切换文本编辑器自动换行
command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name                     = 切换自动换行
command.org.jkiss.dbeaver.ui.editors.text.content.format.description        = 格式化文本
command.org.jkiss.dbeaver.ui.editors.text.content.format.name               = 内容格式化

confirm.close_result_tabs.message   = 有"{0}"个未固定的结果标签页。你想在执行新查询之前关闭这些标签页吗？
confirm.close_result_tabs.tip       = 在关闭已打开的结果标签页时发出警告
confirm.close_result_tabs.title     = 关闭多余的结果标签页
confirm.close_running_query.message = 编辑器中有 "{0}" 个正在运行的查询，确定要全部取消并关闭编辑器吗？
confirm.close_running_query.tip     = 如果编辑器中有正在运行的查询，则取消关闭编辑器
confirm.close_running_query.title   = 取消正在运行的查询
confirm.dangerous_sql.message       = 在 "{1}" 上执行的 {0} 语句没有 WHERE 条件.\n可能会丢失数据。确定要执行此操作?
confirm.dangerous_sql.tip           = 执行不包含 WHERE 子句的查询时发出警告
confirm.dangerous_sql.title         = 执行危险查询
confirm.drop_sql.message            = 您即将执行 DROP TABLE 语句。\r\n可能会丢失数据。 您确定要继续吗？
confirm.drop_sql.tip                = 关于 DROP TABLE 查询执行的警告
confirm.drop_sql.title              = 执行 DROP TABLE 查询

confirm.sql.group.name              = SQL 编辑器
confirm.sql.toggleMessage           = 不再询问

context.org.jkiss.dbeaver.ui.editors.sql.name        = SQL 编辑器上下文
context.org.jkiss.dbeaver.ui.editors.sql.script.name = SQL 脚本编辑器上下文
context.org.jkiss.dbeaver.ui.editors.sql.scriptname  = SQL 脚本编辑器上下文

editor.sql.name = SQL 编辑器

extension-point.org.jkiss.dbeaver.sql.covertname = SQL 文本转换

menu.database.sql.generate                                        = 生成 SQL
menu.org.jkiss.dbeaver.core.connection.sqleditor                  = SQL 编辑器
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label     = 执行
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.extraPanels.label = 面板
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label        = 文件
menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label      = 布局
menu.org.jkiss.dbeaver.ui.editors.sql.connection.separate.label   = 打开单独的连接
menu.sqleditor                                                    = &SQL 编辑器
menu.sqleditor.open.default                                       = 默认命令

org.jkiss.dbeaver.toolBarConfiguration.sqlEditor.side.bottom = SQL 编辑器底部工具栏
org.jkiss.dbeaver.toolBarConfiguration.sqlEditor.side.top    = SQL 编辑器顶部工具栏
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.description     = 修剪尾随和前导空格
org.jkiss.dbeaver.ui.editors.sql.trim.spaces.name            = 清理空白

page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.name        = 代码编辑器
page.org.jkiss.dbeaver.preferences.main.sql.completion.description = 代码补全设置
page.org.jkiss.dbeaver.preferences.main.sql.completion.name        = 代码补全
page.org.jkiss.dbeaver.preferences.main.sql.dialects.name          = 方言设置
page.org.jkiss.dbeaver.preferences.main.sql.format.description     = SQL 格式化设置
page.org.jkiss.dbeaver.preferences.main.sql.format.name            = 格式化
page.org.jkiss.dbeaver.preferences.main.sql.resources.name         = 脚本
page.org.jkiss.dbeaver.preferences.main.sql.templates.name         = 模板
page.org.jkiss.dbeaver.preferences.main.sqleditor.description      = SQL 编辑器设置
page.org.jkiss.dbeaver.preferences.main.sqleditor.name             = SQL 编辑器
page.org.jkiss.dbeaver.preferences.main.sqlexecute.description     = SQL 处理设置
page.org.jkiss.dbeaver.preferences.main.sqlexecute.name            = SQL 处理

pref.page.name.sql.codeeditor = 代码编辑器
pref.page.name.sql.completion = 代码补全
pref.page.name.sql.dialects   = 方言语法
pref.page.name.sql.editor     = SQL编辑器
pref.page.name.sql.execute    = SQL处理
pref.page.name.sql.format     = SQL格式化
pref.page.name.sql.resources  = 脚本
pref.page.name.sql.templates  = 模板

sql.convert.c.description                           = 将 SQL 文本转换为 C/C++ 格式
sql.convert.delphi.description                      = 将 SQL 文本转换为 Delphi 格式
sql.convert.html.description                        = 将 SQL 文本转换为 HTML 格式
sql.convert.java.description                        = 将 SQL 文本转换为 Java 格式
sql.convert.label.keep.formatting.discription       = 保留原始格式（空白）
sql.convert.label.keep.formatting.name              = 保持格式
sql.convert.label.line.delimiter.delphi.discription = 源代码行的分隔符。通常是＃13＃10或空格
sql.convert.label.line.delimiter.discription = 源代码行的分隔符。通常为 \\n 或空格
sql.convert.label.line.delimiter.name               = 行分隔符
sql.convert.label.use.string.builder.description    = 使用StringBuilder而不是字符串相加
sql.convert.label.use.string.builder.name           = 使用StringBuilder
sql.convert.unformatted.text.description            = 将 SQL 文本转换为无格式的单行普通文本
sql.convert.unformatted.text.name                   = 无格式文本
sql.plan.view.simple.name                           = 简单
sql.plan.view.simple.tip                            = 简单的执行计划演示

themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description = SQL 编辑器
themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label       = SQL 编辑器

view.sql.results.title = 数据
extension-point.org.jkiss.dbeaver.sql.editorAddIns.name=SQL 编辑器插件
editor.sql.results.name=SQL结果
sql.convert.python.description=将SQL文本转换为Python格式
command.org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet.name=DDL
command.org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet.description=生成基于结果集的 DDL
command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.description=禁用自动补全、代码折叠和单词出现高亮\n（对于大文件脚本会自动应用）
command.org.jkiss.dbeaver.core.sql.editor.showScripts.name=显示脚本
command.org.jkiss.dbeaver.core.sql.editor.showScripts.description=显示现有SQL脚本
command.org.jkiss.dbeaver.ui.editors.sql.selectToMatchingBracket.name=选择匹配的括号
command.org.jkiss.dbeaver.ui.editors.sql.selectToMatchingBracket.description=从当前括号选择到匹配的括号
command.org.jkiss.dbeaver.ui.editors.sql.show.outline.name=切换大纲
command.org.jkiss.dbeaver.ui.editors.sql.show.outline.description=显示/隐藏脚本大纲视图
command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.name=在单个标签页中显示多个结果
command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.description=将每个结果集显示在单独的标签页中，或显示在一个可滚动的标签页中
command.org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab.name=固定/取消固定
command.org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab.description=固定/取消固定结果标签
colorDefinition.org.jkiss.dbeaver.sql.editor.color.function.foreground.label=SQL函数颜色
colorDefinition.org.jkiss.dbeaver.sql.editor.color.function.foreground.description=SQL函数颜色
themeElementCategory.org.jkiss.dbeaver.sql.plan.view.label=查询计划
themeElementCategory.org.jkiss.dbeaver.sql.plan.view.description=查询计划的颜色
colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.background.label=索引扫描背景
colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.background.description=索引扫描行背景
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.background.label=全表扫描背景
colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.background.description=全表扫描行背
page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.description=SQL代码编辑器设置
databaseScriptProblem.name=数据库脚本问题
semanticProblem.name=语义错误问题
scriptEditorPropertyPage.name=SQL编辑器行为
confirm.save_sql_console.title=保存SQL控制台为新的脚本
confirm.save_sql_console.tip=尝试保存 SQL 控制台时的确认
confirm.save_sql_console.message=您想将这个SQL控制台保存为一个新的脚本吗？