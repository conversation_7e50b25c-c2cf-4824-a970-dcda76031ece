<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension-point id="org.jkiss.dbeaver.sql.covert" name="%extension-point.org.jkiss.dbeaver.sql.covertname" schema="schema/org.jkiss.dbeaver.sql.convert.exsd"/>
    <extension-point id="org.jkiss.dbeaver.sql.executors" name="%org.jkiss.dbeaver.sql.executors.name" schema="schema/org.jkiss.dbeaver.sql.executors.exsd"/>
    <extension-point id="org.jkiss.dbeaver.sqlPresentation" name="%extension-point.org.jkiss.dbeaver.sqlPresentation.name" schema="schema/org.jkiss.dbeaver.sqlPresentation.exsd"/>
    <extension-point id="org.jkiss.dbeaver.sql.plan.view" name="%extension-point.org.jkiss.dbeaver.sqlPlanView.name" schema="schema/org.jkiss.dbeaver.sqlPlanView.exsd"/>
    <extension-point id="org.jkiss.dbeaver.sql.editorAddIns" name="%extension-point.org.jkiss.dbeaver.sql.editorAddIns.name" schema="schema/org.jkiss.dbeaver.sql.editorAddIns.exsd"/>

    <extension point="org.eclipse.core.filebuffers.annotationModelCreation">
        <factory contentTypeId="org.jkiss.dbeaver.sql" class="org.jkiss.dbeaver.ui.editors.sql.semantics.SQLEditorSemanticAnnotationModelFactory">
        </factory>
    </extension>

    <extension point="org.eclipse.core.runtime.preferences">
        <initializer class="org.jkiss.dbeaver.ui.editors.sql.internal.SQLEditorPreferencesInitializer"/>
    </extension>

    <extension point="org.jkiss.dbeaver.resourceHandler">
        <handler type="sql-script" class="org.jkiss.dbeaver.ui.editors.sql.scripts.ScriptsHandlerImpl"/>
    </extension>

    <extension point="org.jkiss.dbeaver.ui.fileTypeHandler">
        <handler id="sql" class="org.jkiss.dbeaver.ui.editors.sql.scripts.SQLFileHandler" remote="false" order="5" extensions="sql"/>
    </extension>

    <extension point="org.jkiss.dbeaver.sqlCommand">
        <command id="include" class="org.jkiss.dbeaver.ui.editors.sql.commands.SQLCommandInclude" label="Include" description="Include another SQL script file"/>
    </extension>

    <extension point="org.jkiss.dbeaver.sql.plan.view">
        <view id="simple" priority="100" label="%sql.plan.view.simple.name" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/page_explain_plan.svg" description="%sql.plan.view.simple.tip" class="org.jkiss.dbeaver.ui.editors.sql.plan.simple.SQLPlanViewProviderSimple" />
    </extension>

    <extension point="org.jkiss.dbeaver.sql.covert">
        <target id="unformatted"
                label="%sql.convert.unformatted.text.name"
                description="%sql.convert.unformatted.text.description"
                class="org.jkiss.dbeaver.ui.editors.sql.convert.impl.UnformattedSQLConverter"/>
        <target id="java" label="Java" description="%sql.convert.java.description" class="org.jkiss.dbeaver.ui.editors.sql.convert.impl.JavaSQLConverter">
            <property id="keep-formatting" label="%sql.convert.label.keep.formatting.name" type="boolean" description="%sql.convert.label.keep.formatting.discription" required="false" defaultValue="false"/>
            <property id="line-delimiter" label="%sql.convert.label.line.delimiter.name" type="string" description="%sql.convert.label.line.delimiter.discription" required="false" defaultValue=" "/>
            <property id="use-string-builder" label="%sql.convert.label.use.string.builder.name" type="boolean" description="%sql.convert.label.use.string.builder.description" required="false" defaultValue="false"/>
        </target>
        <target id="python" label="Python" description="%sql.convert.python.description" class="org.jkiss.dbeaver.ui.editors.sql.convert.impl.PythonCodeConverter">
            <property id="keep-formatting" label="%sql.convert.label.keep.formatting.name" type="boolean" description="%sql.convert.label.keep.formatting.discription" required="false" defaultValue="false"/>
        </target>
        <target id="cpp" label="C/C++" description="%sql.convert.c.description" class="org.jkiss.dbeaver.ui.editors.sql.convert.impl.CPPSQLConverter">
            <property id="keep-formatting" label="%sql.convert.label.keep.formatting.name" type="boolean" description="%sql.convert.label.keep.formatting.discription" required="false" defaultValue="false"/>
            <property id="line-delimiter" label="%sql.convert.label.line.delimiter.name" type="string" description="%sql.convert.label.line.delimiter.discription" required="false" defaultValue=" "/>
        </target>
        <target id="delphi" label="Delphi" description="%sql.convert.delphi.description" class="org.jkiss.dbeaver.ui.editors.sql.convert.impl.DelphiSQLConverter">
            <property id="keep-formatting" label="%sql.convert.label.keep.formatting.name" type="boolean" description="%sql.convert.label.keep.formatting.discription" required="false" defaultValue="false"/>
            <property id="line-delimiter" label="%sql.convert.label.line.delimiter.name" type="string" description="%sql.convert.label.line.delimiter.delphi.discription" required="false" defaultValue="#13#10"/>
            <property id="use-string-builder" label="%sql.convert.label.use.string.builder.name" type="boolean" description="%sql.convert.label.use.string.builder.description" required="false" defaultValue="false"/>
        </target>
        <target id="html"
                label="HTML"
                description="%sql.convert.html.description"
                class="org.jkiss.dbeaver.ui.editors.sql.convert.impl.HTMLSQLConverter"/>
    </extension>

    <extension point="org.eclipse.ui.editors">
        <editor name="%editor.sql.name"
                extensions="sql"
                icon="platform:/plugin/org.jkiss.dbeaver.model/icons/tree/script.svg"
                contributorClass="org.jkiss.dbeaver.ui.editors.sql.SQLEditorContributor"
                class="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"
                matchingStrategy="org.jkiss.dbeaver.ui.editors.sql.SQLEditorMatchingStrategy"
                id="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
            <contentTypeBinding contentTypeId="org.jkiss.dbeaver.sql"/>
        </editor>
        <editor name="%editor.sql.results.name"
                icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/grid.svg"
                class="org.jkiss.dbeaver.ui.editors.sql.SQLResultsEditor"
                id="org.jkiss.dbeaver.ui.editors.sql.SQLResultsEditor">
        </editor>
    </extension>

    <extension point="org.eclipse.ui.workbench.texteditor.rulerColumns">
        <column class="org.jkiss.dbeaver.ui.editors.text.ScriptPositionColumn"
                enabled="false"
                global="false"
                id="org.jkiss.dbeaver.ui.editors.columns.script.position"
                includeInMenu="true"
                name="%column.org.jkiss.dbeaver.ui.editors.columns.script.position.name">
            <placement gravity="0.9"/>
            <targetClass class="org.jkiss.dbeaver.ui.editors.sql.SQLEditorBase"/>
        </column>
    </extension>

    <extension point="org.eclipse.ui.editors.annotationTypes">
        <type name="org.eclipse.jdt.ui.occurrences"/>
    </extension>

    <extension point="org.eclipse.ui.editors.markerAnnotationSpecification">
        <specification
                annotationType="org.jkisss.dbeaver.ui.occurrences.underCursor"
                label="SQL - word (under cursor) occurrences"
                icon="platform:/plugin/org.jkiss.dbeaver.model/icons/tree/script.svg"
                textPreferenceKey="occurrenceIndication"
                textPreferenceValue="false"
                highlightPreferenceKey="occurrenceHighlighting"
                highlightPreferenceValue="true"
                contributesToHeader="false"
                overviewRulerPreferenceKey="occurrenceIndicationInOverviewRuler"
                overviewRulerPreferenceValue="true"
                verticalRulerPreferenceKey="occurrenceIndicationInVerticalRuler"
                verticalRulerPreferenceValue="false"
                colorPreferenceKey="occurrenceIndicationColor"
                colorPreferenceValue="212,212,212"
                presentationLayer="4"
                showInNextPrevDropdownToolbarActionKey="showOccurrenceInNextPrevDropdownToolbarAction"
                showInNextPrevDropdownToolbarAction="true"
                isGoToNextNavigationTargetKey="isOccurrenceGoToNextNavigationTarget"
                isGoToNextNavigationTarget="false"
                isGoToPreviousNavigationTargetKey="isOccurrenceGoToPreviousNavigationTarget"
                isGoToPreviousNavigationTarget="false"
                textStylePreferenceKey="occurrenceTextStyle"
                textStylePreferenceValue="NONE">
        </specification>
        <specification
                annotationType="org.jkisss.dbeaver.ui.occurrences.forSelection"
                label="SQL - selected word occurrences"
                icon="platform:/plugin/org.jkiss.dbeaver.model/icons/tree/script.svg"
                textPreferenceKey="writeOccurrenceIndication"
                textPreferenceValue="false"
                highlightPreferenceKey="writeOccurrenceHighlighting"
                highlightPreferenceValue="true"
                contributesToHeader="false"
                overviewRulerPreferenceKey="writeOccurrenceIndicationInOverviewRuler"
                overviewRulerPreferenceValue="true"
                verticalRulerPreferenceKey="writeOccurrenceIndicationInVerticalRuler"
                verticalRulerPreferenceValue="false"
                colorPreferenceKey="writeOccurrenceIndicationColor"
                colorPreferenceValue="240,216,168"
                presentationLayer="4"
                showInNextPrevDropdownToolbarActionKey="SQLEditor.showOccurrenceInNextPrevDropdownToolbarAction"
                showInNextPrevDropdownToolbarAction="true"
                isGoToNextNavigationTargetKey="SQLEditor.isOccurrenceGoToNextNavigationTarget"
                isGoToNextNavigationTarget="false"
                isGoToPreviousNavigationTargetKey="SQLEditor.isOccurrenceGoToPreviousNavigationTarget"
                isGoToPreviousNavigationTarget="false"
                textStylePreferenceKey="SQLEditor.occurrenceTextStyle"
                textStylePreferenceValue="NONE">
        </specification>
    </extension>

    <extension point="org.eclipse.ui.elementFactories">
        <factory id="org.jkiss.dbeaver.ui.editors.sql.SQLEditorInputFactory" class="org.jkiss.dbeaver.ui.editors.sql.SQLEditorInputFactory"/>
    </extension>

    <extension point="org.eclipse.ui.contexts">
        <context
                id="org.jkiss.dbeaver.ui.editors.sql"
                name="%context.org.jkiss.dbeaver.ui.editors.sql.name"
                parentId="org.eclipse.ui.textEditorScope">
        </context>
        <context
                id="org.jkiss.dbeaver.ui.editors.sql.script"
                name="%context.org.jkiss.dbeaver.ui.editors.sql.script.name"
                parentId="org.jkiss.dbeaver.ui.editors.sql">
        </context>
        <context
                id="org.jkiss.dbeaver.ui.editors.sql.script.focused"
                name="%context.org.jkiss.dbeaver.ui.editors.sql.script.name"
                parentId="org.eclipse.ui.textEditorScope">
        </context>

    </extension>

    <extension point="org.eclipse.core.expressions.propertyTesters">
        <propertyTester
                class="org.jkiss.dbeaver.ui.editors.sql.SQLEditorPropertyTester"
                id="org.jkiss.dbeaver.ui.editors.sql.SQLEditorPropertyTester"
                namespace="org.jkiss.dbeaver.ui.editors.sql"
                properties="active,canExecute,canExecuteNative,canExplain,canNavigate,canValidate,canExport,hasSelection,hasActiveQuery,isActiveQueryRunning,foldingEnabled,foldingSupported"
                type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
        <propertyTester
                class="org.jkiss.dbeaver.ui.editors.sql.generator.SQLGeneratorPropertyTester"
                id="org.jkiss.dbeaver.ui.editors.sql.generator.SQLGeneratorPropertyTester"
                namespace="org.jkiss.dbeaver.ui.editors.sql.util"
                properties="canGenerate"
                type="org.eclipse.ui.IWorkbenchPart"/>
    </extension>

    <extension point="org.eclipse.core.expressions.definitions">
        <definition id="org.jkiss.dbeaver.core.ui.sql.editor.base">
            <with variable="activeEditor">
                <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditorBase"/>
            </with>
        </definition>
        <definition id="org.jkiss.dbeaver.core.ui.sql.editor">
            <with variable="activeEditor">
                <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
            </with>
        </definition>
        <definition id="org.jkiss.dbeaver.sql.canOpenEditor">
            <with variable="selection">
                <count value="1"/>
                <iterate operator="and">
                    <and>
                        <not>
                            <instanceof value="org.jkiss.dbeaver.model.navigator.DBNDatabaseFolder"/>
                        </not>
                        <or>
                            <instanceof value="org.jkiss.dbeaver.model.navigator.DBNDataSource"/>
                            <adapt type="org.jkiss.dbeaver.model.struct.DBSStructContainer"/>
                        </or>
                    </and>
                </iterate>
            </with>
        </definition>
    </extension>

    <extension point="org.eclipse.ui.commands">
        <category id="org.jkiss.dbeaver.core.sql" name="%category.sqleditor.name" description="%category.sqleditor.description"/>

        <command id="org.jkiss.dbeaver.core.sql.editor.open" name="%command.org.jkiss.dbeaver.core.sql.editor.open.name" description="%command.org.jkiss.dbeaver.core.sql.editor.open.description" categoryId="org.jkiss.dbeaver.core.database"/>
        <command id="org.jkiss.dbeaver.core.sql.editor.recent" name="%command.org.jkiss.dbeaver.core.sql.editor.recent.name" description="%command.org.jkiss.dbeaver.core.sql.editor.recent.description" categoryId="org.jkiss.dbeaver.core.database"/>
        <command id="org.jkiss.dbeaver.core.sql.editor.create" name="%command.org.jkiss.dbeaver.core.sql.editor.create.name" description="%command.org.jkiss.dbeaver.core.sql.editor.create.description" categoryId="org.jkiss.dbeaver.core.database"/>
        <command id="org.jkiss.dbeaver.core.sql.editor.console" name="%command.org.jkiss.dbeaver.core.sql.editor.console.name" description="%command.org.jkiss.dbeaver.core.sql.editor.console.description" categoryId="org.jkiss.dbeaver.core.database"/>
        <command id="org.jkiss.dbeaver.core.sql.editor.showScripts" name="%command.org.jkiss.dbeaver.core.sql.editor.showScripts.name" description="%command.org.jkiss.dbeaver.core.sql.editor.showScripts.description" categoryId="org.jkiss.dbeaver.core.database"/>
        <command id="org.jkiss.dbeaver.core.sql.editor.forSelection" name="%command.org.jkiss.dbeaver.core.sql.editor.forSelection.name" description="%command.org.jkiss.dbeaver.core.sql.editor.forSelection.description" categoryId="org.jkiss.dbeaver.core.database"/>
        <command id="org.jkiss.dbeaver.core.sql.editor.defaultCommand" name="%command.org.jkiss.dbeaver.core.sql.editor.open.name" description="%command.org.jkiss.dbeaver.core.sql.editor.open.description" categoryId="org.jkiss.dbeaver.core.database"/>
        <command id="org.jkiss.dbeaver.core.sql.editor.open.default" name="%command.org.jkiss.dbeaver.core.sql.editor.open.default.name" description="%command.org.jkiss.dbeaver.core.sql.editor.open.default.description" categoryId="org.jkiss.dbeaver.core.database">
            <commandParameter id="command" name="SQL editor open command" optional="false"/>
        </command>
        <command id="org.jkiss.dbeaver.core.procedure.execute" name="%command.org.jkiss.dbeaver.core.procedure.execute.name" description="%command.org.jkiss.dbeaver.core.procedure.execute.description" categoryId="org.jkiss.dbeaver.core.database"/>

        <command id="org.jkiss.dbeaver.ui.editors.sql.run.statement" name="%command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.run.statementNew" name="%command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.run.script" name="%command.org.jkiss.dbeaver.ui.editors.sql.run.script.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.run.script.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.run.scriptNew" name="%command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.run.scriptNew.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition" name="%command.org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.run.explain" name="%command.org.jkiss.dbeaver.ui.editors.sql.run.explain.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.run.explain.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab" style="toggle" name="%command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.description" categoryId="org.jkiss.dbeaver.core.sql">
            <state class="org.eclipse.ui.handlers.RegistryToggleState:true" id="org.eclipse.ui.commands.toggleState"/>
        </command>
        <command id="org.jkiss.dbeaver.ui.editors.sql.load.plan" name="%command.org.jkiss.dbeaver.ui.editors.sql.load.plan.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.load.plan.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.run.count" name="%command.org.jkiss.dbeaver.ui.editors.sql.run.count.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.run.count.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.run.all.rows" name="%command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.run.all.rows.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.run.expression" name="%command.org.jkiss.dbeaver.ui.editors.sql.run.expression.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.run.expression.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.query.next" name="%command.org.jkiss.dbeaver.ui.editors.sql.query.next.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.query.next.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.query.prev" name="%command.org.jkiss.dbeaver.ui.editors.sql.query.prev.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.query.prev.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.cancel.query" name="%command.org.jkiss.dbeaver.ui.editors.sql.cancel.query.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.cancel.query.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet" name="%command.org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet.description" categoryId="org.jkiss.dbeaver.core.sql"/>

        <command id="org.jkiss.dbeaver.ui.editors.sql.show.output" name="%command.org.jkiss.dbeaver.ui.editors.sql.show.output.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.show.output.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.show.log" name="%command.org.jkiss.dbeaver.ui.editors.sql.show.log.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.show.log.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.show.outline" name="%command.org.jkiss.dbeaver.ui.editors.sql.show.outline.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.show.outline.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.show.variables" name="%command.org.jkiss.dbeaver.ui.editors.sql.show.variables.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.show.variables.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanels" name="%command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanel.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.switch.presentation" name="Switch presentation to" description="Switch SQL editor presentation" categoryId="org.jkiss.dbeaver.core.sql">
            <commandParameter
                    id="presentationId"
                    name="Presentation"
                    optional="false"
                    values="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerSwitchPresentation$ParameterValues"/>
        </command>

        <command id="org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel" name="%command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel" name="%command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.switch.panel" name="%command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.switch.panel.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.open.file" name="%command.org.jkiss.dbeaver.ui.editors.sql.open.file.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.open.file.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.save.file" name="%command.org.jkiss.dbeaver.ui.editors.sql.save.file.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.save.file.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list" name="%command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.trim.spaces" name="%org.jkiss.dbeaver.ui.editors.sql.trim.spaces.name" description="%org.jkiss.dbeaver.ui.editors.sql.trim.spaces.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.comment.single" name="%command.org.jkiss.dbeaver.ui.editors.sql.comment.single.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.comment.single.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.comment.multi" name="%command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.comment.multi.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.word.wrap" name="%command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.word.wrap.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.export.data" name="%command.org.jkiss.dbeaver.ui.editors.sql.export.data.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.export.data.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.navigate.object" name="%command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.navigate.object.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.assist.templates" name="%command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.assist.templates.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.sync.connection" name="%command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.sync.connection.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.sync.auto" name="%command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.sync.auto.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.rename" name="%command.org.jkiss.dbeaver.ui.editors.sql.rename.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.rename.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.deleteThisScript" name="%command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.deleteThisScript.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.close.tab" name="%command.org.jkiss.dbeaver.ui.editors.sql.close.tab.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.close.tab.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab" name="%command.org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket" name="%command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.selectToMatchingBracket" name="%command.org.jkiss.dbeaver.ui.editors.sql.selectToMatchingBracket.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.selectToMatchingBracket.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.text.content.format" name="%command.org.jkiss.dbeaver.ui.editors.text.content.format.name" description="%command.org.jkiss.dbeaver.ui.editors.text.content.format.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.toggleLayout" name="%command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.toggleLayout.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.search.web" name="%command.org.jkiss.dbeaver.ui.editors.sql.web.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.web.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.refresh.current.schema" name="%command.org.jkiss.dbeaver.ui.editors.sql.refresh.current.schema.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.refresh.current.schema.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.refresh.all.schemas" name="%command.org.jkiss.dbeaver.ui.editors.sql.refresh.all.schemas.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.refresh.all.schemas.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser" name="%command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.copy.query" name="%command.org.jkiss.dbeaver.ui.editors.sql.copy.query.name" description="%command.org.jkiss.dbeaver.ui.editors.sql.copy.query.description" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.FoldingsEnabled" name="%command.org.jkiss.dbeaver.ui.editors.sql.FoldingsEnabled.name" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.ExpandAllFoldings" name="%command.org.jkiss.dbeaver.ui.editors.sql.ExpandAllFoldings.name" categoryId="org.jkiss.dbeaver.core.sql"/>
        <command id="org.jkiss.dbeaver.ui.editors.sql.CollapseAllFoldings" name="%command.org.jkiss.dbeaver.ui.editors.sql.CollapseAllFoldings.name" categoryId="org.jkiss.dbeaver.core.sql"/>
    </extension>

    <extension point="org.eclipse.ui.commandImages">
        <image commandId="org.jkiss.dbeaver.core.sql.editor.open" icon="platform:/plugin/org.jkiss.dbeaver.model/icons/tree/script.svg"/>
        <image commandId="org.jkiss.dbeaver.core.sql.editor.recent" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/sql_script_recent.svg"/>
        <image commandId="org.jkiss.dbeaver.core.sql.editor.create" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/sql_script_create.svg"/>
        <image commandId="org.jkiss.dbeaver.core.sql.editor.console" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/sql_console.svg"/>
        <image commandId="org.jkiss.dbeaver.core.sql.editor.showScripts" icon="platform:/plugin/org.jkiss.dbeaver.model/icons/tree/script_folder.svg"/>
        <image commandId="org.jkiss.dbeaver.core.sql.editor.forSelection" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/sql_console.svg"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.run.statement" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/sql_exec.svg"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.run.statementNew" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/sql_exec_new.svg"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.run.script" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/sql_script_exec.svg"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.run.scriptNew" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/sql_script_exec_new.svg"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.run.explain" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/sql_plan.svg"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/sql_multiple_results_per_tab_false.svg"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.cancel.query" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/sql_cancel.svg"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.load.plan" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/file/load.png"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.sync.connection" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sync_connection.svg"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.export.data" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/table_export.svg"/>

        <image commandId="org.jkiss.dbeaver.ui.editors.sql.open.file" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/file/load.png"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.save.file" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/file/save_as.svg"/>

        <image commandId="org.jkiss.dbeaver.ui.editors.sql.show.output" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/page_output.svg"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.show.log" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/page_error.svg"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.show.variables" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/variable.svg"/>
        <image commandId="org.jkiss.dbeaver.ui.editors.sql.show.outline" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/toggle_outline.svg"/>

        <image commandId="org.jkiss.dbeaver.ui.editors.sql.toggleLayout" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/misc/rotate.svg"/>
    </extension>

    <extension point="org.eclipse.ui.handlers">

        <!-- SQL editor handlers -->

        <handler commandId="org.jkiss.dbeaver.core.sql.editor.open" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerOpenEditor">
            <activeWhen>
                <or>
                    <reference definitionId="org.jkiss.dbeaver.core.ui.navigator.part"/>
                    <reference definitionId="org.jkiss.dbeaver.core.ui.datasource.editor"/>
                </or>
            </activeWhen>
            <enabledWhen>
                <with variable="activePart">
                    <test property="org.jkiss.dbeaver.core.global.currentProjectResourceViewable"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.core.sql.editor.recent" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerOpenEditor">
            <enabledWhen>
                <with variable="activePart">
                    <test property="org.jkiss.dbeaver.core.global.currentProjectResourceViewable"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.core.sql.editor.create" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerOpenEditor">
            <enabledWhen>
                <with variable="activePart">
                    <test property="org.jkiss.dbeaver.core.global.currentProjectResourceEditable"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.core.sql.editor.console" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerOpenConsole">
            <enabledWhen>
                <with variable="activePart">
                    <test property="org.jkiss.dbeaver.core.global.hasActiveProject"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.core.sql.editor.showScripts" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerShowScripts">
            <enabledWhen>
                <with variable="activePart">
                    <test property="org.jkiss.dbeaver.core.global.hasActiveProject"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.core.sql.editor.defaultCommand" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerOpenEditor">
            <enabledWhen>
                <with variable="activePart">
                    <test property="org.jkiss.dbeaver.core.global.hasActiveProject"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.core.sql.editor.forSelection" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerOpenObjectConsole">
            <enabledWhen>
                <with variable="selection">
                    <count value="+"/>
                    <iterate operator="and">
                        <adapt type="org.jkiss.dbeaver.model.struct.DBSDataContainer"/>
                    </iterate>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.core.sql.editor.open.default" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerOpenDefaultSwitch"/>
        <handler commandId="org.jkiss.dbeaver.core.procedure.execute" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerRunProcedureConsole">
            <enabledWhen>
                <with variable="selection">
                    <count value="+"/>
                    <iterate operator="and">
                        <adapt type="org.jkiss.dbeaver.model.struct.rdb.DBSProcedure"/>
                    </iterate>
                </with>
            </enabledWhen>
        </handler>

        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.rename" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerRenameFile">
            <enabledWhen>
                <with variable="activePart">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                    	<test property="org.jkiss.dbeaver.core.datasource.projectResourceEditable"/>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.deleteThisScript" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerDeleteCurrentFile">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <test property="org.jkiss.dbeaver.core.datasource.projectResourceEditable"/>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <!--
                <handler commandId="org.eclipse.ui.edit.text.showInformation" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerRenameFilelerRenameFile">
                    <enabledWhen>
                        <with variable="activePart">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </enabledWhen>
                </handler>
        -->
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.generate.ddl.by.resultSet" class="org.jkiss.dbeaver.ui.editors.sql.generator.SQLGeneratorHandlerResultSetDDL"/>

        <!-- SQL editor handlers -->

        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.run.statement" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExecute">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <test property="org.jkiss.dbeaver.ui.editors.sql.canExecute" value="statement"/>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.run.statementNew" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExecute">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <test property="org.jkiss.dbeaver.ui.editors.sql.canExecute" value="statement"/>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.run.script" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExecute">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <test property="org.jkiss.dbeaver.ui.editors.sql.canExecute" value="script"/>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExecute">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <test property="org.jkiss.dbeaver.ui.editors.sql.canExecute" value="script"/>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.core.sql.script.run.scriptNative" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExecute">
            <activeWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor.base"/></activeWhen>
            <enabledWhen>
                    <with variable="activeEditor">
                        <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                            <test property="org.jkiss.dbeaver.ui.editors.sql.canExecuteNative" value="script"/>
                        </adapt>
                    </with>
            </enabledWhen>
        </handler>

        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.run.scriptNew" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExecute">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <test property="org.jkiss.dbeaver.ui.editors.sql.canExecute" value="script"/>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.run.count" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExecute">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <test property="org.jkiss.dbeaver.ui.editors.sql.canExecute" value="statement"/>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.run.all.rows" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExecute">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <test property="org.jkiss.dbeaver.ui.editors.sql.canExecute" value="statement"/>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.run.expression" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExecute">
            <enabledWhen>
                <and>
                    <with variable="activeEditor">
                        <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                            <test property="org.jkiss.dbeaver.ui.editors.sql.hasSelection"/>
                        </adapt>
                    </with>
                </and>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.cancel.query" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerCancelActiveQuery">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <test property="org.jkiss.dbeaver.ui.editors.sql.isActiveQueryRunning"/>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.core.edit.copy.special" class="org.jkiss.dbeaver.ui.editors.sql.convert.CopySourceCodeHandler">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor.base"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.search.web" class="org.jkiss.dbeaver.ui.editors.sql.commands.OpenLinkInWindowHandler">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor.base"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.refresh.current.schema" class="org.jkiss.dbeaver.ui.editors.sql.commands.RefreshActiveSchemaHandler">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor.base"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.refresh.all.schemas" class="org.jkiss.dbeaver.ui.editors.sql.commands.RefreshAllSchemasHandler">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor.base"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser" class="org.jkiss.dbeaver.ui.editors.sql.commands.DisableSQLSyntaxParserHandler">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor.base"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.copy.query" class="org.jkiss.dbeaver.ui.editors.sql.commands.CopyActiveQueryHandler">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <test property="org.jkiss.dbeaver.ui.editors.sql.hasActiveQuery"/>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>

        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.query.next" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerNavigateQuery">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor.base"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.query.prev" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerNavigateQuery">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor.base"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.show.output" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerSwitchPanel">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.show.log" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerSwitchPanel">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.show.outline" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerToggleOutlineView">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.show.variables" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerSwitchPanel">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanels" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerToggleExtraPanels">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>

        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerToggleResultsPanel">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerMaximizeResultsPanel">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.switch.panel" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerSwitchPanel">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.switch.presentation" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerSwitchPresentation">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExecute">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.run.explain" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExecute">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <and>
                            <!-- Ensures the command only available in the default SQL presentation - the only one that supports "script" -->
                            <test property="org.jkiss.dbeaver.ui.editors.sql.canExecute" value="script"/>
                            <test property="org.jkiss.dbeaver.ui.editors.sql.canExplain"/>
                        </and>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.load.plan" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExecute">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <and>
                            <!-- Ensures the command only available in the default SQL presentation - the only one that supports "script" -->
                            <test property="org.jkiss.dbeaver.ui.editors.sql.canExecute" value="script"/>
                            <test property="org.jkiss.dbeaver.ui.editors.sql.canExplain"/>
                        </and>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.close.tab" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerCloseTab">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerToggleTabPinned">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.toggleLayout" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerToggleEditorLayout">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.navigate.object" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerNavigateObject">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditorBase">
                        <test property="org.jkiss.dbeaver.ui.editors.sql.canNavigate"/>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.assist.templates" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerAssistTemplates">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditorBase"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.sync.connection" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerSyncConnection">
            <enabledWhen>
                <with variable="activeEditor">
                    <instanceof value="org.jkiss.dbeaver.ui.IDataSourceContainerUpdate"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.sync.auto" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerSyncConnectionAuto"/>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.export.data" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExportData">
            <enabledWhen>
                <with variable="activeEditor">
                    <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                        <and>
                            <test property="org.jkiss.dbeaver.ui.editors.sql.canExecute" value="script"/>
                            <test property="org.jkiss.dbeaver.ui.editors.sql.canExport"/>
                        </and>
                    </adapt>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.FoldingsEnabled" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerEnableDisableFolding">
            <enabledWhen>
                <and>
                    <with variable="activeEditor">
                        <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditorBase">
                            <test property="org.jkiss.dbeaver.ui.editors.sql.foldingSupported"/>
                        </adapt>
                    </with>
                </and>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.ExpandAllFoldings" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExpandCollapseAllFoldings">
            <enabledWhen>
                <and>
                    <with variable="activeEditor">
                        <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditorBase">
                            <test property="org.jkiss.dbeaver.ui.editors.sql.foldingEnabled"/>
                        </adapt>
                    </with>
                    <with variable="activeEditor">
                        <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditorBase">
                            <test property="org.jkiss.dbeaver.ui.editors.sql.foldingSupported"/>
                        </adapt>
                    </with>
                </and>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.CollapseAllFoldings" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerExpandCollapseAllFoldings">
            <enabledWhen>
                <and>
                    <with variable="activeEditor">
                        <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditorBase">
                            <test property="org.jkiss.dbeaver.ui.editors.sql.foldingEnabled"/>
                        </adapt>
                    </with>
                    <with variable="activeEditor">
                        <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditorBase">
                            <test property="org.jkiss.dbeaver.ui.editors.sql.foldingSupported"/>
                        </adapt>
                    </with>
                </and>
            </enabledWhen>
        </handler>

        <!-- Text editor handlers -->
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.save.file" class="org.jkiss.dbeaver.ui.editors.text.handlers.SaveTextFileHandler">
            <enabledWhen>
                <with variable="activeEditor">
                    <test property="org.jkiss.dbeaver.ui.editors.text.canSave"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.open.file" class="org.jkiss.dbeaver.ui.editors.text.handlers.LoadTextFileHandler">
            <enabledWhen>
                <with variable="activeEditor">
                    <test property="org.jkiss.dbeaver.ui.editors.text.canLoad"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.morph.delimited.list" class="org.jkiss.dbeaver.ui.editors.text.handlers.MorphDelimitedListHandler">
            <enabledWhen>
                <with variable="activeEditor">
                    <test property="org.jkiss.dbeaver.ui.editors.text.canComment" value="single"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.trim.spaces" class="org.jkiss.dbeaver.ui.editors.text.handlers.TrimTextSpacesHandler">
            <enabledWhen>
                <with variable="activeEditor">
                    <test property="org.jkiss.dbeaver.ui.editors.text.available"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.comment.single" class="org.jkiss.dbeaver.ui.editors.text.handlers.ToggleSingleLineCommentHandler">
            <enabledWhen>
                <with variable="activeEditor">
                    <test property="org.jkiss.dbeaver.ui.editors.text.canComment" value="single"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.comment.multi" class="org.jkiss.dbeaver.ui.editors.text.handlers.ToggleMultiLineCommentHandler">
            <enabledWhen>
                <with variable="activeEditor">
                    <test property="org.jkiss.dbeaver.ui.editors.text.canComment" value="single"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.word.wrap" class="org.jkiss.dbeaver.ui.editors.text.handlers.ToggleWordWrapHandler">
            <enabledWhen>
                <with variable="activeEditor">
                    <test property="org.jkiss.dbeaver.ui.editors.text.available"/>
                </with>
            </enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerGoToMatchingBracket">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor.base"/></enabledWhen>
        </handler>
        <handler commandId="org.jkiss.dbeaver.ui.editors.sql.selectToMatchingBracket" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerGoToMatchingBracket">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor.base"/></enabledWhen>
        </handler>

    </extension>

    <extension point="org.eclipse.ui.bindings">

        <!-- SQL Editor -->
        <!--<key commandId="org.jkiss.dbeaver.core.sql.editor.open" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+L"/>-->
        <key commandId="org.jkiss.dbeaver.core.sql.editor.recent" contextId="org.jkiss.dbeaver.ui.context.navigator" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" platform="win32" sequence="CTRL+Enter"/>
        <key commandId="org.jkiss.dbeaver.core.sql.editor.recent" contextId="org.jkiss.dbeaver.ui.context.navigator" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" platform="gtk" sequence="CTRL+Enter"/>
        <key commandId="org.jkiss.dbeaver.core.sql.editor.recent" contextId="org.jkiss.dbeaver.ui.context.navigator" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" platform="cocoa" sequence="COMMAND+Enter"/>
        <key commandId="org.jkiss.dbeaver.core.sql.editor.console" contextId="org.jkiss.dbeaver.ui.context.navigator" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+ALT+Enter"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.sync.connection" contextId="org.jkiss.dbeaver.ui.context.navigator" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+SHIFT+."/>

        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.statement" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" platform="win32" sequence="CTRL+Enter"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.statement" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" platform="gtk" sequence="CTRL+Enter"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.statement" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" platform="cocoa" sequence="COMMAND+Enter"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.statementNew" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" platform="win32" sequence="CTRL+\"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.statementNew" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" platform="gtk" sequence="CTRL+\"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.statementNew" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" platform="cocoa" sequence="COMMAND+\"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.statementNew" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" platform="cocoa" sequence="CTRL+\"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.script" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="ALT+X"/>
        <key commandId="org.jkiss.dbeaver.core.sql.script.run.scriptNative" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="ALT+N"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.scriptNew" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+ALT+SHIFT+X"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="ALT+P"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.count" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+ALT+SHIFT+C"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.all.rows" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+ALT+SHIFT+A"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.expression" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+ALT+'"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.run.explain" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+SHIFT+E"/>

        <key commandId="org.jkiss.dbeaver.ui.editors.sql.close.tab" contextId="org.jkiss.dbeaver.ui.editors.sql.script" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+SHIFT+\"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.toggle.pinned.tab" contextId="org.jkiss.dbeaver.ui.editors.sql.script" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+SHIFT+P"/>

        <key commandId="org.jkiss.dbeaver.ui.editors.sql.navigate.object" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="F4"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.query.next" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="ALT+ARROW_DOWN"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.query.prev" contextId="org.jkiss.dbeaver.ui.editors.sql.script.focused" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="ALT+ARROW_UP"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.show.output" contextId="org.jkiss.dbeaver.ui.editors.sql.script" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+SHIFT+O"/>
        <!-- <key commandId="org.jkiss.dbeaver.ui.editors.sql.show.log" contextId="org.jkiss.dbeaver.ui.editors.sql.script" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration"/> -->

        <key commandId="org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel" contextId="org.jkiss.dbeaver.ui.editors.sql.script" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+T"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel" contextId="org.jkiss.dbeaver.ui.editors.sql.script" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+SHIFT+T"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.switch.panel" contextId="org.jkiss.dbeaver.ui.editors.sql.script" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="ALT+T"/>
        <!--<key commandId="org.jkiss.dbeaver.ui.editors.sql.assist.templates" contextId="org.jkiss.dbeaver.ui.editors.sql" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+ALT+SPACE"/>-->
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.open.file" contextId="org.jkiss.dbeaver.ui.editors.sql" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+ALT+SHIFT+O"/>

        <key commandId="org.jkiss.dbeaver.ui.editors.sql.comment.single" contextId="org.jkiss.dbeaver.ui.editors.sql" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+/"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.comment.multi" contextId="org.jkiss.dbeaver.ui.editors.sql" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+SHIFT+/"/>

        <key commandId="org.jkiss.dbeaver.ui.editors.sql.word.wrap" contextId="org.jkiss.dbeaver.ui.editors.sql" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+ALT+SHIFT+W"/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.rename" contextId="org.jkiss.dbeaver.ui.editors.sql.script" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+F2"/>

        <key commandId="org.jkiss.dbeaver.ui.editors.text.content.format" contextId="org.eclipse.ui.contexts.window" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+SHIFT+F"/>

        <!-- SQL editor open shortcuts. On MacOS we can't use functional key as defaults. On Linux they also have some special OS-wide meaning. -->
        <key commandId="org.jkiss.dbeaver.core.sql.editor.open" contextId="org.jkiss.dbeaver.ui.perspective" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="F3"/>
        <key commandId="org.jkiss.dbeaver.core.sql.editor.open" contextId="org.jkiss.dbeaver.ui.perspective" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+["/>
        <key commandId="org.jkiss.dbeaver.core.sql.editor.create" contextId="org.jkiss.dbeaver.ui.perspective" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+F3"/>
        <key commandId="org.jkiss.dbeaver.core.sql.editor.create" contextId="org.jkiss.dbeaver.ui.perspective" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+]"/>

        <key commandId="org.jkiss.dbeaver.ui.editors.sql.gotoMatchingBracket" contextId="org.jkiss.dbeaver.ui.editors.sql" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+SHIFT+["/>
        <key commandId="org.jkiss.dbeaver.ui.editors.sql.selectToMatchingBracket" contextId="org.jkiss.dbeaver.ui.editors.sql" schemeId="org.eclipse.ui.defaultAcceleratorConfiguration" sequence="CTRL+SHIFT+]"/>
    </extension>

    <extension point="org.eclipse.ui.menus">
        <!-- Navigator context menu -->
        <menuContribution allPopups="false" locationURI="popup:org.eclipse.ui.popup.any?after=sql_tools">
            <menu label="%menu.org.jkiss.dbeaver.core.connection.sqleditor">
                <visibleWhen><reference definitionId="org.jkiss.dbeaver.sql.canOpenEditor"/></visibleWhen>
                <command commandId="org.jkiss.dbeaver.core.sql.editor.open">
                    <visibleWhen checkEnabled="true">
                        <reference definitionId="org.jkiss.dbeaver.sql.canOpenEditor"/>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.core.sql.editor.recent">
                    <visibleWhen checkEnabled="true">
                        <reference definitionId="org.jkiss.dbeaver.sql.canOpenEditor"/>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.core.sql.editor.create">
                    <visibleWhen checkEnabled="true">
                        <reference definitionId="org.jkiss.dbeaver.sql.canOpenEditor"/>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.core.sql.editor.console">
                    <visibleWhen><reference definitionId="org.jkiss.dbeaver.sql.canOpenEditor"/></visibleWhen>
                </command>
            </menu>
        </menuContribution>
        <menuContribution allPopups="false" locationURI="popup:org.eclipse.ui.popup.any?after=generate">
            <command commandId="org.jkiss.dbeaver.core.procedure.execute">
                <visibleWhen checkEnabled="true"/>
            </command>
            <command commandId="org.jkiss.dbeaver.core.sql.editor.forSelection">
                <visibleWhen checkEnabled="true"/>
            </command>
        </menuContribution>

        <menuContribution locationURI="menu:org.jkiss.dbeaver.menu.sql.open">
            <command commandId="org.jkiss.dbeaver.core.sql.editor.open">
                <visibleWhen checkEnabled="true"/>
            </command>
            <command commandId="org.jkiss.dbeaver.core.sql.editor.recent">
                <visibleWhen checkEnabled="true"/>
            </command>
            <command commandId="org.jkiss.dbeaver.core.sql.editor.create">
                <visibleWhen checkEnabled="true"/>
            </command>
            <separator name="console" visible="true"/>
            <command commandId="org.jkiss.dbeaver.core.sql.editor.console"/>
            <separator name="search" visible="true"/>
            <command commandId="org.jkiss.dbeaver.core.sql.editor.showScripts">
            </command>
            <command commandId="org.eclipse.text.quicksearch.commands.quicksearchCommand">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.core.global.bundleInstalled" args="org.eclipse.text.quicksearch"/>
                </visibleWhen>
            </command>
            <separator name="settings" visible="true"/>

            <menu id="SQLEditorOpenDefaultMenu" label="%menu.sqleditor.open.default">
                <visibleWhen>
                    <with variable="activePart">
                        <test property="org.jkiss.dbeaver.core.global.canEditResource"/>
                    </with>
                </visibleWhen>
                <command commandId="org.jkiss.dbeaver.core.sql.editor.open.default" style="radio"><parameter name="command" value="org.jkiss.dbeaver.core.sql.editor.open"/></command>
                <command commandId="org.jkiss.dbeaver.core.sql.editor.open.default" style="radio"><parameter name="command" value="org.jkiss.dbeaver.core.sql.editor.recent"/></command>
                <command commandId="org.jkiss.dbeaver.core.sql.editor.open.default" style="radio"><parameter name="command" value="org.jkiss.dbeaver.core.sql.editor.create"/></command>
                <command commandId="org.jkiss.dbeaver.core.sql.editor.open.default" style="radio"><parameter name="command" value="org.jkiss.dbeaver.core.sql.editor.console"/></command>
            </menu>
        </menuContribution>

        <menuContribution allPopups="false" locationURI="toolbar:dbeaver-general?after=sql_tools">
            <command commandId="org.jkiss.dbeaver.core.sql.editor.defaultCommand"
                 label="SQL"
                 tooltip="%command.org.jkiss.dbeaver.core.sql.editor.open.description"
                 icon="platform:/plugin/org.jkiss.dbeaver.model/icons/tree/script.svg"
                 style="pulldown"
                 mode="FORCE_TEXT"
                 id="org.jkiss.dbeaver.menu.sql.open">
                <visibleWhen checkEnabled="true">
                    <reference definitionId="org.jkiss.dbeaver.core.ui.toolbar.general.visible"/>
                </visibleWhen>
            </command>
<!--
            <command commandId="org.jkiss.dbeaver.core.sql.editor.open"/>
            <command commandId="org.jkiss.dbeaver.core.sql.editor.create"/>
            <command commandId="org.jkiss.dbeaver.core.sql.editor.console"/>
-->
        </menuContribution>

        <!-- SQL editor menu -->
        <menuContribution allPopups="false" locationURI="menu:org.eclipse.ui.main.menu?after=additions">
            <menu id="SQLEditorMenu" label="%menu.sqleditor">
                <visibleWhen><reference definitionId="DBeaverPerspectiveActive"/></visibleWhen>

                <command commandId="org.jkiss.dbeaver.core.sql.editor.open">
                    <visibleWhen checkEnabled="true"/>
                </command>
                <command commandId="org.jkiss.dbeaver.core.sql.editor.recent">
                    <visibleWhen checkEnabled="true"/>
                </command>
                <command commandId="org.jkiss.dbeaver.core.sql.editor.create">
                    <visibleWhen checkEnabled="true"/>
                </command>
                <command commandId="org.jkiss.dbeaver.core.sql.editor.console"/>

                <separator name="execute" visible="true"/>

                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.statement">
                    <visibleWhen>
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.statementNew">
                    <visibleWhen>
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.script">
                    <visibleWhen>
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.core.sql.script.run.scriptNative">
                    <activeWhen><reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor.base"/></activeWhen>
                    <visibleWhen>
                        <with variable="activeEditor">
                            <and>
                                <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                                <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                                    <test property="org.jkiss.dbeaver.ui.editors.sql.canExecuteNative" value="script"/>
                                </adapt>
                            </and>
                        </with>
                    </visibleWhen>
                </command>

                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition">
                    <visibleWhen>
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.scriptNew">
                    <visibleWhen>
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.cancel.query">
                    <visibleWhen>
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.count">
                    <visibleWhen>
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.all.rows">
                    <visibleWhen>
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.expression">
                    <visibleWhen>
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>

                <separator name="query" visible="true"/>

                <dynamic class="org.jkiss.dbeaver.ui.editors.sql.commands.MultipleResultsPerTabMenuContribution" id="org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab.mmcontributor">
                    <visibleWhen>
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </dynamic>
                
                <separator name="plan" visible="true"/>

                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.explain">
                    <visibleWhen>
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.load.plan">
                    <visibleWhen>
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>

                <separator name="files" visible="true"/>

                <command commandId="org.jkiss.dbeaver.ui.editors.sql.open.file">
                    <visibleWhen checkEnabled="true">
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.save.file">
                    <visibleWhen checkEnabled="true">
                        <with variable="activeEditor">
                            <instanceof value="org.jkiss.dbeaver.ui.editors.sql.SQLEditor"/>
                        </with>
                    </visibleWhen>
                </command>

                <separator name="layout" visible="true"/>

                <menu label="%menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.extraPanels.label">
                    <command commandId="org.jkiss.dbeaver.ui.editors.sql.show.output"/>
                    <command commandId="org.jkiss.dbeaver.ui.editors.sql.show.log"/>
                    <command commandId="org.jkiss.dbeaver.ui.editors.sql.show.variables"/>
                    <command commandId="org.jkiss.dbeaver.ui.editors.sql.show.outline" style="toggle"/>
                    <separator name="panels_config" visible="true"/>
                    <command commandId="org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanels" style="toggle"/>
                </menu>
                <menu label="%menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label">
                    <dynamic id="org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.resultSet.orientation" class="org.jkiss.dbeaver.ui.editors.sql.SQLEditor$ResultSetOrientationMenuContributor"/>
                    <separator name="mode_toggles" visible="true"/>
                    <command commandId="org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab" style="toggle"/>
                    <separator name="layout_toggles" visible="true"/>
                    <command commandId="org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel"/>
                    <command commandId="org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel"/>
                    <command commandId="org.jkiss.dbeaver.ui.editors.sql.switch.panel"/>
                </menu>

                <separator name="context" visible="true"/>

                <menu label="%menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.context.label">
                    <separator name="context_toggles" visible="true"/>
                    <command commandId="org.jkiss.dbeaver.ui.tools.select.connection">
                        <parameter name="noCustomLabel" value="true"/>
                        <visibleWhen>
                            <with variable="activeEditor">
                                <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                                    <test property="org.jkiss.dbeaver.core.datasource.projectResourceEditable"/>
                                </adapt>
                            </with>
                        </visibleWhen>
                    </command>
                    <command commandId="org.jkiss.dbeaver.ui.tools.select.schema">
                        <parameter name="noCustomLabel" value="true"/>
                    </command>
                    <command commandId="org.jkiss.dbeaver.ui.editors.sql.sync.connection"/>
                    <command commandId="org.jkiss.dbeaver.ui.editors.sql.refresh.current.schema"/>
                    <command commandId="org.jkiss.dbeaver.ui.editors.sql.refresh.all.schemas"/>
                </menu>

                <separator name="options" visible="true"/>

                <command commandId="org.jkiss.dbeaver.ui.editors.sql.sync.auto" style="toggle"/>

				<menu label="%menu.org.jkiss.dbeaver.ui.editors.sql.connection.separate.label">
                    <dynamic id="org.jkiss.dbeaver.ui.editors.sql.connection.separate.switch" class="org.jkiss.dbeaver.ui.editors.sql.handlers.SQLEditorHandlerSeparateConnectionSwitch"/>
                </menu>
            </menu>
        </menuContribution>

        <menuContribution allPopups="false" locationURI="popup:org.jkiss.dbeaver.ui.editors.sql.SQLEditor.EditorContext?after=sql.additions">
            <menu id="org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute" label="%menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.execute.label" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/sql/sql_exec.svg">
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.statement"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.statementNew"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.script"/>
                <command commandId="org.jkiss.dbeaver.core.sql.script.run.scriptNative"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.scriptFromPosition"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.scriptNew"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.cancel.query"/>
                <separator name="additions_extra" visible="true"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.count"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.all.rows"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.expression"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.export.data"/>
                <separator name="additions_plan" visible="true"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.explain"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.load.plan"/>
                <separator name="plan" visible="true"/>
                <separator name="additions_active_object" visible="true"/>
                <command commandId="org.jkiss.dbeaver.ui.tools.select.connection">
                   <parameter name="noCustomLabel" value="true"/>
                    <visibleWhen>
                        <with variable="activeEditor">
                            <adapt type="org.jkiss.dbeaver.ui.editors.sql.SQLEditor">
                                <test property="org.jkiss.dbeaver.core.datasource.projectResourceEditable"/>
                            </adapt>
                        </with>
                    </visibleWhen>
                </command>
                <command commandId="org.jkiss.dbeaver.ui.tools.select.schema">
                    <parameter name="noCustomLabel" value="true"/>
                </command>
            </menu>
            <menu id="org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file" label="%menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.file.label">
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.rename">
                    <visibleWhen checkEnabled="true"/>
                </command>
                <command commandId="org.eclipse.ui.file.revert"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.open.file">
                    <visibleWhen checkEnabled="true"/>
                </command>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.save.file">
                    <visibleWhen checkEnabled="true"/>
                </command>
                <separator name="editor_file" visible="true"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser" style="toggle" tooltip="%command.org.jkiss.dbeaver.ui.editors.sql.disableSQLSyntaxParser.description">
                    <visibleWhen>
                        <reference definitionId="org.jkiss.dbeaver.core.ui.sql.editor.base"/>
                    </visibleWhen>
                </command>
            </menu>
        </menuContribution>
        <menuContribution allPopups="false" locationURI="popup:org.jkiss.dbeaver.ui.editors.sql.SQLEditor.EditorContext?after=sql.extras">
            <menu id="org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout" label="%menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.label">
                <dynamic id="org.jkiss.dbeaver.ui.editors.sql.SQLEditor.layout.resultSet.orientation" class="org.jkiss.dbeaver.ui.editors.sql.SQLEditor$ResultSetOrientationMenuContributor"/>
                <separator name="mode_toggles" visible="true"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab" style="toggle"/>
                <separator name="layout_toggles" visible="true"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.toggle.result.panel"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.maximize.result.panel"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.switch.panel"/>
            </menu>
        </menuContribution>
        <menuContribution allPopups="false" locationURI="popup:org.jkiss.dbeaver.ui.editors.sql.SQLEditor.EditorContext?after=sql.extras">
            <menu id="org.jkiss.dbeaver.ui.editors.sql.SQLEditor.extraPanels" label="%menu.org.jkiss.dbeaver.ui.editors.sql.SQLEditor.extraPanels.label">
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.show.output"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.show.log"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.show.variables"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.show.outline" style="toggle"/>
                <separator name="panels_config" visible="true"/>
                <command commandId="org.jkiss.dbeaver.ui.editors.sql.toggle.extraPanels" style="toggle"/>
            </menu>
        </menuContribution>
        <menuContribution allPopups="false" locationURI="popup:org.jkiss.dbeaver.ui.editors.sql.SQLEditor.EditorContext?after=group.copy">
            <command commandId="org.jkiss.dbeaver.core.edit.copy.special"/>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.copy.query"/>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.search.web"/>
        </menuContribution>

        <menuContribution allPopups="false" locationURI="popup:org.eclipse.ui.popup.any?after=generate">
            <menu label="%menu.database.sql.generate" icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/misc/sql.svg">
                <visibleWhen>
                    <with variable="activePart">
                        <test property="org.jkiss.dbeaver.ui.editors.sql.util.canGenerate"/>
                    </with>
                </visibleWhen>
                <dynamic id="org.jkiss.dbeaver.core.menu.sql.generate" class="org.jkiss.dbeaver.ui.editors.sql.generator.SQLGeneratorContributor"/>
            </menu>
        </menuContribution>

        <menuContribution allPopups="false" locationURI="popup:#SQLRulerContext?before=additions">
            <separator name="foldings" visible="true"/>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.FoldingsEnabled" label="%command.org.jkiss.dbeaver.ui.editors.sql.FoldingsEnabled.name" style="toggle">
                <visibleWhen checkEnabled="true"/>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.ExpandAllFoldings" label="%command.org.jkiss.dbeaver.ui.editors.sql.ExpandAllFoldings.name" style="push">
                <visibleWhen checkEnabled="true"/>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.CollapseAllFoldings" label="%command.org.jkiss.dbeaver.ui.editors.sql.CollapseAllFoldings.name" style="push">
                <visibleWhen checkEnabled="true"/>
            </command>
        </menuContribution>

        <menuContribution allPopups="false" locationURI="toolbar:org.jkiss.dbeaver.ui.editors.sql.toolbar.side.top" >
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.statement">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.top,org.jkiss.dbeaver.ui.editors.sql.run.statement"/>
                </visibleWhen>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.statementNew">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.top,org.jkiss.dbeaver.ui.editors.sql.run.statementNew"/>
                </visibleWhen>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.script">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.top,org.jkiss.dbeaver.ui.editors.sql.run.script"/>
                </visibleWhen>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.scriptNew">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.top,org.jkiss.dbeaver.ui.editors.sql.run.scriptNew"/>
                </visibleWhen>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.cancel.query">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.top,org.jkiss.dbeaver.ui.editors.sql.cancel.query"/>
                </visibleWhen>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab" style="toggle">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.top,org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab"/>
                </visibleWhen>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.run.explain">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.top,org.jkiss.dbeaver.ui.editors.sql.run.explain"/>
                </visibleWhen>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.load.plan">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.top,org.jkiss.dbeaver.ui.editors.sql.load.plan"/>
                </visibleWhen>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.export.data">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.top,org.jkiss.dbeaver.ui.editors.sql.export.data"/>
                </visibleWhen>
            </command>
            <separator name="tools" visible="false"/>
        </menuContribution>

        <menuContribution allPopups="false" locationURI="toolbar:org.jkiss.dbeaver.ui.editors.sql.toolbar.side.bottom" >
            <dynamic class="org.jkiss.dbeaver.ui.controls.ToolbarSeparatorContribution$Horizontal" id="sqlEditor.side.bottom.separator">
                <visibleWhen checkEnabled="false">
                    <or>
                        <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.bottom,org.jkiss.dbeaver.ui.editors.sql.show.output"/>
                        <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.bottom,org.jkiss.dbeaver.ui.editors.sql.show.log"/>
                        <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.bottom,org.jkiss.dbeaver.ui.editors.sql.show.variables"/>
                        <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.bottom,org.jkiss.dbeaver.ui.editors.sql.show.outline"/>
                    </or>
                </visibleWhen>
            </dynamic>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.show.output" style="toggle">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.bottom,org.jkiss.dbeaver.ui.editors.sql.show.output"/>
                </visibleWhen>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.show.log" style="toggle">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.bottom,org.jkiss.dbeaver.ui.editors.sql.show.log"/>
                </visibleWhen>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.show.variables" style="toggle">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.bottom,org.jkiss.dbeaver.ui.editors.sql.show.variables"/>
                </visibleWhen>
            </command>
            <command commandId="org.jkiss.dbeaver.ui.editors.sql.show.outline" style="toggle">
                <visibleWhen>
                    <test property="org.jkiss.dbeaver.ui.toolbar.configuration.visible" args="sqlEditor.side.bottom,org.jkiss.dbeaver.ui.editors.sql.show.outline"/>
                </visibleWhen>
            </command>
        </menuContribution>
    </extension>

    <extension point="org.eclipse.ui.themes">

        <!-- Colors for SQL editor -->
        <themeElementCategory label="%themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.label" id="org.jkiss.dbeaver.ui.presentation.sql">
            <description>%themeElementCategory.org.jkiss.dbeaver.ui.presentation.sql.description</description>
        </themeElementCategory>

        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.keyword.foreground"
            value="COLOR_DARK_RED">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.keyword.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.datatype.foreground"
            value="COLOR_DARK_BLUE">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.datatype.foreground.description</description>
        </colorDefinition>
        <colorDefinition
                label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.function.foreground.label"
                categoryId="org.jkiss.dbeaver.ui.presentation.sql"
                id="org.jkiss.dbeaver.sql.editor.color.function.foreground"
                value="COLOR_DARK_BLUE">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.function.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.string.foreground"
            value="COLOR_DARK_GREEN">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.string.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.table.foreground"
            value="142,0,198">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.alias.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.table.alias.foreground"
            value="142,0,198">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.table.alias.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.column.foreground"
            value="0,100,100">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.derived.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.column.derived.foreground"
            value="0,100,100">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.column.derived.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.schema.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.schema.foreground"
            value="149,96,55">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.schema.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.composite.field.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.composite.field.foreground"
            value="100,0,100">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.composite.field.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.sqlVariable.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.sqlVariable.foreground"
            value="COLOR_DARK_BLUE">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.sqlVariable.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.semanticError.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.semanticError.foreground"
            value="108,81,81">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.semanticError.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.number.foreground"
            value="COLOR_BLUE">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.number.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.comment.foreground"
            value="COLOR_DARK_GRAY">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.comment.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.delimiter.foreground"
            value="COLOR_RED">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.delimiter.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.command.foreground"
            value="COLOR_DARK_MAGENTA">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.command.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.parameter.foreground"
            value="COLOR_DARK_BLUE">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.parameter.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.text.foreground"
            value="COLOR_BLACK">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.text.background"
            value="COLOR_WHITE">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.text.background.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.disabled.background"
            value="COLOR_WIDGET_BACKGROUND">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.disabled.background.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.aiSuggestion.foreground.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.aiSuggestion.foreground"
            value="128,128,128">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.aiSuggestion.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.editor.color.aiSuggestion.background.label"
            categoryId="org.jkiss.dbeaver.ui.presentation.sql"
            id="org.jkiss.dbeaver.sql.editor.color.aiSuggestion.background"
            value="232,242,254">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.editor.color.aiSuggestion.background.description</description>
        </colorDefinition>
        <!-- Colors for Query Plan -->
        <themeElementCategory label="%themeElementCategory.org.jkiss.dbeaver.sql.plan.view.label" id="org.jkiss.dbeaver.sql.plan.view.colors">
            <description>%themeElementCategory.org.jkiss.dbeaver.sql.plan.view.description</description>
        </themeElementCategory>

        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.background.label"
            categoryId="org.jkiss.dbeaver.sql.plan.view.colors"
            id="org.jkiss.dbeaver.sql.plan.view.color.indexscan.background"
            value="128,255,128">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.plan.indexscan.background.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.foreground.label"
            categoryId="org.jkiss.dbeaver.sql.plan.view.colors"
            id="org.jkiss.dbeaver.sql.plan.view.color.indexscan.foreground"
            value="COLOR_BLACK">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.plan.view.indexscan.foreground.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.background.label"
            categoryId="org.jkiss.dbeaver.sql.plan.view.colors"
            id="org.jkiss.dbeaver.sql.plan.view.color.tablescan.background"
            value="255,128,128">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.background.description</description>
        </colorDefinition>
        <colorDefinition
            label="%colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.foreground.label"
            categoryId="org.jkiss.dbeaver.sql.plan.view.colors"
            id="org.jkiss.dbeaver.sql.plan.view.color.tablescan.foreground"
            value="COLOR_BLACK">
            <description>%colorDefinition.org.jkiss.dbeaver.sql.plan.view.tablescan.foreground.description</description>
        </colorDefinition>

    </extension>

    <extension point="org.eclipse.e4.ui.css.swt.theme">
        <stylesheet uri="css/e4-dark-sql-editor.css">
            <themeid refid="org.eclipse.e4.ui.css.theme.e4_dark"/>
        </stylesheet>
    </extension>

    <extension point="org.eclipse.ui.propertyPages">
        <page category="org.jkiss.dbeaver.preferences.editors" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLEditor" id="org.jkiss.dbeaver.preferences.main.sqleditor" name="%page.org.jkiss.dbeaver.preferences.main.sqleditor.name">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.preferences.datasource"/></enabledWhen>
        </page>
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sql.codeeditor" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLCodeEditing" name="%page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.name">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.preferences.datasource"/></enabledWhen>
        </page>
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sql.completion" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLCompletion" name="%page.org.jkiss.dbeaver.preferences.main.sql.completion.name">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.preferences.datasource"/></enabledWhen>
        </page>
<!--
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sql.dialects" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLDialects" name="%page.org.jkiss.dbeaver.preferences.main.sql.dialects.name">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.preferences.datasource"/></enabledWhen>
        </page>
-->
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sql.resources" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLResources" name="%page.org.jkiss.dbeaver.preferences.main.sql.resources.name">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.preferences.datasource"/></enabledWhen>
        </page>
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sql.format" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLFormat" name="%page.org.jkiss.dbeaver.preferences.main.sql.format.name">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.preferences.datasource"/></enabledWhen>
        </page>
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sqlexecute" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLExecute" name="%page.org.jkiss.dbeaver.preferences.main.sqlexecute.name">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.preferences.datasource"/></enabledWhen>
        </page>
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLTemplates" id="org.jkiss.dbeaver.preferences.main.sql.templates" name="%page.org.jkiss.dbeaver.preferences.main.sql.templates.name">
            <enabledWhen><reference definitionId="org.jkiss.dbeaver.core.preferences.datasource"/></enabledWhen>
        </page>
    </extension>

    <extension point="org.eclipse.ui.preferencePages">
        <page category="org.jkiss.dbeaver.preferences.editors" id="org.jkiss.dbeaver.preferences.main.sqleditor" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLEditor" name="%pref.page.name.sql.editor">
            <keywordReference id="org.jkiss.dbeaver.core.keyword.pref.editor.sql"/>
            <keywordReference id="org.jkiss.dbeaver.core.keyword.dbeaver"/>
            <keywordReference id="org.jkiss.dbeaver.pref.keyword.sql.sqleditor"/>
        </page>
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sqlexecute" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLExecute" name="%pref.page.name.sql.execute">
            <keywordReference id="org.jkiss.dbeaver.core.keyword.pref.editor.sql"/>
            <keywordReference id="org.jkiss.dbeaver.core.keyword.dbeaver"/>
            <keywordReference id="org.jkiss.dbeaver.pref.keyword.sql.sqlexecute"/>
        </page>
<!--
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sql.dialects" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLDialects" name="%pref.page.name.sql.dialects">
            <keywordReference id="org.jkiss.dbeaver.core.keyword.pref.editor.sql"/>
            <keywordReference id="org.jkiss.dbeaver.core.keyword.dbeaver"/>
        </page>
-->
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sql.codeeditor" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLCodeEditing" name="%pref.page.name.sql.codeeditor">
            <keywordReference id="org.jkiss.dbeaver.core.keyword.pref.editor.sql"/>
            <keywordReference id="org.jkiss.dbeaver.core.keyword.dbeaver"/>
            <keywordReference id="org.jkiss.dbeaver.pref.keyword.sql.codeeditor"/>
        </page>
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sql.completion" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLCompletion" name="%pref.page.name.sql.completion">
            <keywordReference id="org.jkiss.dbeaver.core.keyword.pref.editor.sql"/>
            <keywordReference id="org.jkiss.dbeaver.core.keyword.dbeaver"/>
            <keywordReference id="org.jkiss.dbeaver.pref.keyword.sql.completion"/>
        </page>
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sql.format" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLFormat" name="%pref.page.name.sql.format">
            <keywordReference id="org.jkiss.dbeaver.core.keyword.pref.editor.sql"/>
            <keywordReference id="org.jkiss.dbeaver.core.keyword.dbeaver"/>
            <keywordReference id="org.jkiss.dbeaver.pref.keyword.sql.format"/>
        </page>
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sql.resources" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLResources" name="%pref.page.name.sql.resources">
            <keywordReference id="org.jkiss.dbeaver.core.keyword.pref.editor.sql"/>
            <keywordReference id="org.jkiss.dbeaver.core.keyword.dbeaver"/>
        </page>
        <page category="org.jkiss.dbeaver.preferences.main.sqleditor" id="org.jkiss.dbeaver.preferences.main.sql.templates" class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLTemplates" name="%pref.page.name.sql.templates">
            <keywordReference id="org.jkiss.dbeaver.core.keyword.pref.editor.sql"/>
            <keywordReference id="org.jkiss.dbeaver.core.keyword.dbeaver"/>
        </page>
    </extension>

    <extension point="org.eclipse.ui.keywords">
        <keyword id="org.jkiss.dbeaver.pref.keyword.sql.format" label="%keyword.org.jkiss.dbeaver.pref.keyword.sql.format.label"/>
        <keyword id="org.jkiss.dbeaver.pref.keyword.sql.completion" label="%keyword.org.jkiss.dbeaver.pref.keyword.sql.completion.label"/>
        <keyword id="org.jkiss.dbeaver.pref.keyword.sql.codeeditor" label="%keyword.org.jkiss.dbeaver.pref.keyword.sql.codeeditor.label"/>
        <keyword id="org.jkiss.dbeaver.pref.keyword.sql.sqlexecute" label="%keyword.org.jkiss.dbeaver.pref.keyword.sql.sqlexecute.label"/>
        <keyword id="org.jkiss.dbeaver.pref.keyword.sql.sqleditor" label="%keyword.org.jkiss.dbeaver.pref.keyword.sql.sqleditor.label"/>
    </extension>

    <extension point="org.jkiss.dbeaver.ui.dataSourceConfigurator">
        <dataSourceConfigurator>
            <dataSourcePage
                id="sql-editor.main"
                after="data-editor-main"
                class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLEditor"
                title="%page.org.jkiss.dbeaver.preferences.main.sqleditor.name"
                description="%page.org.jkiss.dbeaver.preferences.main.sqleditor.description"/>
            <dataSourcePage
                id="sql-editor.codeeditor"
                parent="sql-editor.main"
                class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLCodeEditing"
                title="%page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.name"
                description="%page.org.jkiss.dbeaver.preferences.main.sql.codeeditor.description"/>
            <dataSourcePage
                id="sql-editor.completion"
                parent="sql-editor.main"
                class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLCompletion"
                title="%page.org.jkiss.dbeaver.preferences.main.sql.completion.name"
                description="%page.org.jkiss.dbeaver.preferences.main.sql.completion.description"/>
            <dataSourcePage
                id="sql-editor.format"
                parent="sql-editor.main"
                class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLFormat"
                title="%page.org.jkiss.dbeaver.preferences.main.sql.format.name"
                description="%page.org.jkiss.dbeaver.preferences.main.sql.format.description"/>
            <dataSourcePage
                id="sql-editor.execute"
                parent="sql-editor.main"
                class="org.jkiss.dbeaver.ui.editors.sql.preferences.PrefPageSQLExecute"
                title="%page.org.jkiss.dbeaver.preferences.main.sqlexecute.name"
                description="%page.org.jkiss.dbeaver.preferences.main.sqlexecute.description"/>
        </dataSourceConfigurator>
    </extension>

    <!--
        <extension point="org.eclipse.ui.perspectiveExtensions">
            <perspectiveExtension targetID="org.jkiss.dbeaver.core.perspective">
                <hiddenToolBarItem id="org.jkiss.dbeaver.core.sql.editor.open"/>
                <hiddenToolBarItem id="org.jkiss.dbeaver.core.sql.editor.create"/>
                <hiddenToolBarItem id="org.jkiss.dbeaver.core.sql.editor.console"/>
            </perspectiveExtension>
        </extension>
    -->

    <extension id="databaseScriptProblemMarker" name="%databaseScriptProblem.name" point="org.eclipse.core.resources.markers">
        <super type="org.eclipse.core.resources.problemmarker"/>
        <super type="org.eclipse.core.resources.textmarker"/>
    </extension>

    <extension id="semanticProblemMarker" name="%semanticProblem.name" point="org.eclipse.core.resources.markers">
        <super type="org.eclipse.core.resources.problemmarker"/>
        <super type="org.eclipse.core.resources.textmarker"/>
    </extension>

    <extension point="org.jkiss.dbeaver.sqlGenerator">
        <generator id="ddlByResultSet" class="org.jkiss.dbeaver.ui.editors.sql.generator.SQLGeneratorDDLFromResultSet" label="CREATE" description="CREATE table" order="6">
            <objectType name="org.jkiss.dbeaver.ui.controls.resultset.IResultSetController"/>
        </generator>
    </extension>

    <extension point="org.jkiss.dbeaver.confirmations">
        <confirmation
                id="close_running_query"
                title="%confirm.close_running_query.title"
                description="%confirm.close_running_query.tip"
                message="%confirm.close_running_query.message"
                toggleMessage="%confirm.sql.toggleMessage"
                group="%confirm.sql.group.name"/>
        <confirmation
                id="dangerous_sql"
                title="%confirm.dangerous_sql.title"
                description="%confirm.dangerous_sql.tip"
                message="%confirm.dangerous_sql.message"
                toggleMessage="%confirm.sql.toggleMessage"
                group="%confirm.sql.group.name"/>
        <confirmation
                id="drop_sql"
                title="%confirm.drop_sql.title"
                description="%confirm.drop_sql.tip"
                message="%confirm.drop_sql.message"
                toggleMessage="%confirm.sql.toggleMessage"
                group="%confirm.sql.group.name"/>
        <confirmation
                id="close_result_tabs"
                title="%confirm.close_result_tabs.title"
                description="%confirm.close_result_tabs.tip"
                message="%confirm.close_result_tabs.message"
                toggleMessage="%confirm.sql.toggleMessage"
                group="%confirm.sql.group.name"/>
        <confirmation
                id="save_sql_console"
                title="%confirm.save_sql_console.title"
                description="%confirm.save_sql_console.tip"
                message="%confirm.save_sql_console.message"
                toggleMessage="%confirm.sql.toggleMessage"
                group="%confirm.sql.group.name"/>
    </extension>

    <extension point="org.jkiss.dbeaver.toolBarConfiguration">
        <toolBar key="sqlEditor.side.top" name="%org.jkiss.dbeaver.toolBarConfiguration.sqlEditor.side.top">
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.run.statement" defaultVisibility="true"/>
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.run.statementNew" defaultVisibility="true"/>
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.run.script" defaultVisibility="true"/>
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.run.scriptNew" defaultVisibility="false"/>
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.multipleResultsPerTab" defaultVisibility="false"/>
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.run.explain" defaultVisibility="true"/>
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.load.plan" defaultVisibility="false"/>
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.export.data" defaultVisibility="false"/>
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.cancel.query" defaultVisibility="false"/>
        </toolBar>
        <toolBar key="sqlEditor.side.bottom" name="%org.jkiss.dbeaver.toolBarConfiguration.sqlEditor.side.bottom">
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.show.output" defaultVisibility="true"/>
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.show.log" defaultVisibility="true"/>
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.show.variables" defaultVisibility="true"/>
            <item commandId="org.jkiss.dbeaver.ui.editors.sql.show.outline" defaultVisibility="true"/>
        </toolBar>
    </extension>

    <extension point="org.jkiss.dbeaver.sqlFormatter">
        <formatter
                id="sqlworkbenchj"
                class="org.jkiss.dbeaver.ui.editors.sql.format.sqlworkbenchj.SQLWorkbenchJFormatter"
                label="SQL Workbench/J formatter"
                description="Formatter from SQL Workbench/J"/>
    </extension>

    <extension point="org.eclipse.core.runtime.adapters">
        <factory adaptableType="org.jkiss.dbeaver.ui.editors.sql.format.sqlworkbenchj.SQLWorkbenchJFormatter" class="org.jkiss.dbeaver.ui.editors.sql.format.sqlworkbenchj.SQLWorkbenchJAdapterFactory">
            <adapter type="org.jkiss.dbeaver.ui.editors.sql.preferences.format.SQLFormatterConfigurator"/>
        </factory>
    </extension>

</plugin>
