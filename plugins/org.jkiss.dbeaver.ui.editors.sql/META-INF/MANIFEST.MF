Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Vendor: DBeaver Corp
Bundle-Name: DBeaver UI Editors - SQL
Bundle-SymbolicName: org.jkiss.dbeaver.ui.editors.sql;singleton:=true
Bundle-Version: 1.0.162.qualifier
Bundle-Release-Date: 20250901
Bundle-RequiredExecutionEnvironment: JavaSE-21
Bundle-ActivationPolicy: lazy
Bundle-Activator: org.jkiss.dbeaver.ui.editors.sql.internal.SQLEditorActivator
Require-Bundle: org.jkiss.dbeaver.model.sql;visibility:=reexport,
 org.jkiss.dbeaver.model.lsm;visibility:=reexport,
 org.jkiss.dbeaver.ui;visibility:=reexport,
 org.jkiss.dbeaver.data.transfer,
 org.jkiss.dbeaver.data.transfer.ui,
 org.jkiss.dbeaver.registry,
 org.jkiss.dbeaver.ui.editors.base;visibility:=reexport,
 org.jkiss.dbeaver.ui.editors.data;visibility:=reexport,
 org.jkiss.dbeaver.ui.navigator,
 org.jkiss.dbeaver.tasks.native.ui;visibility:=reexport,
 org.eclipse.ui.workbench.texteditor;visibility:=reexport,
 org.eclipse.ui.console,
 org.eclipse.e4.ui.model.workbench,
 org.eclipse.ui.forms,
 org.eclipse.e4.ui.workbench.renderers.swt
Export-Package: org.jkiss.dbeaver.ui.controls,
 org.jkiss.dbeaver.ui.controls.querylog,
 org.jkiss.dbeaver.ui.editors.sql,
 org.jkiss.dbeaver.ui.editors.sql.addins,
 org.jkiss.dbeaver.ui.editors.sql.convert,
 org.jkiss.dbeaver.ui.editors.sql.dialogs,
 org.jkiss.dbeaver.ui.editors.sql.generator,
 org.jkiss.dbeaver.ui.editors.sql.handlers,
 org.jkiss.dbeaver.ui.editors.sql.indent,
 org.jkiss.dbeaver.ui.editors.sql.internal,
 org.jkiss.dbeaver.ui.editors.sql.log,
 org.jkiss.dbeaver.ui.editors.sql.plan,
 org.jkiss.dbeaver.ui.editors.sql.plan.simple,
 org.jkiss.dbeaver.ui.editors.sql.preferences,
 org.jkiss.dbeaver.ui.editors.sql.preferences.format,
 org.jkiss.dbeaver.ui.editors.sql.registry,
 org.jkiss.dbeaver.ui.editors.sql.scripts,
 org.jkiss.dbeaver.ui.editors.sql.syntax,
 org.jkiss.dbeaver.ui.editors.sql.templates,
 org.jkiss.dbeaver.ui.editors.sql.util,
 org.jkiss.dbeaver.ui.editors.sql.suggestion,
 org.jkiss.dbeaver.ui.editors.sql.commands
Automatic-Module-Name: org.jkiss.dbeaver.ui.editors.sql

