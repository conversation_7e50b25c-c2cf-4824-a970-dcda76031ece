# Translated By 2021 Ralic Lo (rali<PERSON><PERSON>@gmail.com)
# Translated By 2023 <PERSON><PERSON><PERSON> (<EMAIL>)
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.ownerName.name =擁有者名稱
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.ownerName.description =最初建立表格或檢視的使用者的使用者名稱
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.externalFile.name =外部檔案
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.externalFile.description =如果表格是使用EXTERNAL FILE子句定義的，則為外部資料檔案的完整路徑
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.keyLength.name =密鑰長度
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.keyLength.description =資料庫密鑰的總長度。\n 對於表格：8 位元組。\n 對於檢視，長度是8乘以檢視引用的表格
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdView.ownerName.name =擁有者名稱
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdSequence.lastValue.name =最後一個值
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.triggerType.name =類型
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.sequence.name =序列
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldType.name =類型
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.defaultSource.name =預設值
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.computedSource.name =由...計算
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.validationSource.name =檢查
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charsetName.name =字元集
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.subType.name =子類型
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldLength.name =長度
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charLength.name =字元長度
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.notNull.name =非空
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.domainTypeName.name =域名
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.charset.name =字元集