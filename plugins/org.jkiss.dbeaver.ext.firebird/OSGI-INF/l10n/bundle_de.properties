datasource.firebird.description = Firebird Jaybird JDBC Treiber

meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charLength.name        = Zeichenlänge
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charsetName.name       = Zeichensatz
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.computedSource.name    = Berechnet von
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.defaultSource.name     = Standardwert
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldLength.name       = Länge
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldType.name         = Typ
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.notNull.name           = Nicht NULL
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.subType.name           = Untertyp
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.validationSource.name  = Überprüfen
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.charset.name        = Zeichensatz
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.domainTypeName.name = Domaintyp
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.sequence.name           = Sequenz
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.triggerType.name        = Typ
