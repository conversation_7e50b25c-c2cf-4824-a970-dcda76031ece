# Copyright (C) 2017 <PERSON>, <PERSON><PERSON> (<EMAIL>)

meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.triggerType.name=类型
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.sequence.name=序列
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldType.name=类型
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.defaultSource.name=默认值
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.computedSource.name=被计算
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.validationSource.name=检查
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charsetName.name=字符集
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.subType.name=子类型
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldLength.name=长度
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charLength.name=字符长度
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.notNull.name=非空
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.domainTypeName.name=域类型
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.charset.name=字符集
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.ownerName.name=所有者名称
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.ownerName.description=最初创建表或视图的用户的用户名
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.externalFile.name=外部文件
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.externalFile.description=如果表是使用 EXTERNAL FILE 子句定义的，则为外部数据文件的完整路径
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.keyLength.description=数据库键的总长度。\n对于一个表：8 字节。\n对于一个视图，长度为 8 乘以视图引用的表的数量
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.keyLength.name=键长度
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdView.ownerName.name=所有者名称
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdSequence.lastValue.name=最后的值