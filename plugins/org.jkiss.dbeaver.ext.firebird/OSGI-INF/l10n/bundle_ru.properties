datasource.firebird.description=Firebird Jaybird JDBC driver

meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.triggerType.name=Тип
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.sequence.name=Последовательность
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldType.name=Тип
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.defaultSource.name=Значение по умолчанию
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.computedSource.name=Вычислено по
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.validationSource.name=Проверка
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charsetName.name=Кодировка
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.subType.name=Подтип
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldLength.name=Длина
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charLength.name=Длина символа
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.notNull.name=Обязательность
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.domainTypeName.name=Тип домена
