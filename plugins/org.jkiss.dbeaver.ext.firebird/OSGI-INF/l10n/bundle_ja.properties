datasource.firebird.description=Firebird Jaybird JDBCドライバ

meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.triggerType.name=タイプ
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.sequence.name=シーケンス
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldType.name=タイプ
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.defaultSource.name=デフォルト値
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.computedSource.name=計算者
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.validationSource.name=チェック
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charsetName.name=Charset
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.subType.name=サブタイプ
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldLength.name=長さ
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charLength.name=文字の長さ
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.notNull.name=NULLでない
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.domainTypeName.name=ドメインタイプ
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.charset.name=Charset