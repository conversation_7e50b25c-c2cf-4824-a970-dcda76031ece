meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.ownerName.name = Ім’я власника
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.ownerName.description = Ім’я користувача, який спочатку створив таблицю або вид
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.externalFile.name = Зовнішній файл
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.externalFile.description = Повний шлях до зовнішнього файлу даних, якщо таблиця визначена з використанням клозу EXTERNAL FILE
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.keyLength.name = Довжина ключа
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.keyLength.description = Загальна довжина ключа бази даних.\nДля таблиці: 8 байтів.\nДля виду довжина - це 8, помножено на кількість таблиць, на які посилається вид
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdView.ownerName.name = Ім’я власника
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdSequence.lastValue.name = Останнє значення
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.triggerType.name = Тип
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.sequence.name = Послідовність
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldType.name = Тип
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.defaultSource.name = Значення за замовчуванням
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.computedSource.name = Розраховано за допомогою
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.validationSource.name = Перевірка
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charsetName.name = Кодування
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.subType.name = Підтип
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldLength.name = Довжина
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charLength.name = Довжина символів
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.notNull.name = NOT NULL
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.domainTypeName.name = Тип домену
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.charset.name = Кодування
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.computedDefinition.name = Визначено розрахунково
