
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charLength.name            = Comprimento de caractere
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charsetName.name           = Conjunto de caracteres
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.computedSource.name        = Computado por
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.defaultSource.name         = Valor padrão
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldLength.name           = Comprimento
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldType.name             = Tipo
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.notNull.name               = Não NULL
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.subType.name               = Sub-tipo
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.validationSource.name      = Verificação
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdSequence.lastValue.name             = Último valor
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.externalFile.description      = O caminho completo até o arquivo de dados externo se a tabela for definida com a cláusula EXTERNAL FILE
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.externalFile.name             = Arquivo externo
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.keyLength.description         = O comprimento total da chave do banco de dados.\nPara uma tabela: 8 bytes.\nPara uma view, o comprimento é 8 multiplicado pelo número de tabelas referenciadas pela view
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.keyLength.name                = Comprimento da chave
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.ownerName.description         = O nome do usuário que criou a tabela ou view
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.ownerName.name                = Nome do proprietário
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.charset.name            = Conjunto de caracteres
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.computedDefinition.name = Definição computada
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.domainTypeName.name     = Tipo de domínio
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.sequence.name               = Sequência
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.triggerType.name            = Tipo
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdView.ownerName.name                 = Nome do proprietário
