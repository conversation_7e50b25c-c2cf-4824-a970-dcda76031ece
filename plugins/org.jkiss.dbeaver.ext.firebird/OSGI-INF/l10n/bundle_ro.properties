meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.ownerName.name=Numele proprietarului
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.ownerName.description=Numele de utilizator al utilizatorului care a creat iniţial tabelul sau vederea
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.externalFile.name=Fişier extern
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.externalFile.description=Calea completă către fişierul de date extern, dacă tabelul este definit cu clauza EXTERNAL FILE
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.keyLength.name=Lungimea cheii
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.keyLength.description=Lungimea totală a cheii bazei de date.\nPentru un tabel: 8 octeţi.\nPentru o vizualizare, lungimea este 8 înmulţită cu numărul de tabele la care face referire vederea
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdView.ownerName.name=Numele proprietarului
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdSequence.lastValue.name=Ultima valoare
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.triggerType.name=Tip
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.sequence.name=Secvenţă
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldType.name=Tip
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.defaultSource.name=Valoare implicită
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.computedSource.name=Calculat de
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.validationSource.name=Verificare
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charsetName.name=Set de caractere
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.subType.name=Subtip
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldLength.name=Lungime
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charLength.name=Lungime de caractere
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.notNull.name=Nu este NULL
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.domainTypeName.name=Tip domeniu
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.charset.name=Set de caractere
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.computedDefinition.name=Definiţie calculată
