meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.ownerName.name=Owner name
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.ownerName.description=The user name of the user who created the table or view originally
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.externalFile.name=External file
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.externalFile.description=The full path to the external data file if the table is defined with the EXTERNAL FILE clause
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.keyLength.name=Key length
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTable.keyLength.description=The total length of the database key.\nFor a table: 8 bytes.\nFor a view, the length is 8 multiplied by the number of tables referenced by the view
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdView.ownerName.name=Owner name
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdSequence.lastValue.name=Last value
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.triggerType.name=Type
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTrigger.sequence.name=Sequence
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldType.name=Type
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.defaultSource.name=Default value
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.computedSource.name=Computed by
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.validationSource.name=Check
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charsetName.name=Charset
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.subType.name=Sub Type
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.fieldLength.name=Length
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.charLength.name=Char Length
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdDataType.notNull.name=Not NULL
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.domainTypeName.name=Domain type
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.charset.name=Charset
meta.org.jkiss.dbeaver.ext.firebird.model.FireBirdTableColumn.computedDefinition.name=Computed Definition
