/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2025 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.jkiss.dbeaver.ui.editors.locks;

import org.jkiss.dbeaver.utils.NLS;

public class LocksUIMessages extends NLS {
    private static final String BUNDLE_NAME = "org.jkiss.dbeaver.ui.editors.locks.LocksUIResources"; //$NON-NLS-1$
    public static String actions_refresh_control_kill_waiting_session;
    public static String actions_refresh_control_refresh_locks;
    public static String create_editor_control_name_lock;
    static {
        // initialize resource bundle
        NLS.initializeMessages(BUNDLE_NAME, LocksUIMessages.class);
    }

    private LocksUIMessages() {
    }
}
