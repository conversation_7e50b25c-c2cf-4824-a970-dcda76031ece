Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: DBeaver Altibase Support UI
Bundle-SymbolicName: org.jkiss.dbeaver.ext.altibase.ui;singleton:=true
Bundle-Version: 1.0.23.qualifier
Bundle-Release-Date: 20250901
Require-Bundle: org.eclipse.core.runtime,
 org.eclipse.core.resources,
 org.eclipse.core.expressions,
 org.eclipse.core.filesystem,
 org.eclipse.jface.text,
 org.eclipse.ui,
 org.eclipse.ui.editors,
 org.eclipse.ui.ide,
 org.eclipse.ui.workbench.texteditor,
 org.jkiss.dbeaver.ext.generic,
 org.jkiss.dbeaver.ext.generic.ui,
 org.jkiss.dbeaver.model,
 org.jkiss.dbeaver.model.sql,
 org.jkiss.dbeaver.ui,
 org.jkiss.dbeaver.ui.editors.base,
 org.jkiss.dbeaver.ui.editors.data,
 org.jkiss.dbeaver.ui.navigator,
 org.jkiss.dbeaver.ext.altibase,
 org.jkiss.dbeaver.ui.editors.session,
 org.jkiss.dbeaver.tasks.native.ui,
 org.jkiss.dbeaver.tasks.ui,
 org.jkiss.dbeaver.ui.editors.locks
Bundle-ActivationPolicy: lazy
Bundle-RequiredExecutionEnvironment: JavaSE-21
Bundle-Vendor: DBeaver Corp
Bundle-Localization: OSGI-INF/l10n/bundle
Export-Package: org.jkiss.dbeaver.ext.altibase.ui.views
Automatic-Module-Name: org.jkiss.dbeaver.ext.altibase.ui
