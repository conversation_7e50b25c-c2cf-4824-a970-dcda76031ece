altibase.dialog.connection.header       = Altibase Connection Settings
editor.lock_manager.name                = Lock Manager
editor.package.declaration.name         = Declaration
editor.package.declaration.description  = Declaration source
editor.package.body.name                = Body
editor.package.body.description         = Body source
editor.session_manager.name             = Session Manager
editor.source.name                      = Source
tree.ddl.node.tip                       = Table DDL


page.org.jkiss.dbeaver.preferences.altibase.main.name=Altibase settings

tool.org.jkiss.dbeaver.ext.altibase.ui.tools.maintenance.AltibaseToolMViewRefresh.label     = Refresh materialized view
tool.org.jkiss.dbeaver.ext.altibase.ui.tools.maintenance.AltibaseToolTruncateTable.label    = Truncate table
tool.org.jkiss.dbeaver.ext.altibase.ui.tools.maintenance.AltibaseViewCompile.label          = Compile view
tool.org.jkiss.dbeaver.ext.altibase.ui.tools.maintenance.AltibasePackageCompile.label       = Compile package
tool.org.jkiss.dbeaver.ext.altibase.ui.tools.maintenance.AltibaseProcedureCompile.label     = Compile procedure/function
tool.org.jkiss.dbeaver.ext.altibase.ui.tools.maintenance.AltibaseReplicationStart.label     = Start replication
tool.org.jkiss.dbeaver.ext.altibase.ui.tools.maintenance.AltibaseReplicationStop.label      = Stop replication
