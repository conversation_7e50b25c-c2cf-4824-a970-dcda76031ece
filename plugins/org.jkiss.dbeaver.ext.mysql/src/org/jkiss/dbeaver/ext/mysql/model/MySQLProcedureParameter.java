/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2010-2024 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.jkiss.dbeaver.ext.mysql.model;

import org.jkiss.code.NotNull;
import org.jkiss.dbeaver.model.impl.jdbc.struct.JDBCAttribute;
import org.jkiss.dbeaver.model.meta.Property;
import org.jkiss.dbeaver.model.struct.DBSTypedObject;
import org.jkiss.dbeaver.model.struct.rdb.DBSProcedureParameter;
import org.jkiss.dbeaver.model.struct.rdb.DBSProcedureParameterKind;

/**
 * MySQLProcedureParameter
 */
public class MySQLProcedureParameter extends JDBCAttribute implements DBSProcedureParameter, DBSTypedObject
{
    private MySQLProcedure procedure;
    private DBSProcedureParameterKind parameterKind;

    public MySQLProcedureParameter(
            MySQLProcedure procedure,
            String columnName,
            String typeName,
            int valueType,
            int ordinalPosition,
            long columnSize,
            Integer scale,
            Integer precision,
            boolean notNull,
            DBSProcedureParameterKind parameterKind)
    {
        super(columnName,
            typeName,
            valueType,
            ordinalPosition,
            columnSize,
            scale,
            precision,
            notNull,
            false);
        this.procedure = procedure;
        this.parameterKind = parameterKind;
    }

    @NotNull
    @Override
    public MySQLDataSource getDataSource()
    {
        return procedure.getDataSource();
    }

    @Override
    public MySQLProcedure getParentObject()
    {
        return procedure;
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 10)
    public DBSProcedureParameterKind getParameterKind()
    {
        return parameterKind;
    }

    @NotNull
    @Override
    public DBSTypedObject getParameterType() {
        return this;
    }
}
