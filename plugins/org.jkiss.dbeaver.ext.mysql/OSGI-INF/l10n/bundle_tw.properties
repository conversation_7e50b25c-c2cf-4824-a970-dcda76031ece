# Translated By 2021 <PERSON><PERSON> Lo (r<PERSON><PERSON><PERSON>@gmail.com)
# Translated By 2023 <PERSON><PERSON><PERSON> (<EMAIL>)

tree.databases.node.name =資料庫
tree.databases.node.tip =伺服器資料庫
tree.database.node.name =資料庫
tree.tables.node.name =表格
tree.tables.node.tip =表格
tree.table.node.name =表格
tree.columns.node.name =列
tree.column.node.name =列
tree.constraints.node.name =約束
tree.constraint.node.name =約束
tree.constraint_columns.node.name =約束列
tree.check.constraints.node.name =檢查約束
tree.check.constraint.node.name =檢查約束
tree.foreign_keys.node.name =外鍵
tree.foreign_key.node.name =外鍵
tree.foreign_key_columns.node.name =外鍵列
tree.references.node.name =參考
tree.reference_key.node.name =參考密鑰
tree.reference_key_columns.node.name =參考鍵列
tree.triggers.node.name =觸發器
tree.triggers.node.tip =觸發
tree.trigger.node.name =觸發
tree.indexes.node.name =索引
tree.indexes.node.tip =索引
tree.index.node.name =索引
tree.index_columns.node.name =索引列
tree.partitions.node.name =分區
tree.partition.node.name =分區
tree.subpartitions.node.name =子分區
tree.subpartition.node.name =子分區
tree.views.node.name =檢視
tree.views.node.tip =觀看次數
tree.view.node.name =查看
tree.procedures.node.name =程序
tree.procedures.node.tip =程序
tree.procedure.node.name =程序
tree.users.node.name =使用者
tree.users.node.tip =使用者
tree.user.node.name =使用者
tree.administer.node.name =管理
tree.administer.node.tip =維護/設置
tree.sessions.node.name =會話
tree.privilege.node.name =特權
tree.user_privileges.node.name =使用者權限
tree.system_info.node.name =系統信息
tree.system_info.node.tip =資料庫系統信息
tree.session_status.node.name =會話狀態
tree.variable.node.name =變量
tree.global_status.node.name =全局狀態
tree.session_variables.node.name =會話變量
tree.global_variables.node.name =全局變量
tree.engines.node.name =引擎
tree.engine.node.name =引擎
tree.charsets.node.name =字元集
tree.charset.node.name =字元集
tree.collation.node.name =整理
tree.events.node.name =事件
tree.events.node.tip =事件
tree.event.node.name =事件
tree.packages.node.name =程式包
tree.packages.node.description = MariaDB程式包（Oracle模式）
tree.package.node.name =程式包
tree.sequences.node.name =序列
tree.sequences.node.tip = MariaDB序列

manager.catalog.name =目錄管理員
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.name.name =資料庫名稱
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCharset.name =預設字元集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCollation.name =預設排序規則
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.sqlPath.name = SQL路徑
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.name =資料庫大小
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.description =資料庫大小（資料長度+索引長度，以位元組為單位）
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.name.name =字元集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.defaultCollation.name =預設排序規則
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.maxLength.name =最大長度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.description.name =描述
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.charset.name =字元集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.name.name =整理
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.id.name = Id
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.default.name =預設
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.compiled.name =已編譯
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.sortLength.name =排序長度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.name.name =引擎
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.support.name =支持
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsTransactions.name =支持交易
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsXA.name =支持XA
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsSavepoints.name =支持保存點
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.name.name =名稱
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.value.name =值
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.name.name =分區名稱
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.position.name =位置
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.method.name =方法
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.expression.name =表達式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.description.name =描述
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.tableRows.name =表格行
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.avgRowLength.name =平均行列
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataLength.name =資料透鏡
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.maxDataLength.name =最大資料長度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.indexLength.name =索引列
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataFree.name =資料免費
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.createTime.name =建立時間
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.updateTime.name =更新時間
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checkTime.name =檢查時間
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checksum.name =校驗和
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.comment.name =評論
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.nodegroup.name =節點組
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.name.name =特權
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.context.name =上下文
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.procedureType.name =程序類型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.resultType.name =結果類型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.bodyType.name =身體類型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.body.name =正文
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.clientBody.name = ClientBody
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.name =確定性
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.description =如果例程對於相同的輸入參數始終產生相同的結果，則該例程被視為“確定性”，否則，將其視為“不確定性”。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.declaration.name =聲明
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.objectDefinitionText.name =定義
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedureParameter.parameterKind.name =列類型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.name =引擎
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.name =自動遞增
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.charset.name =字元集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.name =整理
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.description.name =描述
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowCount.name =行數
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.name =平均行長
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.name =資料長度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.name =最大資料長度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataFree.name =資料免費
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.name =索引長度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.name =行格式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.name =建立時間
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.name =更新時間
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.name =檢查時間
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.columnPosition.name =＃
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.fullTypeName.name =資料類型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.maxLength.name =長度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.required.name =不為空
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.autoGenerated.name =自動遞增
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.keyType.name =鍵
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.charset.name =字元集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.collation.name =整理
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.comment.name =評論
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.extraInfo.name =額外
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.name =表達式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.description =虛擬列生成表達式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.ordinalPosition.name =位置
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.attribute.name =列
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableForeignKeyColumn.referencedColumn.name =引用列
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.unique.name =唯一
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.description.name =評論
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.cardinality.name =基數
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.additionalInfo.name =附加
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ordinalPosition.name =位置
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ascending.name =升序
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.nullable.name =可為空
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.subPart.name =額外
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.tableColumn.name =列
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.table.name =表格
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.charsetClient.name =客戶端字元集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.sqlMode.name = SQL模式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.objectDefinitionText.name =定義
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.name.name =使用者名稱
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.host.name =主機名稱
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definition.name =定義
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.checkOption.name =檢查選項
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.updatable.name =可更新
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definer.name = Definer
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.algorithm.name =算法
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.name.name =檢視名稱
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.objectDefinitionText.name =定義
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.name = ID
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.description = SELECT標識符。這是查詢中SELECT的順序號
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.name =選擇類型
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.description =選擇的類型
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.name =表格
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.description =輸出行所引用的表格
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.name =類型
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.description =連接類型
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.name =可能的鍵
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.description =表明MySQL可以從哪些索引中選擇來查找該表中的行
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.name =鍵
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.description = MySQL實際上決定使用的鍵（索引）
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.name =密鑰長度
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.description = MySQL決定使用的密鑰的長度
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.name =參考
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.description =顯示將哪些列或常量與鍵列中命名的索引進行比較以從表中選擇行
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.name =行
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.description = MySQL認為執行查詢必須檢查的行數
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.name =已篩選
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.description =表格條件估計的表格行百分比
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.name =額外
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.description =有關MySQL如何解析查詢的其他信息
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeType.name =類型
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeDisplayName.name =名稱
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeCost.name =成本
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeRowCount.name =行
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.name = PID
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.description =進程ID
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.name =使用者
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.description =資料庫使用者
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.name =主機
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.description =遠端主機
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.name =資料庫
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.description =資料庫
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.name =命令
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.description =當前命令
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.name =時間
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.description =命令開始時間
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.name =狀態
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.description =狀態
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.name =活動查詢
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.description =當前正在執行SQL查詢
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.name.name =名稱
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.description.name =名稱
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.objectDefinitionText.name =程式包宣告
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.extendedDefinitionText.name =程式包本體
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.name.name =事件名稱
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.name =定義者
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.description =建立事件的使用者的帳戶，格式為'user_name'@'host_name'。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.timeZone.name =時區
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventBody.name =身體
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventDefinition.name =定義
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventType.name =類型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.executeAt.name =執行於
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalValue.name =間隔值
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalField.name =間隔字段
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.sqlMode.name = SQL模式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.starts.name =啟動
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.ends.name =結束
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.status.name =狀態
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.onCompletion.name =完成時
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.created.name =已建立
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastAltered.name =已更改
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastExecuted.name =已執行
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.description.name =評論
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.originator.name =發起人
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.characterSetClient.name =字元集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.collationConnection.name =整理（連接）
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.databaseCollation.name =整理（資料庫）
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.objectDefinitionText.name =定義
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.name.name =序列名稱
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cycle.name =是周期
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.startValue.name =起始值
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cache.name =快取大小
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.minValue.name =最小值
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.maxValue.name =最大值
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.incrementBy.name =遞增
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.objectDefinitionText.name = DDL

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableCheckSettings.option.name =檢查選項

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.quick.name =快速
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.extended.name =擴展的
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.useFRM.name =使用FRM

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.object.name =表格名稱
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.messageType.name =狀態
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.messageText.name =消息

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.name =檢查表達式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.description =檢查資料是否滿足給定條件。

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncateSettings.force.name =強制
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncateSettings.force.description =截斷具有外鍵約束的表格