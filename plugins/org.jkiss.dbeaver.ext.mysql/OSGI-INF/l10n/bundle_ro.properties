tree.databases.node.name=Baze de date
tree.databases.node.tip= Baze de date server
tree.database.node.name=Baza de date
tree.tables.node.name=Tabelele
tree.tables.node.tip=Tabelele
tree.table.node.name= Tabel
tree.columns.node.name=Coloane
tree.column.node.name=Coloan<PERSON>
tree.constraints.node.name=Constrângeri
tree.constraint.node.name=Constrângere
tree.constraint_columns.node.name=Coloane de constrângere
tree.check.constraints.node.name=Verificaţi constrângerile
tree.check.constraint.node.name=Verificaţi constrângerea
tree.foreign_keys.node.name=Chei străine
tree.foreign_key.node.name=<PERSON><PERSON>e străină
tree.foreign_key_columns.node.name=Coloane cu cheie străină
tree.references.node.name=Referinţe
tree.reference_key.node.name=Cheie de referinţă
tree.reference_key_columns.node.name=Coloane cheie de referinţă
tree.triggers.node.name=Triggere
tree.triggers.node.tip=Triggere
tree.trigger.node.name=Trigger
tree.indexes.node.name=Indixi
tree.indexes.node.tip=Indixi
tree.index.node.name=Index
tree.index_columns.node.name=Coloane indexate
tree.partitions.node.name=Partiţii
tree.partition.node.name=Partiţie
tree.subpartitions.node.name=Subpartiţii
tree.subpartition.node.name=Subpartiţie
tree.views.node.name=Vederi
tree.views.node.tip=Vederi
tree.view.node.name=Vedere
tree.procedures.node.name=Proceduri
tree.procedures.node.tip=Proceduri
tree.procedure.node.name=Procedură
tree.procedure_columns.node.name=Parametrii procedurii
tree.users.node.name=Utilizatori
tree.users.node.tip=Utilizatori
tree.user.node.name=Utilizator
tree.userGrants.node.name= Granturi
tree.userGrants.node.tip= Permisiuni de utilizator acordate
tree.administer.node.name=Administra
tree.administer.node.tip=Întreţinere/Setări
tree.sessions.node.name=Sesiuni
tree.privilege.node.name=Privilegiu
tree.user_privileges.node.name=Privilegii utilizator
tree.system_info.node.name=Informaţii de sistem
tree.system_info.node.tip=Informaţii despre sistemul bazei de date
tree.session_status.node.name=Starea sesiunii
tree.variable.node.name=Variabilă
tree.global_status.node.name=Stare globală
tree.session_variables.node.name= Variabile de sesiune
tree.global_variables.node.name= Variabile globale
tree.engines.node.name=Motoare
tree.engine.node.name=Motor
tree.charsets.node.name=Charsets
tree.charset.node.name=Charset
tree.plugins.node.name=Pluginuri
tree.plugin.node.name=Plugin
tree.collation.node.name=Triere
tree.events.node.name=Evenimente
tree.events.node.tip=Evenimente
tree.event.node.name=Eveniment
tree.packages.node.name=Pachete
tree.packages.node.description=Pachete MariaDB (modul Oracle)
tree.package.node.name=Pachet
tree.sequences.node.name = Secvenţe
tree.sequences.node.tip = Secvenţe MariaDB

manager.catalog.name=Manager de catalog

parameters.all.caches = Metadatele din cache
parameters.all.caches.tip = Constrângeri de citire a tabelelor şi coloane în etapa de citire a tabelelor.\nAceastă setare poate reduce performanţa de încărcare a metadatelor pentru bazele de date mici şi poate creşte pentru bazele de date mari.

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.name.name=Numele bazei de date
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCharset.name=Set de caractere implicit
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCollation.name=Triere implicită
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.sqlPath.name=Cale SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.name=Dimensiunea bazei de date
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.description=Dimensiunea bazei de date (lungimea datelor + lungimea indexului în octeţi)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.name.name=Charset
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.defaultCollation.name=Triere implicită
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.maxLength.name=Lungimea maximă
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.description.name=Descriere
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.charset.name=Charset
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.name.name=Triere
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.id.name=Id
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.default.name=Implicit
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.compiled.name=Compilat
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.sortLength.name= Sortaţi lungimea
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.name.name=Motor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.support.name=Suport
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsTransactions.name=Acceptă tranzacţii
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsXA.name=Acceptă XA
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsSavepoints.name=Acceptă puncte de salvare
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.name.name=Nume
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.type.name=Type
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.status.name=Stare
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.library.name=Bibliotecă
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.license.name=Licenţă
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.name.name=Nume
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.value.name=Valoare
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.name.name=Nume partiţie
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.position.name=Poziţie
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.method.name=Metoda
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.expression.name=Expresie
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.description.name=Descriere
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.tableRows.name=Rânduri de tabel
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.avgRowLength.name=Lungimea medie a rândului
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataLength.name=Lungimea datelor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.maxDataLength.name=Lungimea maximă a datelor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.indexLength.name=Lungimea Index
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataFree.name=Fără date
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.createTime.name=Creează timp
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.updateTime.name= Ora actualizării
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checkTime.name=Verificaţi Ora
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checksum.name= Sumă de control
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.comment.name=Comentează
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.nodegroup.name=Grup de noduri
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.name.name=Privilegiu
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.context.name=Context
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.procedureType.name=Tipul de procedură
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.resultType.name=Tip de rezultat
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.bodyType.name=Tipul corpului
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.body.name=Corpul
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.clientBody.name=ClientBody
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.name=Determinist
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.description=O rutină este considerată "deterministă" dacă produce întotdeauna acelaşi rezultat pentru aceiaşi parametri de intrare, iar "nu deterministă" în caz contrar.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.declaration.name=Declaraţie
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.objectDefinitionText.name=Definiţie
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedureParameter.parameterKind.name=Tip de coloană
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.name=Motor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.description=Motorul de stocare pentru tabel.\nPentru tabelele partiţionate, Motorul arată numele motorului de stocare folosit de toate partiţiile.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.name= Incrementare automată
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.description=Următoarea valoare AUTO_INCREMENT pentru acest tabel.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.charset.name=Charset
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.name=Triere
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.description=Trierea implicită a tabelului.\nIeşirea nu listează în mod explicit setul de caractere implicit al tabelului, dar numele colării începe cu numele setului de caractere.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.description.name=Descriere
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowCount.name=Număr de rânduri
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.name=Lungimea medie a rândului
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.description=Lungimea medie a rândului.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.name=Lungimea datelor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.description=Pentru MyISAM, Data_length este lungimea fişierului de date, în octeţi.\nPentru InnoDB, Data_length este cantitatea aproximativă de spaţiu alocată pentru indexul grupat, în octeţi.\nMai precis, este dimensiunea indexului grupat, în pagini, înmulţită de dimensiunea paginii InnoDB.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.name=Lungimea maximă a datelor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.description=Pentru MyISAM, Max_data_length este lungimea maximă a fişierului de date.\nAcesta este numărul total de octeţi de date care pot fi stocaţi în tabel, având în vedere dimensiunea pointerului de date utilizată.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataFree.name=Data free
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.name=Lungimea indexului
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.description=Pentru MyISAM, Index_length este lungimea fişierului index, în octeţi.\nPentru InnoDB, Index_length este cantitatea aproximativă de spaţiu alocată pentru indecşii non-cluster, în octeţi.\nMai precis, este suma dimensiunilor indexului non-cluster, în pagini, înmulţit cu dimensiunea paginii InnoDB.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.name=Format de rând
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.description=Formatul de stocare a rândurilor (Fixat, Dinamic, Comprimat, Redundant, Compact).\nPentru tabelele MyISAM, Dynamic corespunde cu ceea ce myisamchk -dvv raportează ca Ambalat.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.name=Timpul creeării
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.description=Când a fost creat tabelul.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.name=Timpul de actualizare
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.description=Când fişierul de date a fost actualizat ultima dată. Pentru unele motoare de stocare, această valoare este NULL.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.name=Verificaţi ora
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.description=Când tabelul a fost verificat ultima dată.\nNu toate motoarele de stocare se actualizează de data aceasta, caz în care, valoarea este întotdeauna NULL.\nPentru tabelele InnoDB partiţionate, Check_time este întotdeauna NULL.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.partitioned.name=Partiţionat
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.partitioned.description=Tabelul are partiţii
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.columnPosition.nume=#
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.fullTypeName.name=Tip de date
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.maxLength.name=Lungime
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.required.name=Nu poate fi nul
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.autoGenerated.name=Increment auto
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.keyType.name=Cheie
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.charset.name=Setul de caractere
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.collation.name=Triere
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.comment.name=Cometariu
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.extraInfo.name=Suplimentar
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.name=Expresie
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.description=Expresie de generare a coloanei virtuale
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.ordinalPosition.name=Poziţie
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.attribute.name=Coloană
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableForeignKeyColumn.referencedColumn.name=Coloana de referinţă
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.unique.name=Unic
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.description.name=Cometariu
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.cardinality.name=Cardinalitatea
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.additionalInfo.name=Adiţional
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ordinalPosition.name=Poziţie
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ascending.name=Ascendent
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.nullable.name=Nulabil
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.subPart.name=Suplimentar
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.tableColumn.name=Coloană
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.table.name=Tabel
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.charsetClient.name=Set de caractere client
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.sqlMode.name=Modul SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.objectDefinitionText.name=Definiţie
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.name.name=Nume de utilizator
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.host.name=Mască gazdă
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxConnections.name=Max conexiuni
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxUserConnections.name=Max conexiuni utilizator
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.sslType.name=Tip SSL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.sslCipher.name=Cifru SSL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxQuestions.name=Max întrebări
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxUpdates.name=Actualizări maxime
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.catalog.name=Catalog
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.table.name=Tabel
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.privilegeNames.name=Privilegii

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definition.name=Definiţie
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.checkOption.name=Verificaţi opţiunea
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.updatable.name=Actualizabil
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definer.name=Definitor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.algorithm.name=Algoritm
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.name.name=Vedeţi numele
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.objectDefinitionText.name=Definiţie
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.name=ID
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.description=Identificatorul SELECT. Acesta este numărul secvenţial al SELECT din interogare
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.name=Selectaţi Tip
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.description=Tipul SELECT
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.name=Tabel
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.description=Tabelul la care se referă rândul de ieşire
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.name=Tip
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.description=Tipul de unire
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.name=Chei posibile
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.description=Indică ce indici poate alege MySQL pentru a găsi rândurile din acest tabel
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.name=Cheie
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.description=Cheie (index) pe care MySQL a decis să o folosească
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.name=Lungimea cheii
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.description=Lungimea cheii pe care MySQL a decis să o folosească
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.name=Ref
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.description=Afişează ce coloane sau constante sunt comparate cu indexul numit în coloana cheie pentru a selecta rândurile din tabel
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.name=Rânduri
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.description=Numărul de rânduri pe care MySQL consideră că trebuie să le examineze pentru a executa interogarea
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.name=Filtrată
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.description=Procentul estimat de rânduri din tabel care vor fi filtrate după condiţia tabelului
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.name=Suplimentar
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.description=Informaţii suplimentare despre modul în care MySQL rezolvă interogarea
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeType.name = Tip
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeDisplayName.name = Nume
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeCost.name = Cost
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeRowCount.name = Rânduri
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.name=PID
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.description=ID proces
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.name=Utilizator
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.description=Utilizator baze de date
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.name=Gazdă
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.description=Gazda la distanţă
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.name=Bază de date
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.description=Baza de date implicită pentru fir, sau NULL dacă nu a fost selectat niciunul.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.name=Comanda
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.description=Comanda curentă
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.name=Timp
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.description=Ora de începere a comenzii
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.name=Stare
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.description=O acţiune, eveniment sau stare care indică ceea ce face firul.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.name=Interogare activă
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.description=În prezent, se execută o interogare SQL
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.source.name=Sursă
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.source.description=Fişierul sursă şi numărul liniei care conţine codul instrumentat care a produs evenimentul.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.progress.name=Progres
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.progress.description=Procentul de lucru finalizat pentru etapele care sprijină raportarea progresului.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.fullScan.name=Scanare completă
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.fullScan.description=Numărul de scanări de tabel complet efectuate de instrucţiunea curentă.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatement.name=Ultima instrucţiune
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatement.description=Ultima instrucţiune executată de fir, dacă nu există nicio instrucţiune în execuţie sau aşteptaţi.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsSent.name=Rânduri trimise
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsSent.description=Numărul de rânduri returnate de instrucţiunea curentă.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpDiskTables.name=Tmp Disk Tables
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpDiskTables.description=Numărul de tabele temporare interne de pe disc create de instrucţiunea curentă.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lockLatency.name=Blocare latenţă
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lockLatency.description=Timpul petrecut în aşteptarea blocărilor de declaraţia curentă.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsExamined.name=Rânduri examinate
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsExamined.description=Numărul de rânduri citite din motoarele de stocare de instrucţiunea curentă.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsAffected.name=Rândurile afectate
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsAffected.description=Numărul de rânduri afectate de instrucţiunea curentă.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpTables.name=Tabelele Tmp
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpTables.description=Numărul de tabele temporare interne în memorie create de instrucţiunea curentă.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.currentMemory.name=Memoria curentă
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.currentMemory.description=Numărul de octeţi alocaţi de fir.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxLatency.name=Latenţa Trx
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxLatency.description=Timpul de aşteptare al tranzacţiei curente pentru fir.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.programName.name=Numele programului
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.programName.description=Numele programului client.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxAutocommit.name=Trx Autocommit
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxAutocommit.description=Dacă modul autocommit a fost activat la începutul tranzacţiei curente.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxState.name=Stare Trx
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxState.description=Starea tranzacţiei curente pentru fir.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.statementLatency.name=Latenţa instrucţiunei
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.statementLatency.description=Cât timp se execută instrucţi.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatementLatency.name=Latenţa ultimei instrucţi
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatementLatency.description=Cât timp a fost executată ultima instrucţiune.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.name.name=Nume
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.description.name=Nume
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.objectDefinitionText.name=Declaraţie de pachet
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.extendedDefinitionText.name=Corpul pachetului

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.name.name=Numele evenimentului
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.name=Definitor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.description=Contul utilizatorului care a creat evenimentul, în format "nume_utilizator"@"nume_gazdă".
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.timeZone.name=Fus orar
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventBody.name=Corp
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventDefinition.name=Definiţie
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventType.name=Tip
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.executeAt.name=Executaţi la
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalValue.name=Valoarea intervalului
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalField.name=Câmp de interval
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.sqlMode.name=Modul SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.starts.name=Începe
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.ends.name=Se termină
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.status.name=Stare
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.onCompletion.name=La finalizare
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.created.name=Creată
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastAltered.name=Modificat
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastExecuted.name=Executat
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.description.name=Cometariu
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.originator.name=Iniţiator
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.characterSetClient.name=Setul de caractere
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.collationConnection.name=Triere (conectare)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.databaseCollation.name=Triere (bază de date)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.objectDefinitionText.name=Definiţie

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.name.name = Numele secvenţei
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cycle.name = Este ciclu
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.startValue.name = Valoarea de pornire
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cache.name = Mărimea cache-ului
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.minValue.name = Valoarea minima
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.maxValue.name = Valoare maximă
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.incrementBy.name = Creşte cu
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.objectDefinitionText.name = DDL

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableCheckSettings.option.name = Verifică opţiunea

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.quick.name = Rapid
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.extended.name = Extins
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.useFRM.name = Folosiţi FRM

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.object.name = Numele tabelului
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.messageType.name = Stare
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.messageText.name = Mesaj

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.name = Verificaţi expresia
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.description = Verifică dacă datele îndeplinesc condiţia dată.

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncateSettings.force.name = Forţează
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncateSettings.force.description = Trunchiaţi tabelul cu constrângeri de cheie străină

mysqlDatabaseRestore.confirmationMessage = Sunteţi pe cale să restauraţi baza de date {0} din {1}.

dashboard.mysql.sessions.label = Sesiuni de server
dashboard.mysql.sessions.description = Afişează sesiunea grupată după comandă
org.jkiss.dbeaver.task.category.mysql.description = Sarcina bazei de date MySQL
org.jkiss.dbeaver.task.category.mysqlTool.name = Instrumente
org.jkiss.dbeaver.task.category.mysqlTool.description = Instrumente de bază de date MySQL
task.mysqlDatabaseBackup.name = Dump MySQL
task.mysqlDatabaseBackup.description = Sarcina de export a bazei de date MySQL
task.mysqlDatabaseRestore.name = Restaurare MySQL
task.mysqlDatabaseRestore.description = Sarcina de import a bazei de date MySQL
task.mysqlScriptExecute.name = Script MySQL
task.mysqlScriptExecute.description = Se execută scriptul MySQL
task.mysqlToolCheckTable.name = Verificaţi tabelul
task.mysqlToolCheckTable.description = Verificaţi tabelele
task.mysqlToolRepairTable.name = Tabel de reparatii
task.mysqlToolRepairTable.description = Tabel(e) de reparaţie
task.mysqlToolAnalyzeTable.name = Analizaţi tabelul
task.mysqlToolAnalyzeTable.description = Analizaţi tabelele
task.mysqlToolOptimizeTable.name = Optimizaţi tabelul
task.mysqlToolOptimizeTable.description = Optimizaţi tabelele
task.mysqlToolTruncateTable.name = Trunchiază tabelul
task.mysqlToolTruncateTable.description = Trunchiaţi tabelele
