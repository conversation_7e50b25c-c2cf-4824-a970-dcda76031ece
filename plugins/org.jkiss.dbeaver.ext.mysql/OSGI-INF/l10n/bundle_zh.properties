# Copyright (C) 2017 <PERSON>, <PERSON><PERSON> (<PERSON><PERSON><PERSON><EMAIL>)
# Copyright (C) 2012 Brook<PERSON>Tran (<EMAIL>)
# Copyright (C) 2018 <PERSON> (<EMAIL>)

dashboard.mysql.sessions.description = 按命令分组显示会话
dashboard.mysql.sessions.label       = 服务器会话

manager.catalog.name = 分类管理

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCharset.name   = 默认字符集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCollation.name = 默认排序规则
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.sqlPath.name          = SQL 路径
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.description             = 数据库大小 (数据长度 + 索引长度，按字节计)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.name                    = 数据库大小
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.name.name                            = 模式名称
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.defaultCollation.name                = 默认排序规则
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.description.name                     = 描述
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.maxLength.name                       = 最大长度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.name.name                            = 字符集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.charset.name                       = 字符集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.compiled.name                      = 编译的
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.default.name                       = 默认
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.id.name                            = Id
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.name.name                          = 排序规则
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.sortLength.name                    = 排序长度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.name.name                             = 引擎
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.support.name                          = 支持
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsSavepoints.name               = 支持保存点
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsTransactions.name             = 支持事务
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsXA.name                       = 支持 XA
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.characterSetClient.name                = 字符集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.collationConnection.name               = 字符排序(连接)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.created.name                           = 已创建
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.databaseCollation.name                 = 字符排序(数据库)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.description                    = 创建事件的用户的账号, 以 'user_name'@'host_name' 的格式.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.name                           = 定义者
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.description.name                       = 注释
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.ends.name                              = 结束
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventBody.name                         = 体
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventDefinition.name                   = 定义
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventType.name                         = 类型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.executeAt.name                         = 执行于
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalField.name                     = 间隔字段
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalValue.name                     = 间隔值
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastAltered.name                       = 变更的
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastExecuted.name                      = 执行的
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.name.name                              = 事件名称
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.objectDefinitionText.name              = 定义
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.onCompletion.name                      = 完成时
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.originator.name                        = 发起者
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.sqlMode.name                           = SQL 模式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.starts.name                            = 开始
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.status.name                            = 状态
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.timeZone.name                          = 时区
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.catalog.name                           = 目录
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.privilegeNames.name                    = 权限
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.table.name                             = 表
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.description.name                     = 名称
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.extendedDefinitionText.name          = 包体
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.name.name                            = 名称
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.objectDefinitionText.name            = 包声明
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.name.name                          = 名称
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.value.name                         = 值
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.avgRowLength.name                  = 平均行长度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checkTime.name                     = 检查时间
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checksum.name                      = 核对合计
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.comment.name                       = 注释
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.createTime.name                    = 创建时间
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataFree.name                      = 已分配但未使用的数据空间大小
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataLength.name                    = 数据长度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.description.name                   = 描述
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.expression.name                    = 表达式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.indexLength.name                   = 索引长度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.maxDataLength.name                 = 最大数据长度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.method.name                        = 方法
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.name.name                          = 分区名称
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.nodegroup.name                     = 节点组
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.position.name                      = 位置
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.tableRows.name                     = 表行
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.updateTime.name                    = 更新时间
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.library.name                          = 库
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.license.name                          = 许可证
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.name.name                             = 名称
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.status.name                           = 状态
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.type.name                             = 类型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.context.name                       = 上下文
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.name.name                          = 权限
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.body.name                          = 体
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.bodyType.name                      = 体类型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.clientBody.name                    = 客户体
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.declaration.name                   = 声明
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.definition.name                    = 定义
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.description          = 一个例程被认为是“确定性”的如果它对于同样的输入参数总是产生同样的结果,“不确定性”的则相反。。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.name                 = 确定性
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.objectDefinitionText.name          = 定义
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.procedureType.name                 = 存储过程类型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.resultType.name                    = 结果类型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedureParameter.parameterKind.name        = 字段类型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cache.name                          = 缓存大小
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cycle.name                          = 是循环
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.incrementBy.name                    = 递增
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.maxValue.name                       = 最大值
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.minValue.name                       = 最小值
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.name.name                           = 序列名称
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.objectDefinitionText.name           = DDL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.startValue.name                     = 起始值
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.name      = 自增
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.name       = 平均行长度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.charset.name            = 字符集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.name          = 检查时间
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.name          = 排序规则
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.name         = 创建时间
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataFree.name           = 已分配但未使用的数据空间大小
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.name         = 数据长度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.description.name        = 描述
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.name             = 引擎
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.name        = 索引长度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.name      = 最大数据长度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowCount.name           = 行计数
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.name          = 行格式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.name         = 更新时间
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.autoGenerated.name               = 自增
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.charset.name                     = 字符集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.collation.name                   = 排序规则
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.columnPosition.name              = #
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.comment.name                     = 注释
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.extraInfo.name                   = 额外的
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.fullTypeName.name                = 数据类型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.description        = 虚拟列生成表达式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.name               = 表达式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.keyType.name                     = 键
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.maxLength.name                   = 长度
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.required.name                    = 非空
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.description      = 检查数据是否满足给定条件。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.name             = 检查表达式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.attribute.name         = 列
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.ordinalPosition.name   = 位置
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableForeignKeyColumn.referencedColumn.name  = 参照字段
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.additionalInfo.name               = 附加
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.cardinality.name                  = 基数
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.description.name                  = 注释
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.unique.name                       = 唯一的
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ascending.name              = 升序
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.nullable.name               = 可为空
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ordinalPosition.name        = 位置
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.subPart.name                = 额外的
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.tableColumn.name            = 列
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.charsetClient.name                   = 客户端字符集
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.objectDefinitionText.name            = 定义
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.sqlMode.name                         = SQL 模式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.table.name                           = 表
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.host.name                               = 主机掩码
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxConnections.name                     = 最大连接数
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxQuestions.name                       = 最大问题数
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxUpdates.name                         = 最大更新数
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxUserConnections.name                 = 最大用户连接数
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.name.name                               = 用户名称
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.sslCipher.name                          = SSL 密码
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.sslType.name                            = SSL 类型
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.algorithm.name           = 算法
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.checkOption.name         = 检查选项
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definer.name             = 定义者
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definition.name          = 定义
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.updatable.name           = 可更新的
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.name.name                               = 视图名称
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.objectDefinitionText.name               = 定义
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeCost.name              = 成本
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeDisplayName.name              = 名称
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeRowCount.name          = 行
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeType.name              = 类型
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.description         = 关于 MySQL 如何解析查询的额外信息
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.name                = 额外的
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.description      = 将会被表条件过滤掉的表行的估计百分比
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.name             = 过滤的
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.description            = SELECT 标识符。这是查询中 SELECT 的顺序号码
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.name                   = ID
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.description           = MySQL 实际上决定使用的键 (索引)
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.name                  = 键
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.description     = MySQL 决定使用的键长度
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.name            = 键长度
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.description      = 联接类型
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.name             = 类型
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.description  = 表名MySQL可以选择的索引用于查找表中的行
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.name         = 可能的键
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.description           = 显示哪些列或常数与命名在键列中的索引相比较来从表中选择行
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.name                  = 参照
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.description      = MySQL 认为必须经检查来执行查询的行数
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.name             = 行
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.description    = SELECT 类型
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.name           = 选择的类型
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.description         = 输出行参照的表
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.name                = 表
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.description      = 当前正在执行的 SQL 查询
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.name             = 活动查询
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.description          = 当前命令
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.name                 = 命令
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.description               = 数据库
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.name                      = 数据库
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.description             = 远程主机
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.name                    = 主机
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.description              = 进程 ID
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.name                     = PID
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.description            = 状态
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.name                   = 状态
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.description             = 命令开始时间
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.name                    = 时间
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.description             = 数据库用户
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.name                    = 用户
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableCheckSettings.option.name           = 检查选项
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.extended.name        = 扩展的
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.quick.name           = 快速
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.useFRM.name          = 使用 FRM
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncateSettings.force.description  = 使用外键约束截断表
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncateSettings.force.name         = 强制
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.messageText.name   = 消息
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.messageType.name   = 状态
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.object.name        = 表名

mysqlDatabaseRestore.confirmationMessage = 您即将从 {1} 恢复数据库 {0}。

org.jkiss.dbeaver.task.category.mysql.description     = MySQL 数据库任务
org.jkiss.dbeaver.task.category.mysqlTool.description = MySQL 数据库工具
org.jkiss.dbeaver.task.category.mysqlTool.name        = 工具

task.mysqlDatabaseBackup.description    = MySQL 数据库导出任务
task.mysqlDatabaseBackup.name           = MySQL 转储
task.mysqlDatabaseRestore.description   = MySQL 数据库导入任务
task.mysqlDatabaseRestore.name          = MySQL 恢复
task.mysqlScriptExecute.description     = MySQL 脚本执行
task.mysqlScriptExecute.name            = MySQL 脚本
task.mysqlToolAnalyzeTable.description  = 分析表
task.mysqlToolAnalyzeTable.name         = 分析表
task.mysqlToolCheckTable.description    = 检查表
task.mysqlToolCheckTable.name           = 检查表
task.mysqlToolOptimizeTable.description = 优化表
task.mysqlToolOptimizeTable.name        = 优化表
task.mysqlToolRepairTable.description   = 修复表
task.mysqlToolRepairTable.name          = 修复表
task.mysqlToolTruncateTable.description = 截断表
task.mysqlToolTruncateTable.name        = 截断表

tree.administer.node.name            = 管理员
tree.administer.node.tip             = 维护/设置
tree.charset.node.name               = 字符集
tree.charsets.node.name              = 字符集
tree.check.constraint.node.name      = 检查约束
tree.check.constraints.node.name     = 检查约束
tree.collation.node.name             = 排序规则
tree.column.node.name                = 列
tree.columns.node.name               = 列
tree.constraint.node.name            = 约束
tree.constraint_columns.node.name    = 约束列
tree.constraints.node.name           = 约束
tree.database.node.name              = 数据库
tree.databases.node.name             = 数据库
tree.databases.node.tip              = 服务器数据库
tree.engine.node.name                = 引擎
tree.engines.node.name               = 引擎
tree.event.node.name                 = 事件
tree.events.node.name                = 事件
tree.events.node.tip                 = 事件
tree.foreign_key.node.name           = 外键
tree.foreign_key_columns.node.name   = 外键列
tree.foreign_keys.node.name          = 外键
tree.global_status.node.name         = 全局状态
tree.global_variables.node.name      = 全局变量
tree.index.node.name                 = 索引
tree.index_columns.node.name         = 索引列
tree.indexes.node.name               = 索引
tree.indexes.node.tip                = 索引
tree.package.node.name               = 包
tree.packages.node.description       = MariaDB 软件包 (Oracle 模式)
tree.packages.node.name              = 包
tree.partition.node.name             = 分区
tree.partitions.node.name            = 分区
tree.plugin.node.name                = 插件
tree.plugins.node.name               = 插件
tree.privilege.node.name             = 权限
tree.procedure.node.name             = 存储过程
tree.procedure_columns.node.name     = 存储过程参数
tree.procedures.node.name            = 存储过程
tree.procedures.node.tip             = 程序
tree.reference_key.node.name         = 引用键
tree.reference_key_columns.node.name = 引用键列
tree.references.node.name            = 引用
tree.sequences.node.name             = 序列
tree.sequences.node.tip              = MariaDB 序列
tree.session_status.node.name        = 会话状态
tree.session_variables.node.name     = 会话变量
tree.sessions.node.name              = 会话
tree.subpartition.node.name          = 子分区
tree.subpartitions.node.name         = 子分区
tree.system_info.node.name           = 系统信息
tree.system_info.node.tip            = 数据库系统信息
tree.table.node.name                 = 表
tree.tables.node.name                = 表
tree.tables.node.tip                 = 表
tree.trigger.node.name               = 触发器
tree.triggers.node.name              = 触发器
tree.triggers.node.tip               = 触发器
tree.user.node.name                  = 用户
tree.userGrants.node.name            = 授权
tree.userGrants.node.tip             = 用户权限授权
tree.user_privileges.node.name       = 用户权限
tree.users.node.name                 = 用户
tree.users.node.tip                  = 用户
tree.variable.node.name              = 变量
tree.view.node.name                  = 视图
tree.views.node.name                 = 视图
tree.views.node.tip                  = 视图
parameters.all.caches=缓存元数据
parameters.all.caches.tip=在读取表的阶段读取表的约束和列。\n此设置可能会减少小型数据库的元数据加载性能，并增加大型数据库的性能。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.description=表的存储引擎。\n对于分区表，Engine 显示所有分区使用的存储引擎名称。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.description=此表的下一个 AUTO_INCREMENT 值。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.description=表的默认排序规则。\n输出中没有显式列出表的默认字符集，但排序规则名称以字符集名称开头。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.description=平均行长度。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.description=对于MyISAM，Data_length是数据文件的长度，单位为字节。\\对于InnoDB，Data_length是为聚集索引分配的大致空间量，单位为字节。\n具体来说，它是聚集索引大小（以页为单位）乘以InnoDB页大小。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.description=对于MyISAM，Max_data_length是数据文件的最大长度。\n这是给定使用的数据指针大小，可以存储在表中的数据总字节数。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.description=对于MyISAM，Index_length是索引文件的长度，单位为字节。\\对于InnoDB，Index_length是为非聚集索引分配的大致空间量，单位为字节。\n具体来说，它是以页为单位的非聚集索引大小的总和乘以InnoDB页大小。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.description=当表创建时。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.description=数据文件最后更新时间。对于某些存储引擎，此值为NULL。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.description=表最后一次检查的时间。\n并非所有存储引擎都会更新此时间，在这种情况下，值始终为 NULL。\n对于分区的 InnoDB 表，Check_time 始终为 NULL。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.partitioned.name=已分区
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.fullScan.name=全扫描
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.partitioned.description=表具有分区
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.fullScan.description=当前语句执行的完整表扫描次数。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatement.name=最后一条语句
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatement.description=如果当前没有正在执行的语句或等待，则为该线程最后执行的语句。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsSent.name=已发送行
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsSent.description=当前语句返回的行数。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpDiskTables.name=临时磁盘表
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpDiskTables.description=当前语句创建的内部磁盘临时表的数量。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lockLatency.name=锁延迟
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lockLatency.description=当前语句等待锁所花费的时间。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsExamined.name=检查的行数
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsExamined.description=当前语句从存储引擎读取的行数。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsAffected.name=影响行数
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsAffected.description=当前语句影响的行数。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpTables.name=临时表
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpTables.description=当前语句创建的内部内存临时表的数量。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.currentMemory.description=线程分配的字节数。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.currentMemory.name=当前内存
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxLatency.description=当前线程事务的等待时间。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxLatency.name=事务延迟
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.programName.description=客户端程序名称。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.programName.name=程序名称
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxAutocommit.name=事务自动提交
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxAutocommit.description=当前事务开始时是否启用了自动提交模式。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxState.name=事务状态
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxState.description=当前线程事务的状态。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.statementLatency.name=语句延迟
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.statementLatency.description=语句执行了多长时间。
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatementLatency.name=最后一条语句的延迟
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatementLatency.description=最后一条语句执行的时间。