tree.databases.node.name=Базы данных
tree.databases.node.tip=База данных сервера
tree.database.node.name=База данных
tree.tables.node.name=Таблицы
tree.tables.node.tip=Таблицы
tree.table.node.name=Таблица
tree.column.node.name=Колонка
tree.columns.node.name=Колонки
tree.constraints.node.name=Ограничения
tree.constraint.node.name=Ограничение
tree.constraint_columns.node.name=Колонки ограничения
tree.check.constraints.node.name=Check ограничения
tree.check.constraint.node.name=Check ограничение
tree.foreign_keys.node.name=Внешние ключи
tree.foreign_key.node.name=Внешний ключ
tree.foreign_key_columns.node.name=Колонки внешнего ключа
tree.references.node.name=Ссылки
tree.reference_key.node.name=Ключ
tree.reference_key_columns.node.name=Колонки
tree.triggers.node.name=Триггеры
tree.triggers.node.tip=Триггеры
tree.trigger.node.name=Триггер
tree.indexes.node.name=Индексы
tree.indexes.node.tip=Индексы
tree.index.node.name=Индекс
tree.index_columns.node.name=Колонки индекса
tree.partitions.node.name=Разделы
tree.partition.node.name=Раздел
tree.subpartitions.node.name=Подразделы
tree.subpartition.node.name=Подраздел
tree.views.node.name=Представления
tree.views.node.tip=Представления
tree.view.node.name=Представление
tree.procedures.node.name=Процедуры
tree.procedures.node.tip=Процедуры
tree.procedure.node.name=Процедура
tree.procedure_columns.node.name=Параметры процедур
tree.users.node.name=Пользователи
tree.users.node.tip=Пользователи
tree.user.node.name=Пользователь
tree.administer.node.name=Администрирование
tree.administer.node.tip=Обслуживание/Настройки
tree.sessions.node.name=Сессии
tree.privilege.node.name=Привилегия
tree.user_privileges.node.name=Пользовательские привелегии
tree.system_info.node.name=Системная информация
tree.system_info.node.tip=Системная информация базы данных
tree.session_status.node.name=Статус сессии
tree.variable.node.name=Переменная
tree.global_status.node.name=Глобальный статус
tree.session_variables.node.name=Переменные сессии
tree.global_variables.node.name=Глобальные переменные
tree.engines.node.name=Движки
tree.engine.node.name=Движок
tree.charsets.node.name=Наборы символов
tree.charset.node.name=Набор символов
tree.plugins.node.name=Плагины
tree.plugin.node.name=Плагин
tree.collation.node.name=Сопоставление
tree.events.node.name=События
tree.events.node.tip=События
tree.packages.node.name=Пакеты
tree.packages.node.description=Пакеты MariaDB (режим Oracle)
tree.sequences.node.name = Последовательности
tree.sequences.node.tip = Последовательности MariaDB

manager.catalog.name=Менеджер каталогов

parameters.all.caches = Кэщировать все методанных
parameters.all.caches.tip = Читать превентивно метаданные ключей и колонок на этапе загрузки таблиц в каталогах.\nВключение этой настройки может увеличить производительность работы с метаданными для больших баз данных и уменьшить для маленьких баз.

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.name.name=Название базы данных
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCharset.name=Кодировка по умолчанию
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCollation.name=Представление по умолчанию
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.sqlPath.name=SQL путь
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.name=Размер
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.description=Размер базы данных (длина данных + длина индекса в байтах)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.name.name=Кодировка
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.maxLength.name=Макс.длина
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.description.name=Описание
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.charset.name=Набор символов
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.default.name=По умолчанию
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.compiled.name=Скомпилирован
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.sortLength.name=Длина сортировки
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.name.name=Сопоставление
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.name.name=Движок
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.support.name=Поддержка
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsTransactions.name=Поддержка транзакций
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsXA.name=Поддержка ХА
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsSavepoints.name=Поддержка точек сохранения
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.name.name=Имя
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.value.name=Значение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.name.name=Имя раздела
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.position.name=Позиция
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.method.name=Метод
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.expression.name=Выражение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.description.name=Описание
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.tableRows.name=Записи таблицы
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.avgRowLength.name=Средняя длина записи
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataLength.name=Длина данных
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.maxDataLength.name=Наиб.длина данных
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.indexLength.name=Длина индекса
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataFree.name=Свободно
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.createTime.name=Время создания
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.updateTime.name=Время изменения
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checkTime.name=Время проверки
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checksum.name=Контр.сумма
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.comment.name=Комментарий
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.nodegroup.name=Узловая группа
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.name.name=Привелегия
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.context.name=Контекст
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.procedureType.name=Тип процедуры
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.resultType.name=Тип результата
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.bodyType.name=Тип тела
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.body.name=Тело
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.name=Детерминированная
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.description=Операция считается 'deterministic' если она всегда приводит к одинаковому результату при одинаковых исходных параметрах и 'not deterministic' в противном случае.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.objectDefinitionText.name=Определение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedureParameter.parameterKind.name=Тип колонки
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.name=Движок
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.name=Авто-увеличение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.charset.name=Набор символов
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.description.name=Описание
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowCount.name=Кол-во записей
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.name=Средняя длина записи
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.name=Размер
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.name=Время создания
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.name=Макс. длина данных
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataFree.name=Свободно
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.name=Сопоставление
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.typeName.name=Тип данных
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.name=Длина индекса
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.name=Формат строки
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.name=Время обновления
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.name=Время проверки
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.fullTypeName.name=Тип данных
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.maxLength.name=Длина
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.required.name=Not NULL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.autoGenerated.name=Авто-увеличение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.keyType.name=Ключ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.charset.name=Набор символов
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.collation.name=Сопоставление
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.comment.name=Комментарий
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.name=Выражение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.extraInfo.name=Дополнительно
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.description=Выражение формирования вирутальной колонки
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.ordinalPosition.name=Позиция
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.attribute.name=Колонка
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.unique.name=Уникальный
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.description.name=Комментарий
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ordinalPosition.name=Позиция
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ascending.name=По возрастанию
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.tableColumn.name=Колонка
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.nullable.name=Возможен NULL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.cardinality.name=Мощность
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.additionalInfo.name=Доп. информация
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.subPart.name=Дополнительно
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.table.name=Таблица
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.charsetClient.name=Кодировка клиента
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.sqlMode.name=SQL режим
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.objectDefinitionText.name=Определение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.name.name=Имя пользователя
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.host.name=Маска хоста
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.checkOption.name=Опция проверки
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definition.name=Определение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.updatable.name=Обновляемый
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definer.name=Владелец
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.algorithm.name=Алгоритм
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.name.name=Название
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.definition.name=Определение
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.description=Идентификатор SELECT'а. Это последовательный номер SELECT'а в запросе.
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.name=Тип селекта
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.description=Тип селекта
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.name=Таблица
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.name=Тип
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.description=Тип
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.name=Возможные ключи
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.description=Обозначает, какие индексы может использовать MySQL при поиске записей в этой таблице
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.name=Ключ
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.description=Ключ (индекс), который использует MySQL
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.name=Длина ключа
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.description=Длина ключа, который использует MySQL
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.description=Показывает, какие колонки или константы сравниваются с именами в индексе ключевой колонки при выборке записей из таблицы
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.name=Записи
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.name=Отфильтровано
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.description=Оценочный процент числа записей таблицы, которые будут отфильтрованы условием таблицы
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.description=Дополнительная информация о том, как MySQL разрешает запрос
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.description=Идентификатор процесса
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.name=Пользователь
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.description=Пользователь базы данных
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.name=Хост
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.description=Удалённый хост
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.name=База данных
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.description=База данных
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.name=Команда
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.description=Текущая команда
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.name=Время
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.description=Время старта команды
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.name=Состояние
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.description=Состояние
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.name.name=Название
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.description.name=Название пакета

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.name.name=Название
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.name.description=Название события
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.name=Владелец
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.description=Учетная запись пользователя, создавшего событие, в формате 'user_name'@'host_name'.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.timeZone.name=Часовой пояс
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventBody.name=Определение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventDefinition.name=Определение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventType.name=Тип
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.executeAt.name=Выполнить в
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalValue.name=Значение интервала
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalField.name=Поле интервала
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.sqlMode.name=Режим SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.starts.name=Начало
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.ends.name=Завершение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.status.name=Статус
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.onCompletion.name=по завершении
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.created.name=Время создания
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastAltered.name=Время изменения
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastExecuted.name=Время выполнения
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.description.name=Комментарий
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.originator.name=ID сервера
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.characterSetClient.name=Набор символов
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.collationConnection.name=Сопоставление (подключение)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.databaseCollation.name=Сопоставление (база данных)

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.name = Условие ограничения
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.description = Проверяет, соответствуют ли данные заданному условию.

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.name.name = Имя последовательности
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cycle.name = Циклична
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.startValue.name = Стартовое значение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cache.name = Размер кэша
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.minValue.name = Минимальное значение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.maxValue.name = максимальное значение
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.incrementBy.name = Шаг

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.name.name=Имя
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.type.name=Тип
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.status.name=Статус
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.library.name=Библиотека
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.license.name=Лицензия
