tree.databases.node.name=Bases de données
tree.database.node.name=Base de données
tree.tables.node.name=Tables
tree.table.node.name=Table
tree.columns.node.name=Colonnes
tree.column.node.name=Colonne
tree.constraints.node.name=Contraintes
tree.constraint.node.name=Contrainte
tree.constraint_columns.node.name=Colonnes de contraintes
tree.foreign_keys.node.name=Clefs étrangères
tree.foreign_key.node.name=Clef étrangère
tree.foreign_key_columns.node.name=Colonnes de clefs étrangères
tree.references.node.name=Références
tree.reference_key.node.name=Clef de références
tree.reference_key_columns.node.name=Colonnes de clefs de références
tree.triggers.node.name=Triggers
tree.trigger.node.name=Trigger
tree.indexes.node.name=Indexes
tree.index.node.name=Index
tree.index_columns.node.name=Colonnes d'index
tree.partitions.node.name=Partitions
tree.partition.node.name=Partition
tree.subpartitions.node.name=Sous-partitions
tree.subpartition.node.name=Sous-partition
tree.views.node.name=Vues
tree.view.node.name=Vue
tree.procedures.node.name=Procédures
tree.procedure.node.name=Procédure
tree.procedure_columns.node.name = Colonnes de procédures
tree.users.node.name=Utilisateurs
tree.user.node.name=Utilisateur
tree.administer.node.name=Administrer
tree.sessions.node.name=Sessions
tree.privilege.node.name=Privilège
tree.user_privileges.node.name=Priviléges utilisateurs
tree.system_info.node.name=Informations Système
tree.session_status.node.name=Statut de session
tree.variable.node.name=Variable
tree.global_status.node.name=Statut global
tree.session_variables.node.name=Variables de sessions
tree.global_variables.node.name=Variables globales
tree.engines.node.name=Moteurs
tree.engine.node.name=Moteur
tree.charsets.node.name=Encodages
tree.charset.node.name=Encodage
tree.collation.node.name=Collation
tree.events.node.name=Evènements
tree.event.node.name=Evènement
tree.packages.node.name=Paquets
tree.packages.node.description=Paquets MariaDB (mode Oracle)
tree.package.node.name=Paquet

tree.check.constraints.node.name=Vérifier les contraintes
tree.check.constraint.node.name=Colonnes
tree.check.constraint_columns.node.name=Vérifier les contraintes

manager.catalog.name=Gestionnaire de catalogue

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.name.name=Nom du schéma
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCharset.name=Encodage par défaut
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCollation.name=Collation par défaut
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.sqlPath.name=Chemin SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.name=Taille de la base de données
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.description=Taille de la base de données (longueur des données + index en bytes)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.name.name=Encodage
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.defaultCollation.name=Collation par défaut
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.maxLength.name=Longueur Max
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.description.name=Description
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.charset.name=Encodage
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.name.name=Collation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.id.name=Id
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.default.name=Défaut
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.compiled.name=Compilé
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.sortLength.name=Trier longueur
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.name.name=Moteur
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.support.name=Support
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsTransactions.name=Supporte les Transactions
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsXA.name=Supporte XA
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsSavepoints.name=Supporte les points de sauvegarde
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.name.name=Nom
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.value.name=Valeur
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.name.name=Nom de la partition
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.position.name=Position
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.method.name=Méthode
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.expression.name=Expression
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.description.name=Description
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.tableRows.name=Lignes de la table
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.avgRowLength.name=Long. moy. lignes
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataLength.name=Long. donnée
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.maxDataLength.name=Long. max. données
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.indexLength.name=Long. Index
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataFree.name=Data Free
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.createTime.name=Heure de création
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.updateTime.name=Heure de mise à jour
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checkTime.name=Heure de vérification
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checksum.name=Checksum
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.comment.name=Commentaire
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.nodegroup.name=Groupe de nœud
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.name.name=Privilège
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.context.name=Contexte
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.procedureType.name=Type de la procédure
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.resultType.name=Type du résultat
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.bodyType.name=Type de corps
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.body.name=Corps
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.clientBody.name=Corps client
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.name=Déterministe
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.description=Une routine est considérée 'déterministe' si elle produit toujours le même résultat pour les mêmes paramètres en entrée, et 'no déterministe' sinon.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.declaration.name=Déclaration
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.objectDefinitionText.name=Définition
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedureParameter.parameterKind.name=Type de colonne
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.name=Moteur
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.name=Auto-Incrémentation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.charset.name=Encodage
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.name=Collation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.description.name=Description
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowCount.name=Nombre de lignes
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.name=Long. moy. lignes
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.name=Longueur des données
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.name=Longueur Max des données
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataFree.name=Data free
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.name=Longueur Index
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.name=Formattage des lignes
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.name=Heure de création
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.name=Heure de mise à jour
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.name=Heure de vérification
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.columnPosition.name=#
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.fullTypeName.name=Type de donnée
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.maxLength.name=Longueur
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.required.name=Non Null
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.autoGenerated.name=Auto-Incrémentation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.keyType.name=Clef
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.charset.name=Encodage
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.collation.name=Collation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.comment.name=Commentaire
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.extraInfo.name=Extra
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.ordinalPosition.name=Position
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.attribute.name=Colonne
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableForeignKeyColumn.referencedColumn.name=Colonne de référence
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.unique.name=Unique
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.description.name=Commentaire
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.cardinality.name=Cardinalité
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.additionalInfo.name=Additionnel
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ordinalPosition.name=Position
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ascending.name=Ascendant
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.nullable.name=Nullable
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.subPart.name=Extra
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.tableColumn.name=Colonne
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.table.name=Table
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.charsetClient.name=Encodage Client
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.sqlMode.name=Mode SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.objectDefinitionText.name=Définition
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.name.name=Nom d'utilisateur
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.host.name=Masque d'hôte
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definition.name=Définition
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.checkOption.name=Cocher Option
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.updatable.name=Updatable
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definer.name=Definer
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.name.name=Nom de la vue
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.objectDefinitionText.name=Définition
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.name=ID
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.description=Identificateur SELECT. Ceci est le numéro séquentiel du SELECT dans la requête
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.name=Type de sélection
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.description=Type du SELECT
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.name=Table
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.description=La table à laquelle fait référence la ligne en sortie
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.name=Type
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.description=Type de jointure
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.name=Clefs possibles
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.description=Indique quels indexes MySQL peut choisir pour retrouver les lignes dans cette table
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.name=Clef
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.description=Clef (index) que MySQL a décidé d'utiliser
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.name=Longueur de la clef
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.description=Longueur de la clef que MySQL a décidé d'utiliser
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.name=Ref
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.description=Montre quelles colonnes ou contraintes sont comparées à l'index nommé dans la colonne de clef pour sélectionner des lignes dans la table
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.name=Lignes
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.description=Nombre de lignes que MySQL pense devoir examiner pour exécuter la requête
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.name=Filtré
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.description=Pourcentage estimé de lignes que seront filtrées par la condition
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.name=Extra
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.description=Informations complémentaires sur la manière de résoudre la requête ar MySQL
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.name=PID
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.description=ID du processus
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.name=Utilisateur
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.description=Utilisateur de la base de données
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.name=Hôte
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.description=Hôte distant
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.name=Base de données
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.description=Base de données
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.name=Commande
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.description=Commande en cours
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.name=Heure
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.description=Heure de démarrage de la commande
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.name=Etat
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.description=Etat
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.name=Requête active
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.description=Exécution en cours de requête SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.name.name=Nom
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.description.name=Nom
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.objectDefinitionText.name=Déclaration du paquet
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.extendedDefinitionText.name=Corps du paquet

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.name.name=Nom d'évènement
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.name=Definer
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.description=Le compte de l'utilisateur qui a créé l'évènement, au format 'nom_utilisateur'@'nom_hote'.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.timeZone.name=Fuseau horaire
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventBody.name=Corps
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventDefinition.name=Définition
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventType.name=Type
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.executeAt.name=Exécuter à
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalValue.name=Valeur interval
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalField.name=Champ interval
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.sqlMode.name=Mode SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.starts.name=Début
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.ends.name=Fin
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.status.name=Statut
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.onCompletion.name=A la complétion
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.created.name=Créé
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastAltered.name=Altéré
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastExecuted.name=Exécuté
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.description.name=Commenté
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.originator.name=Origine
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.characterSetClient.name=Encodage
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.collationConnection.name=Collation (connexion)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.databaseCollation.name=Collation (base de données)
