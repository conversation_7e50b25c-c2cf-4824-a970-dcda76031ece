tree.databases.node.name=Бази даних
tree.databases.node.tip=Серверні бази даних
tree.database.node.name=База даних
tree.tables.node.name=Таблиці
tree.tables.node.tip=Таблиці
tree.table.node.name=Таблиця
tree.columns.node.name=Стовпці
tree.column.node.name=Стовпець
tree.constraints.node.name=Обмеження
tree.constraint.node.name=Обмеження
tree.constraint_columns.node.name=Стовпці обмеження
tree.check.constraints.node.name=Обмеження CHECK
tree.check.constraint.node.name=Обмеження CHECK
tree.foreign_keys.node.name=Зовнішні ключі
tree.foreign_key.node.name=Зовнішній ключ
tree.foreign_key_columns.node.name=Стовпці зовнішнього ключа
tree.references.node.name=Посилання
tree.reference_key.node.name=К<PERSON>юч посилання
tree.reference_key_columns.node.name=Стовпці ключа посилання
tree.triggers.node.name=Тригери
tree.triggers.node.tip=Тригери
tree.trigger.node.name=Тригер
tree.indexes.node.name=Індекси
tree.indexes.node.tip=Індекси
tree.index.node.name=Індекс
tree.index_columns.node.name=Стовпці індексу
tree.partitions.node.name=Розділи
tree.partition.node.name=Розділ
tree.subpartitions.node.name=Підрозділи
tree.subpartition.node.name=Підрозділ
tree.views.node.name=Перегляди
tree.views.node.tip=Перегляди
tree.view.node.name=Перегляд
tree.procedures.node.name=Процедури
tree.procedures.node.tip=Процедури
tree.procedure.node.name=Процедура
tree.procedure_columns.node.name=Параметри процедури
tree.users.node.name=Користувачі
tree.users.node.tip=Користувачі
tree.user.node.name=Користувач
tree.userGrants.node.name=Дозволи користувача
tree.userGrants.node.tip=Дозволи користувача
tree.administer.node.name=Адміністрування
tree.administer.node.tip=Обслуговування/Налаштування
tree.sessions.node.name=Сесії
tree.privilege.node.name=Привілеї
tree.user_privileges.node.name=Привілеї користувача
tree.system_info.node.name=Системна інформація
tree.system_info.node.tip=Інформація про систему бази даних
tree.session_status.node.name=Статус сесії
tree.variable.node.name=Змінна
tree.global_status.node.name=Глобальний статус
tree.session_variables.node.name=Сесійні змінні
tree.global_variables.node.name=Глобальні змінні
tree.engines.node.name=Двигуни
tree.engine.node.name=Двигун
tree.charsets.node.name=Кодування символів
tree.charset.node.name=Кодування символів
tree.plugins.node.name=Плагіни
tree.plugin.node.name=Плагін
tree.collation.node.name=Спільне порівняння
tree.events.node.name=Події
tree.events.node.tip=Події
tree.event.node.name=Подія
tree.packages.node.name=Пакети
tree.packages.node.description=Пакети MariaDB (режим Oracle)
tree.package.node.name=Пакет
tree.sequences.node.name=Послідовності
tree.sequences.node.tip=Послідовності MariaDB

manager.catalog.name=Менеджер каталогів

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.name.name= Назва бази даних
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCharset.name=Стандартний кодувальник
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCollation.name=Стандартне упорядкування
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.sqlPath.name=Шлях SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.name=Розмір бази даних
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.description=Розмір бази даних (довжина даних + довжина індексу в байтах)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.name.name=Кодувальник
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.defaultCollation.name=Стандартне упорядкування
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.maxLength.name=Максимальна довжина
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.description.name=Опис
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.charset.name=Кодувальник
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.name.name=Упорядкування
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.id.name=Ідентифікатор
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.default.name=Стандартне
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.compiled.name=Скомпільоване
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.name.name=Двигун
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.support.name=Підтримка
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsTransactions.name=Підтримує транзакції
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsXA.name=Підтримує XA
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsSavepoints.name=Підтримує точки збереження
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.name.name=Назва
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.type.name=Тип
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.status.name=Статус
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.library.name=Бібліотека
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.license.name=Ліцензія
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.name.name=Назва
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.value.name=Значення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.name.name=Назва розділу
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.position.name=Позиція
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.method.name=Метод
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.expression.name=Вираз
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.description.name=Опис
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.tableRows.name=Кількість рядків
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.avgRowLength.name=Середня довжина рядка
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataLength.name=Довжина даних
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.maxDataLength.name=Максимальна довжина даних
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.indexLength.name=Довжина індексу
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataFree.name=Вільне місце
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.createTime.name=Час створення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.updateTime.name=Час оновлення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checkTime.name=Час перевірки
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checksum.name=Контрольна сума
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.comment.name=Коментар
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.nodegroup.name=Група вузлів
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.name.name=Привілегія
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.context.name=Контекст
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.procedureType.name=Тип процедури
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.resultType.name=Тип результату
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.bodyType.name=Тип тіла
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.body.name=Тіло
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.clientBody.name=Тіло клієнта
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.name=Детерміновано
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.description=Процедура вважається "детермінованою", якщо вона завжди повертає той самий результат для одних і тих же параметрів введення, і "недетермінованою" в інших випадках.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.declaration.name=Оголошення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.objectDefinitionText.name=Визначення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedureParameter.parameterKind.name=Тип колонки
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.name=Двигун
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.description=Сховище для таблиці.\nДля розділених таблиць, Engine показує ім’я сховища, яке використовується всіма розділами.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.name=Автоматичне збільшення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.description=Наступне значення AUTO_INCREMENT для цієї таблиці.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.charset.name=Кодувальник
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.name=Упорядкування
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.description=Стандартне упорядкування таблиці.\nВихід не вказує явно стандартний набір символів таблиці, але ім’я упорядкування починається з назви набору символів.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.description.name=Опис
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowCount.name=Кількість рядків
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.name=Середня довжина рядка
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.description=Середня довжина рядка.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.name=Довжина даних
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.description=Для MyISAM, Data_length - це довжина файлу даних у байтах.\nДля InnoDB, Data_length - приблизна кількість місця, виділена для кластерного індексу, у байтах.\nКонкретно, це розмір кластерного індексу в сторінках, помножений на розмір сторінки InnoDB.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.name=Максимальна довжина даних
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.description=Для MyISAM, Max_data_length - максимальна довжина файлу даних.\nЦе загальна кількість байтів даних, які можна зберегти в таблиці, при використанні розміру вказівника даних.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataFree.name=Вільне місце
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.name=Довжина індексу
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.description=Для MyISAM, Index_length - це довжина файлу індексу у байтах.\nДля InnoDB, Index_length - приблизна кількість місця, виділена для не кластерних індексів, у байтах.\nКонкретно, це сума розмірів не кластерних індексів в сторінках, помножена на розмір сторінки InnoDB.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.name=Формат рядка
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.description=Формат зберігання рядків (Фіксований, Динамічний, Стиснутий, Зайвий, Компактний).\nДля таблиць MyISAM, Dynamic відповідає тому, що myisamchk -dvv повідомляє як Packed.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.name=Час створення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.description=Коли була створена таблиця.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.name=Час оновлення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.description=Коли був останній раз оновлений файл даних. Для деяких сховищ, це значення дорівнює NULL.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.name=Час перевірки
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.description=Коли була останній раз перевірена таблиця.\nНе всі сховища оновлюють цей час, у такому випадку значення завжди дорівнює NULL.\nДля розділених таблиць InnoDB, Check_time завжди дорівнює NULL.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.partitioned.name=Розділено
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.partitioned.description=Таблиця має розділи
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.columnPosition.name=#
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.fullTypeName.name=Тип даних
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.maxLength.name=Довжина
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.required.name=Обов’язкове
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.autoGenerated.name=Автоінкремент
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.keyType.name=Ключ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.charset.name=Кодування
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.collation.name=Співставлення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.comment.name=Коментар
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.extraInfo.name=Додатково
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.name=Вираз
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.description=Вираз для генерації віртуального стовпця
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.ordinalPosition.name=Позиція
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.attribute.name=Стовпець
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableForeignKeyColumn.referencedColumn.name=Стовпець посилання
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.unique.name=Унікальний
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.description.name=Коментар
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.cardinality.name=Кардинальність
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.additionalInfo.name=Додатково
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ordinalPosition.name=Позиція
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ascending.name=Зростаючий порядок
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.nullable.name=Допускається значення NULL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.subPart.name=Додатково
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.tableColumn.name=Стовпець
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.table.name=Таблиця
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.charsetClient.name=Клієнтське кодування
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.sqlMode.name=SQL-режим
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.objectDefinitionText.name=Визначення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.name.name=Ім’я користувача
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.host.name=Маска хоста
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxConnections.name=Максимальна кількість з’єднань
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxUserConnections.name=Максимальна кількість з’єднань для користувача
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.sslType.name=Тип SSL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.sslCipher.name=Шифр SSL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxQuestions.name=Максимальна кількість запитань
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxUpdates.name=Максимальна кількість оновлень
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.catalog.name=Каталог
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.table.name=Таблиця
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.privilegeNames.name=Привілеї

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definition.name=Визначення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.checkOption.name=Опція перевірки
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.updatable.name=Оновлюваний
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definer.name=Оператор визначення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.algorithm.name=Алгоритм
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.name.name=Назва перегляду
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.objectDefinitionText.name=Визначення
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.name=Ідентифікатор
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.description=Ідентифікатор SELECT. Це послідовний номер SELECT у запиті
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.name=Тип SELECT
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.description=Тип SELECT
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.name=Таблиця
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.description=Таблиця, до якої відноситься рядок виводу
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.name=Тип
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.description=Тип з’єднання
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.name=Можливі ключі
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.description=Вказує, які індекси може використовувати MySQL для пошуку рядків у цій таблиці
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.name=Ключ
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.description=Ключ (індекс), який фактично вирішив використовувати MySQL
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.name=Довжина ключа
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.description=Довжина ключа, який вирішив використовувати MySQL
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.name=Ref
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.description=Показує, які стовпці або константи порівнюються із зазначеним індексом у стовпці ключа для вибору рядків із таблиці
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.name=Рядки
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.description=Кількість рядків, яку MySQL вважає необхідною для перегляду для виконання запиту
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.name=Фільтровані
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.description=Оцінений відсоток рядків таблиці, які будуть відфільтровані умовою таблиці
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.name=Додатково
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.description=Додаткова інформація про те, як MySQL вирішує запит
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeType.name = Тип
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeDisplayName.name = Ім’я
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeCost.name = Вартість
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeRowCount.name = Рядки
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.name=PID
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.description=Ідентифікатор процесу
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.name=Користувач
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.description=Користувач бази даних
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.name=Хост
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.description=Віддалений хост
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.name=База даних
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.description=База даних, яку вибрав потік, або NULL, якщо жодна не була вибрана.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.name=Команда
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.description=Поточна команда
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.name=Час
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.description=Час початку команди
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.name=Стан
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.description=Дія, подія або стан, який показує, що робить потік.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.name=Активний запит
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.description=В даний момент виконуваний SQL-запит
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.source.name=Джерело
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.source.description=Файл і номер рядка, що містять інструментований код, який виробив подію.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.progress.name=Прогрес
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.progress.description=Відсоток завершення роботи для етапів, які підтримують звіт про прогрес.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.fullScan.name=Повний скан
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.fullScan.description=Кількість повних сканів таблиці, виконаних поточним оператором.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatement.name=Остання команда
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatement.description=Остання виконана команда потоку, якщо немає виконуваної команди чи очікування.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsSent.name=Відправлені рядки
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsSent.description=Кількість рядків, повернених поточною командою.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpDiskTables.name=Тимчасові дискові таблиці
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpDiskTables.description=Кількість внутрішніх тимчасових таблиць на диску, створених поточною командою.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lockLatency.name=Затримка блокування
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lockLatency.description=Час очікування блокування поточною командою.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsExamined.name=Переглянуті рядки
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsExamined.description=Кількість рядків, які були прочитані зі сховища поточною командою.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsAffected.name=Змінені рядки
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsAffected.description=Кількість рядків, які зазнали змін від поточної команди.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpTables.name=Тимчасові таблиці
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpTables.description=Кількість внутрішніх тимчасових таблиць у пам’яті, створених поточною командою.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.currentMemory.name=Поточна пам’ять
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.currentMemory.description=Кількість байтів, виділених потоком.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxLatency.name=Затримка транзакції
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxLatency.description=Час очікування поточної транзакції для потоку.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.programName.name=Назва програми
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.programName.description=Назва клієнтської програми.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxAutocommit.name=Автозавершення транзакції
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxAutocommit.description=Чи було увімкнено автозавершення транзакції при початку поточної транзакції.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxState.name=Стан транзакції
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxState.description=Стан поточної транзакції для потоку.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.statementLatency.name=Затримка команди
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.statementLatency.description=Тривалість виконання команди.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatementLatency.name=Затримка останньої команди
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatementLatency.description=Тривалість виконання останньої команди.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.name.name=Назва
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.description.name=Назва
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.objectDefinitionText.name=Оголошення пакета
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.extendedDefinitionText.name=Тіло пакета

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.name.name = Назва події
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.name = Визначник
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.description = Обліковий запис користувача, який створив подію, у форматі 'ім’я_користувача'@'ім’я_хоста'.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.timeZone.name = Часовий пояс
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventBody.name = Тіло
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventDefinition.name = Визначення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventType.name = Тип
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.executeAt.name = Виконати о
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalValue.name = Значення інтервалу
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalField.name = Поле інтервалу
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.sqlMode.name = Режим SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.starts.name = Починається
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.ends.name = Закінчується
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.status.name = Статус
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.onCompletion.name = При завершенні
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.created.name = Створено
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastAltered.name = Змінено
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastExecuted.name = Виконано
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.description.name = Коментар
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.originator.name = Ініціатор
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.characterSetClient.name = Набір символів
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.collationConnection.name = Упорядкування (з’єднання)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.databaseCollation.name = Упорядкування (база даних)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.objectDefinitionText.name = Визначення

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.name.name = Назва послідовності
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cycle.name = Циклічність
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.startValue.name = Початкове значення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cache.name = Розмір кешу
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.minValue.name = Мінімальне значення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.maxValue.name = Максимальне значення
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.incrementBy.name = Збільшувати на
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.objectDefinitionText.name = DDL

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableCheckSettings.option.name = Перевірити опцію

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.quick.name = Швидке
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.extended.name = Розширене
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.useFRM.name = Використовувати FRM

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.object.name = Назва таблиці
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.messageType.name = Статус
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.messageText.name = Повідомлення

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.name = Вираз перевірки
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.description = Перевіряє, чи відповідають дані заданому умові.

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncateSettings.force.name = Примусово
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncateSettings.force.description = Обрізати таблицю з обмеженнями зовнішніх ключів

mysqlDatabaseRestore.confirmationMessage = Ви збираєтеся відновити базу даних {0} з {1}.

dashboard.mysql.sessions.label = Сеанси сервера
dashboard.mysql.sessions.description = Показує сесії, згруповані за командами
org.jkiss.dbeaver.task.category.mysql.description = Завдання бази даних MySQL
org.jkiss.dbeaver.task.category.mysqlTool.name = Інструменти 
org.jkiss.dbeaver.task.category.mysqlTool.description = Інструменти бази даних MySQL
task.mysqlDatabaseBackup.name = Резервне копіювання MySQL
task.mysqlDatabaseBackup.description = Завдання експорту бази даних MySQL
task.mysqlDatabaseRestore.name = Відновлення MySQL
task.mysqlDatabaseRestore.description = Завдання імпорту бази даних MySQL
task.mysqlScriptExecute.name = Виконання скрипта MySQL
task.mysqlScriptExecute.description = Виконання скрипта MySQL
task.mysqlToolCheckTable.name = Перевірка таблиці
task.mysqlToolCheckTable.description = Перевірка таблиць
task.mysqlToolRepairTable.name = Відновлення таблиці
task.mysqlToolRepairTable.description = Відновлення таблиць
task.mysqlToolAnalyzeTable.name = Аналіз таблиці
task.mysqlToolAnalyzeTable.description = Аналіз таблиць
task.mysqlToolOptimizeTable.name = Оптимізація таблиці
task.mysqlToolOptimizeTable.description = Оптимізація таблиць
task.mysqlToolTruncateTable.name = Обрізка таблиці
task.mysqlToolTruncateTable.description = Обрізка таблиць
