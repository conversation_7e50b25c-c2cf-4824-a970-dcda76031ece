tree.databases.node.name=データベース
tree.database.node.name=データベース
tree.tables.node.name=テーブル
tree.tables.node.tip=テーブル
tree.table.node.name=テーブル
tree.columns.node.name=列
tree.column.node.name=カラム
tree.constraints.node.name=制約
tree.constraint.node.name=制約
tree.constraint_columns.node.name=制約列
tree.foreign_keys.node.name=外部キー
tree.foreign_key.node.name=外部キー
tree.foreign_key_columns.node.name=外部キー列
tree.references.node.name=参照
tree.reference_key.node.name=参照キー
tree.reference_key_columns.node.name=参照キー列
tree.triggers.node.name=トリガー
tree.triggers.node.tip=トリガー
tree.trigger.node.name=トリガー
tree.indexes.node.name=インデックス
tree.indexes.node.tip=インデックス
tree.index.node.name=インデックス
tree.index_columns.node.name=インデックス列
tree.partitions.node.name=パーティション
tree.partition.node.name=パーティション
tree.subpartitions.node.name=サブパーティション
tree.subpartition.node.name=サブパーティション
tree.views.node.name=ビュー
tree.views.node.tip=ビュー
tree.view.node.name=ビュー
tree.procedures.node.name=プロシージャ
tree.procedures.node.tip=プロシージャ
tree.procedure.node.name=プロシージャ
tree.procedure_columns.node.name=プロシージャ列
tree.users.node.name=ユーザー
tree.users.node.tip=ユーザー
tree.user.node.name=ユーザー
tree.administer.node.name=管理者
tree.sessions.node.name=セッション
tree.privilege.node.name=特権
tree.user_privileges.node.name=ユーザー特権
tree.system_info.node.name=システム情報
tree.system_info.node.tip=データベースのシステム情報
tree.session_status.node.name=セッションステータス
tree.variable.node.name=変数
tree.global_status.node.name=グローバルステータス
tree.session_variables.node.name=セッション変数
tree.global_variables.node.name=グローバル変数
tree.engines.node.name=エンジン
tree.engine.node.name=エンジン
tree.charsets.node.name=文字セット
tree.charset.node.name=文字セット
tree.plugins.node.name=プラグイン
tree.plugin.node.name=プラグイン
tree.collation.node.name=照合
tree.events.node.name=イベント
tree.events.node.tip=イベント
tree.event.node.name=イベント
tree.packages.node.name=パッケージ
tree.packages.node.description=MariaDBパッケージ（Oracleモード）
tree.package.node.name=パッケージ
tree.sequences.node.name=シーケンス
tree.sequences.node.tip=シーケンス

manager.catalog.name=カタログマネージャー

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.name.name=スキーマ名
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCharset.name=デフォルトの文字セット
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCollation.name=デフォルトの照合順序
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.sqlPath.name=SQLパス
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.name=データベースサイズ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.description=データベースサイズ（データ長+インデックス長（バイト））
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.name.name=文字セット
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.defaultCollation.name=デフォルトの照合順序
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.maxLength.name=最大長
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.description.name=説明
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.charset.name=文字セット
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.name.name=照合
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.id.name=Id
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.default.name=デフォルト
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.compiled.name=コンパイルされた
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.sortLength.name=ソートの長さ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.name.name=エンジン
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.support.name=サポート
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsTransactions.name=トランザクションをサポートする
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsXA.name=XAをサポート
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsSavepoints.name=セーブポイントをサポート
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.name.name=名
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.value.name=値
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.name.name=パーティション名
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.position.name=ポジション
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.method.name=方法
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.expression.name=式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.description.name=説明
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.tableRows.name=テーブルの行
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.avgRowLength.name=平均行
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataLength.name=データ長
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.maxDataLength.name=最大データ長
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.indexLength.name=インデックス長
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataFree.name=データフリー
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.createTime.name=時間を作る
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.updateTime.name=更新時間
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checkTime.name=チェック時間
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checksum.name=チェックサム
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.comment.name=コメント
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.nodegroup.name=ノードグループ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.name.name=特権
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.context.name=コンテキスト
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.procedureType.name=プロシージャタイプ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.resultType.name=結果の種類
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.bodyType.name=ボディタイプ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.body.name=体
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.clientBody.name=ClientBody
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.name=Deterministic
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.description=ルーチンは、同じ入力パラメーターに対して常に同じ結果を生成し、それ以外の場合には「決定的でない」場合は、「決定論的」と見なされます。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.declaration.name=宣言
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.objectDefinitionText.name=定義
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedureParameter.parameterKind.name=列タイプ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.name=エンジン
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.name=オートインクリメント
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.charset.name=文字セット
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.name=照合
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.description.name=説明
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowCount.name=行数
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.name=平均行長
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.name=データ長
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.name=最大データ長
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataFree.name=データフリー
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.name=インデックスの長さ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.name=行形式
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.name=時間を作る
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.name=更新時間
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.name=時間を確認する
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.columnPosition.name=#
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.fullTypeName.name=データ・タイプ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.maxLength.name=長さ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.required.name=NOT NULL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.autoGenerated.name=オートインクリメント
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.keyType.name=キー
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.charset.name=文字セット
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.collation.name=照合
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.comment.name=コメント
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.extraInfo.name=追加情報
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.ordinalPosition.name=ポジション
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.attribute.name=カラム
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableForeignKeyColumn.referencedColumn.name=参照欄
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.unique.name=ユニーク
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.description.name=コメント
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.cardinality.name=カーディナリティ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.additionalInfo.name=追加
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ordinalPosition.name=ポジション
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ascending.name=昇順
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.nullable.name=Nullable
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.subPart.name=追加情報
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.tableColumn.name=カラム
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.table.name=表
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.charsetClient.name=クライアントの文字セット
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.sqlMode.name=SQLモード
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.objectDefinitionText.name=定義
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.name.name=ユーザー名
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.host.name=ホストマスク
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxConnections.name=最大接続数
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxUserConnections.name=最大ユーザ接続数

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definition.name=定義
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.checkOption.name=チェックオプション
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.updatable.name=更新可能
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definer.name=定義者
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.algorithm.name=アルゴリズム
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.name.name=ビュー名
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.objectDefinitionText.name=定義
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.name=ID
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.description=SELECT識別子。これは、クエリ内のSELECTの連続番号です
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.name=タイプの選択
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.description=SELECTのタイプ
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.name=表
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.description=出力行が参照するテーブル
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.name=タイプ
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.description=結合タイプ
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.name=可能なキー
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.description=MySQLがこのテーブルの行の検索に使用できるインデックスを示します
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.name=キー
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.description=MySQLが実際に使用することを決めたキー（インデックス）
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.name=キーの長さ
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.description=MySQLが使用することを決めた鍵の長さ
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.name=Ref
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.description=キー列で指定されたインデックスと比較される列または定数を表示して、テーブルから行を選択します。
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.name=行
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.description=クエリを実行するためにMySQLが検討しなければならない行の数
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.name=フィルタリングされた
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.description=テーブル条件によってフィルタリングされるテーブル行の推定パーセンテージ
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.name=追加情報
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.description=MySQLがクエリを解決する方法に関する追加情報
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.name=PID
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.description=プロセスID
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.name=ユーザー
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.description=データベースユーザー
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.name=ホスト
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.description=リモートホスト
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.name=データベース
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.description=データベース
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.name=コマンド
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.description=現在のコマンド
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.name=時間
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.description=コマンドの開始時刻
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.name=状態
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.description=状態
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.name=アクティブなクエリ
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.description=現在実行中のSQLクエリ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.name.name=名
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.description.name=名
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.objectDefinitionText.name=パッケージ宣言
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.extendedDefinitionText.name=パッケージ本体

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.name.name=イベント名
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.name=定義者
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.description='user_name' @ 'host_name'形式でイベントを作成したユーザーのアカウント。
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.timeZone.name=タイムゾーン
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventBody.name=体
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventDefinition.name=定義
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventType.name=タイプ
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.executeAt.name=実行時
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalValue.name=間隔値
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalField.name=間隔フィールド
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.sqlMode.name=SQLモード
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.starts.name=開始
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.ends.name=終わり
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.status.name=状態
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.onCompletion.name=完了時に
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.created.name=作成した
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastAltered.name=変更されました
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastExecuted.name=実行された
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.description.name=コメント
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.originator.name=ORIGINATOR
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.characterSetClient.name=文字セット
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.collationConnection.name=照合（接続）
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.databaseCollation.name=照合（データベース）
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.objectDefinitionText.name=定義

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.name.name=シーケンス名
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.minValue.name=最小値
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.maxValue.name=最大値
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.objectDefinitionText.name=DDL

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.object.name=テーブル名
