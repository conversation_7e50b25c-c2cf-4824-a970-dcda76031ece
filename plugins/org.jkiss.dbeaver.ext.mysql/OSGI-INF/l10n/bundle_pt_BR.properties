
dashboard.mysql.sessions.description = Exibe sessão agrupada por comando
dashboard.mysql.sessions.label       = Sessões do servidor

manager.catalog.name = Gerenciador de catálogo

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCharset.name       = Conjunto de caracteres padrão
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCollation.name     = Collation padrão
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.sqlPath.name              = Caminho do SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.description                 = Tamanho do banco de dados (comprimento dos dados + comprimento dos índices em bytes)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.name                        = Tamanho do banco de dados
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.name.name                                = Nome do banco de dados
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.defaultCollation.name                    = Collation padrão
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.description.name                         = Descrição
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.maxLength.name                           = Comprimento Máx.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.name.name                                = Conjunto de caracteres
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.charset.name                           = Conjunto de caracteres
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.compiled.name                          = Compilado
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.default.name                           = Padrão
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.id.name                                = Id
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.name.name                              = Collation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.sortLength.name                        = Ordenar comprimento
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.name.name                                 = Motor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.support.name                              = Suporte
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsSavepoints.name                   = Suporta pontos de salvamento
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsTransactions.name                 = Suporta transações
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsXA.name                           = Suporta XA
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.characterSetClient.name                    = Conjunto de caracteres
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.collationConnection.name                   = Collation (conexão)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.created.name                               = Criado
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.databaseCollation.name                     = Collation (banco de dados)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.description                        = A conta do usuário que criou o evento, no formato 'user_name'@'host_name'.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.name                               = Definidor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.description.name                           = Comentário
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.ends.name                                  = Finaliza
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventBody.name                             = Corpo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventDefinition.name                       = Definição
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventType.name                             = Tipo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.executeAt.name                             = Executar em
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalField.name                         = Campo de intervalo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalValue.name                         = Valor do intervalo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastAltered.name                           = Alterado
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastExecuted.name                          = Executado
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.name.name                                  = Nome do evento
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.objectDefinitionText.name                  = Definição
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.onCompletion.name                          = Na conclusão
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.originator.name                            = Originador
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.sqlMode.name                               = Modo do SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.starts.name                                = Começa
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.status.name                                = Status
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.timeZone.name                              = Fuso horário
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.catalog.name                               = Catálogo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.privilegeNames.name                        = Privilégios
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.table.name                                 = Tabela
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.description.name                         = Nome
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.extendedDefinitionText.name              = Corpo do pacote
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.name.name                                = Nome
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.objectDefinitionText.name                = Declaração do pacote
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.name.name                              = Nome
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.value.name                             = Valor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.avgRowLength.name                      = Tamanho médio da linha
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checkTime.name                         = Hora da verificação
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checksum.name                          = Checksum
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.comment.name                           = Comentário
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.createTime.name                        = Hora da criação
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataFree.name                          = Dados livres
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataLength.name                        = Tam. dados
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.description.name                       = Descrição
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.expression.name                        = Expressão
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.indexLength.name                       = Tam. índices
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.maxDataLength.name                     = Tam. máx. dados
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.method.name                            = Método
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.name.name                              = Nome da partição
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.nodegroup.name                         = Grupo de nós
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.position.name                          = Posição
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.tableRows.name                         = Linhas da tabela
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.updateTime.name                        = Hora da atualização
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.library.name                              = Biblioteca
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.license.name                              = Licença
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.name.name                                 = Nome
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.status.name                               = Status
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.type.name                                 = Tipo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.context.name                           = Contexto
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.name.name                              = Privilégio
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.body.name                              = Corpo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.bodyType.name                          = Tipo do corpo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.clientBody.name                        = Corpo do cliente
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.declaration.name                       = Declaração
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.description              = Uma rotina é considerada 'determinística' se sempre produz o mesmo resultado para os mesmos parâmetros de entrada e, caso contrário, não é determinística.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.name                     = Determinístico
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.objectDefinitionText.name              = Definição
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.procedureType.name                     = Tipo de procedimento
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.resultType.name                        = Tipo de resultado
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedureParameter.parameterKind.name            = Tipo de coluna
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cache.name                              = Tamanho do cache
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cycle.name                              = É ciclo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.incrementBy.name                        = Incrementar por
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.maxValue.name                           = Valor máximo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.minValue.name                           = Valor mínimo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.name.name                               = Nome da sequência
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.objectDefinitionText.name               = DDL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.startValue.name                         = Valor inicial
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.description   = O próximo valor AUTO_INCREMENT para esta tabela.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.name          = Auto-incremento
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.description    = O comprimento médio da linha
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.name           = Tam. méd. linha
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.charset.name                = Conjunto de caracteres
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.description       = Última vez em que a tabela foi verificada.\nNem todos os motores de armazenamento atualizam este tempo, e nesses casos o valor é sempre NULL.\nPara tabelas InnoDB particionadas, Check_time é sempre NULL.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.name              = Hora de verificação
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.description       = A collation padrão da tabela.\nA saída não lista explicitamente o conjunto de caracteres padrão da tabela, mas o nome da collation começa com o nome do conjunto de caracteres.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.name              = Collation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.description      = Quando a tabela foi criada.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.name             = Hora da criação
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataFree.name               = Data free
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.description      = Para MyISAM, Data_length é o comprimento do arquivo de dados, em bytes.\nPara InnoDB, Data_length é a quantidade aproximada de espaço alocado para o índice agrupado, em bytes.\nEspecificamente, é o tamanho do índice agrupado, em páginas, multiplicado pelo tamanho de página do InnoDB.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.name             = Comprimento de dados
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.description.name            = Descrição
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.description          = O motor de armazenamento para a tabela.\nPara tabelas particionadas, Motor mostra o nome do motor de armazenamento usado por todas as partições.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.name                 = Motor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.description     = Para MyISAM, Index_length é o comprimento do arquivo de índice, em bytes.\nPara InnoDB, Index_length é a quantidade aproximada de espaço alocado para índices não-agrupados, em bytes.\nEspecificamente, é a soma de tamanhos de índices não-agrupados, em páginas, multiplicada pelo tamanho de página do InnoDB.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.name            = Comprimento de índice
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.description   = Para MyISAM, Max_data_length é o comprimento máximo do arquivo de dados.\nIsto é o número total de bytes de dados que podem ser armazenados na tabela, considerando o tamanho do ponteiro de dados usado.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.name          = Tamanho máximo de dados
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.partitioned.description     = A tabela possui partições
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.partitioned.name            = Particionada
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowCount.name               = Número de linhas
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.description       = O formato de armazenamento de linha (Fixo, Dinâmico, Comprimido, Redundante, Compacto).\nPara tabelas MyISAM, Dinâmico corresponde ao que myisamchk -dvv relata como Embalado.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.name              = Formato de linha
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.description      = Última vez que o arquivo de dados foi atualizado. Para alguns motores de armazenamento, este valor é NULL.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.name             = Hora da atualização
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.autoGenerated.name                   = Auto-incremento
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.charset.name                         = Conjunto de caracteres
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.collation.name                       = Collation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.columnPosition.name                  = #
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.comment.name                         = Comentário
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.extraInfo.name                       = Extra
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.fullTypeName.name                    = Tipo de dados
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.description            = Expressão de geração de coluna virtual
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.name                   = Expressão
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.keyType.name                         = Chave
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.maxLength.name                       = Comprimento
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.required.name                        = Não nulo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.description          = Verifica se os dados atendem à condição informada.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.name                 = Verificar expressão
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.attribute.name             = Coluna
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.ordinalPosition.name       = Posição
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableForeignKeyColumn.referencedColumn.name      = Coluna referenciada
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.additionalInfo.name                   = Adicional
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.cardinality.name                      = Cardinalidade
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.description.name                      = Comentário
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.unique.name                           = Unique
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ascending.name                  = Ascendente
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.nullable.name                   = Anulável
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ordinalPosition.name            = Posição
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.subPart.name                    = Extra
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.tableColumn.name                = Coluna
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.charsetClient.name                       = Conjunto de caracteres do cliente
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.objectDefinitionText.name                = Definição
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.sqlMode.name                             = Modo SQL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.table.name                               = Tabela
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.host.name                                   = Máscara do host
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxConnections.name                         = Máx. de conexões
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxQuestions.name                           = Máx. de perguntas
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxUpdates.name                             = Máx. de atualizações
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxUserConnections.name                     = Máx. de conexões de usuário
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.name.name                                   = Nome do usuário
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.sslCipher.name                              = Cifra SSL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.sslType.name                                = Tipo SSL
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.algorithm.name               = Algorítmo
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.checkOption.name             = Verificar opção
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definer.name                 = Definidor
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definition.name              = Definição
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.updatable.name               = Atualizável
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.name.name                                   = Ver o nome
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.objectDefinitionText.name                   = Definição
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeCost.name                  = Custo
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeDisplayName.name           = Nome
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeRowCount.name              = Linhas
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeType.name                  = Tipo
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.description             = Informações adicionais sobre como o MySQL resolve a consulta
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.name                    = Extra
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.description          = Porcentagem estimada de linhas da tabela que serão filtradas pela condição da tabela
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.name                 = Filtrado
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.description                = O identificador SELECT. Este é o número sequencial do SELECT na consulta
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.name                       = ID
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.description               = Chave (índice) que o MySQL realmente decidiu usar
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.name                      = Chave
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.description         = Comprimento da chave que o MySQL decidiu usar
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.name                = Comprimento da chave
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.description          = O tipo de junção
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.name                 = Tipo
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.description      = Indica quais índices o MySQL pode escolher para encontrar as linhas nesta tabela
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.name             = Chaves possíveis
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.description               = Mostra quais colunas ou constantes são comparadas ao índice nomeado na coluna-chave para selecionar linhas da tabela
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.name                      = Ref
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.description          = Número de linhas que o MySQL acredita que deve examinar para executar a consulta
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.name                 = Linhas
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.description        = O tipo de SELECT
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.name               = Tipo de SELECT
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.description             = A tabela à qual a linha de saída se refere
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.name                    = Tabela
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.description          = Atualmente executando consulta SQL
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.name                 = Consulta ativa
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.description              = Comando atual
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.name                     = Comando
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.currentMemory.description        = O número de bytes alocados pela thread.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.currentMemory.name               = Memória atual
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.description                   = O banco de dados padrão para a thread, ou NULL se nenhum foi selecionado.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.name                          = Banco de dados
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.fullScan.description             = O número de varreduras de tabela completa efetuadas pela declaração atual.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.fullScan.name                    = Scan completo
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.description                 = Host remoto
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.name                        = Host
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatement.description        = A última declaração executada pela thread, se não houver nenhuma declaração em execução ou em espera no momento.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatement.name               = Última declaração
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatementLatency.description = Por quanto tempo a última declaração foi executada.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatementLatency.name        = Latência da última declaração
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lockLatency.description          = O tempo gasto em espera por bloqueios pela declaração atual.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lockLatency.name                 = Latência de bloqueio
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.description                  = ID do processo
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.name                         = PID
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.programName.description          = O nome do programa cliente.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.programName.name                 = Nome do programa
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.progress.description             = O percentual de trabalho completado pelos estágios que suportam relato de progresso.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.progress.name                    = Progresso
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsAffected.description         = O número de linhas afetadas pela declaração atual.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsAffected.name                = Linhas afetadas
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsExamined.description         = O número de linhas lidas dos motores de armazenamento pela declaração atual.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsExamined.name                = Linhas examinadas
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsSent.description             = O número de linhas retornadas pela declaração atual.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsSent.name                    = Linhas enviadas
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.source.description               = O arquivo fonte e o número da linha contendo o código instrumentado que produziu o evento.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.source.name                      = Fonte
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.description                = Uma ação, evento ou estado que indica o que a thread está fazendo.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.name                       = Estado
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.statementLatency.description     = Por quanto tempo a declaração está sendo executada.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.statementLatency.name            = Latência da declaração
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.description                 = Hora de início do comando
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.name                        = Tempo
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpDiskTables.description        = O número de tabelas temporárias internas em disco criadas pela declaração atual.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpDiskTables.name               = Tabelas temp. em disco
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpTables.description            = O número de tabelas temporárias internas em memória criadas pela declaração atual.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpTables.name                   = Tabelas temp.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxAutocommit.description        = Se o modo auto-commit foi habilitado quando a transação atual foi iniciada.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxAutocommit.name               = Auto-commit da transação
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxLatency.description           = O tempo de espera da transação atual pela thread.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxLatency.name                  = Latência da transação
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxState.description             = O estado para a transação atual pela thread.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxState.name                    = Estado da transação
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.description                 = Usuário do banco de dados
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.name                        = Usuário
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableCheckSettings.option.name               = Verificação de opções
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.extended.name            = Extendida
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.quick.name               = Rápida
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.useFRM.name              = Usar FRM
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncateSettings.force.description      = Trunca tabela com constraints de chave estrangeira
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncateSettings.force.name             = Forçar
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.messageText.name       = Mensagem
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.messageType.name       = Status
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.object.name            = Nome da tabela

mysqlDatabaseRestore.confirmationMessage = Você está prestes a restaurar o banco de dados {0} de {1}.

org.jkiss.dbeaver.task.category.mysql.description     = Tarefa de banco de dados MySQL
org.jkiss.dbeaver.task.category.mysqlTool.description = Ferramentas de banco de dados MySQL
org.jkiss.dbeaver.task.category.mysqlTool.name        = Ferramentas

parameters.all.caches     = Cache de metadados
parameters.all.caches.tip = Lê constraints e colunas de tabelas durante a etapa de leitura de tabelas.\nEsta configuração pode reduzir a performance no carregamento de metadados em pequenos bancos de dados e aumentar em grandes bancos de dados.

task.mysqlDatabaseBackup.description    = Tarefa de exportação de banco de dados do MySQL
task.mysqlDatabaseBackup.name           = Dump MySQL
task.mysqlDatabaseRestore.description   = Tarefa de importação de banco de dados do MySQL
task.mysqlDatabaseRestore.name          = Restaurar MySQL
task.mysqlScriptExecute.description     = Execução de script MySQL
task.mysqlScriptExecute.name            = Script MySQL
task.mysqlToolAnalyzeTable.description  = Analisa tabela(s)
task.mysqlToolAnalyzeTable.name         = Analisar tabela
task.mysqlToolCheckTable.description    = Verifica tabela(s)
task.mysqlToolCheckTable.name           = Verificar tabela
task.mysqlToolOptimizeTable.description = Otimiza tabela(s)
task.mysqlToolOptimizeTable.name        = Otimizar tabela
task.mysqlToolRepairTable.description   = Repara tabela(s)
task.mysqlToolRepairTable.name          = Reparar tabela
task.mysqlToolTruncateTable.description = Trunca tabela(s)
task.mysqlToolTruncateTable.name        = Truncar tabela

tree.administer.node.name            = Administrar
tree.administer.node.tip             = Manutenção/Configurações
tree.charset.node.name               = Conjunto de caracteres
tree.charsets.node.name              = Conjuntos de caracteres
tree.check.constraint.node.name      = Verificar constraint
tree.check.constraints.node.name     = Verificar constraints
tree.collation.node.name             = Collation
tree.column.node.name                = Coluna
tree.columns.node.name               = Colunas
tree.constraint.node.name            = Constraint
tree.constraint_columns.node.name    = Colunas de constraints
tree.constraints.node.name           = Constraints
tree.database.node.name              = Banco de dados
tree.databases.node.name             = Bancos de dados
tree.databases.node.tip              = Bancos de dados do servidor
tree.engine.node.name                = Motor
tree.engines.node.name               = Motores
tree.event.node.name                 = Evento
tree.events.node.name                = Eventos
tree.events.node.tip                 = Eventos
tree.foreign_key.node.name           = Chave estrangeira
tree.foreign_key_columns.node.name   = Colunas de chaves estrangeira
tree.foreign_keys.node.name          = Chaves estrangeiras
tree.global_status.node.name         = Status global
tree.global_variables.node.name      = Variáveis globais
tree.index.node.name                 = Índice
tree.index_columns.node.name         = Colunas de índice
tree.indexes.node.name               = Índices
tree.indexes.node.tip                = Índices
tree.package.node.name               = Pacote
tree.packages.node.description       = Pacotes  MariaDB (modo Oracle)
tree.packages.node.name              = Pacotes
tree.partition.node.name             = Partição
tree.partitions.node.name            = Partições
tree.plugin.node.name                = Plugin
tree.plugins.node.name               = Plugins
tree.privilege.node.name             = Privilégio
tree.procedure.node.name             = Procedure
tree.procedure_columns.node.name     = Parâmetros de procedures
tree.procedures.node.name            = Procedures
tree.procedures.node.tip             = Procedures
tree.reference_key.node.name         = Chave de referência
tree.reference_key_columns.node.name = Colunas de chave de referência
tree.references.node.name            = Referências
tree.sequences.node.name             = Sequências
tree.sequences.node.tip              = Sequências MariaDB
tree.session_status.node.name        = Status de sessão
tree.session_variables.node.name     = Variáveis de sessão
tree.sessions.node.name              = Sessões
tree.subpartition.node.name          = Subpartição
tree.subpartitions.node.name         = Subpartições
tree.system_info.node.name           = Informações do sistema
tree.system_info.node.tip            = Informação do sistema de banco de dados
tree.table.node.name                 = Tabela
tree.tables.node.name                = Tabelas
tree.tables.node.tip                 = Tabelas
tree.trigger.node.name               = Trigger
tree.triggers.node.name              = Triggers
tree.triggers.node.tip               = Triggers
tree.user.node.name                  = Usuário
tree.userGrants.node.name            = Grants
tree.userGrants.node.tip             = Grants de permissões de usuário
tree.user_privileges.node.name       = Previlégios de usuário
tree.users.node.name                 = Usuários
tree.users.node.tip                  = Usuários
tree.variable.node.name              = Variável
tree.view.node.name                  = View
tree.views.node.name                 = Views
tree.views.node.tip                  = Views
