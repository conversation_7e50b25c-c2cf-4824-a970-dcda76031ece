tree.databases.node.name=Databases
tree.databases.node.tip=Server databases
tree.database.node.name=Database
tree.tables.node.name=Tables
tree.tables.node.tip=Tables
tree.table.node.name=Table
tree.columns.node.name=Columns
tree.column.node.name=Column
tree.constraints.node.name=Constraints
tree.constraint.node.name=Constraint
tree.constraint_columns.node.name=Constraint columns
tree.check.constraints.node.name=Check constraints
tree.check.constraint.node.name=Check constraint
tree.foreign_keys.node.name=Foreign Keys
tree.foreign_key.node.name=Foreign Key
tree.foreign_key_columns.node.name=Foreign key columns
tree.references.node.name=References
tree.reference_key.node.name=Reference Key
tree.reference_key_columns.node.name=Reference Key columns
tree.triggers.node.name=Triggers
tree.triggers.node.tip=Triggers
tree.trigger.node.name=Trigger
tree.indexes.node.name=Indexes
tree.indexes.node.tip=Indexes
tree.index.node.name=Index
tree.index_columns.node.name=Index columns
tree.partitions.node.name=Partitions
tree.partition.node.name=Partition
tree.subpartitions.node.name=Subpartitions
tree.subpartition.node.name=Subpartition
tree.views.node.name=Views
tree.views.node.tip=Views
tree.view.node.name=View
tree.procedures.node.name=Procedures
tree.procedures.node.tip=Procedures
tree.procedure.node.name=Procedure
tree.procedure_columns.node.name=Procedure parameters
tree.users.node.name=Users
tree.users.node.tip=Users
tree.user.node.name=User
tree.userGrants.node.name=Grants
tree.userGrants.node.tip=User permissions grants
tree.administer.node.name=Administer
tree.administer.node.tip=Maintenance/Settings
tree.sessions.node.name=Sessions
tree.privilege.node.name=Privilege
tree.user_privileges.node.name=User Privileges
tree.system_info.node.name=System Info
tree.system_info.node.tip=Database system information
tree.session_status.node.name=Session Status
tree.variable.node.name=Variable
tree.global_status.node.name=Global Status
tree.session_variables.node.name=Session Variables
tree.global_variables.node.name=Global Variables
tree.engines.node.name=Engines
tree.engine.node.name=Engine
tree.charsets.node.name=Charsets
tree.charset.node.name=Charset
tree.plugins.node.name=Plugins
tree.plugin.node.name=Plugin
tree.collation.node.name=Collation
tree.events.node.name=Events
tree.events.node.tip=Events
tree.event.node.name=Event
tree.packages.node.name=Packages
tree.packages.node.description=MariaDB Packages (Oracle mode)
tree.package.node.name=Package
tree.sequences.node.name = Sequences
tree.sequences.node.tip = MariaDB Sequences

manager.catalog.name=Catalog manager

parameters.all.caches = Cache metadata
parameters.all.caches.tip = Read tables constraints and columns at the stage of reading tables.\nThis setting may reduce metadata loading performance for small databases and increase for large databases.

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.name.name=Database Name
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCharset.name=Default Charset
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.defaultCollation.name=Default Collation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog$AdditionalInfo.sqlPath.name=SQL Path
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.name=Database size
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog.databaseSize.description=Database size (data length + index length in bytes)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.name.name=Charset
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.defaultCollation.name=Default Collation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.maxLength.name=Max length
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCharset.description.name=Description
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.charset.name=Charset
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.name.name=Collation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.id.name=Id
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.default.name=Default
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.compiled.name=Compiled
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLCollation.sortLength.name=Sort length
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.name.name=Engine
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.support.name=Support
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsTransactions.name=Supports Transactions
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsXA.name=Supports XA
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEngine.supportsSavepoints.name=Supports Savepoints
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.name.name=Name
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.type.name=Type
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.status.name=Status
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.library.name=Library
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin.license.name=License
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.name.name=Name
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLParameter.value.name=Value
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.name.name=Partition Name
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.position.name=Position
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.method.name=Method
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.expression.name=Expression
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.description.name=Description
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.tableRows.name=Table Rows
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.avgRowLength.name=Avg Row Len
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataLength.name=Data Len
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.maxDataLength.name=Max Data Len
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.indexLength.name=Index Len
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.dataFree.name=Data Free
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.createTime.name=Create Time
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.updateTime.name=Update Time
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checkTime.name=Check Time
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.checksum.name=Checksum
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.comment.name=Comment
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPartition.nodegroup.name=Node Group
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.name.name=Privilege
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPrivilege.context.name=Context
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.procedureType.name=Procedure Type
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.resultType.name=Result Type
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.bodyType.name=Body Type
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.body.name=Body
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.clientBody.name=ClientBody
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.name=Deterministic
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.deterministic.description=A routine is considered 'deterministic' if it always produces the same result for the same input parameters, and 'not deterministic' otherwise.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.declaration.name=Declaration
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure.objectDefinitionText.name=Definition
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLProcedureParameter.parameterKind.name=Column Type
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.name=Engine
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.engine.description=The storage engine for the table.\nFor partitioned tables, Engine shows the name of the storage engine used by all partitions.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.name=Auto Increment
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.autoIncrement.description=The next AUTO_INCREMENT value for this table.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.charset.name=Charset
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.name=Collation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.collation.description=The table default collation.\nThe output does not explicitly list the table default character set, but the collation name begins with the character set name.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.description.name=Description
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowCount.name=Row Count
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.name=Avg Row Length
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.avgRowLength.description=The average row length.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.name=Data Length
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataLength.description=For MyISAM, Data_length is the length of the data file, in bytes.\nFor InnoDB, Data_length is the approximate amount of space allocated for the clustered index, in bytes.\nSpecifically, it is the clustered index size, in pages, multiplied by the InnoDB page size.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.name=Max data length
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.maxDataLength.description=For MyISAM, Max_data_length is maximum length of the data file.\nThis is the total number of bytes of data that can be stored in the table, given the data pointer size used.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.dataFree.name=Data free
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.name=Index length
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.indexLength.description=For MyISAM, Index_length is the length of the index file, in bytes.\nFor InnoDB, Index_length is the approximate amount of space allocated for non-clustered indexes, in bytes.\nSpecifically, it is the sum of non-clustered index sizes, in pages, multiplied by the InnoDB page size.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.name=Row format
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.rowFormat.description=The row-storage format (Fixed, Dynamic, Compressed, Redundant, Compact).\nFor MyISAM tables, Dynamic corresponds to what myisamchk -dvv reports as Packed.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.name=Create Time
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.createTime.description=When the table was created.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.name=Update time
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.updateTime.description=When the data file was last updated. For some storage engines, this value is NULL.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.name=Check time
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.checkTime.description=When the table was last checked.\nNot all storage engines update this time, in which case, the value is always NULL.\nFor partitioned InnoDB tables, Check_time is always NULL. 
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.partitioned.name=Partitioned
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTable$AdditionalInfo.partitioned.description=Table has partitions
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.columnPosition.name=#
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.fullTypeName.name=Data Type
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.maxLength.name=Length
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.required.name=Not Null
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.autoGenerated.name=Auto Increment
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.keyType.name=Key
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.charset.name=Charset
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.collation.name=Collation
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.comment.name=Comment
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.extraInfo.name=Extra
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.name=Expression
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn.genExpression.description=Virtual column generation expression
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.ordinalPosition.name=Position
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraintColumn.attribute.name=Column
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableForeignKeyColumn.referencedColumn.name=Reference Column
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.unique.name=Unique
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.description.name=Comment
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.cardinality.name=Cardinality
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex.additionalInfo.name=Additional
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ordinalPosition.name=Position
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.ascending.name=Ascending
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.nullable.name=Nullable
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.subPart.name=Extra
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndexColumn.tableColumn.name=Column
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.table.name=Table
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.charsetClient.name=Client Charset
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.sqlMode.name=SQL Mode
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger.objectDefinitionText.name=Definition
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.name.name=User name
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.host.name=Host mask
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxConnections.name=Max connections
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxUserConnections.name=Max user connections
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.sslType.name=SSL type
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.sslCipher.name=SSL cipher
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxQuestions.name=Max questions
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLUser.maxUpdates.name=Max updates
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.catalog.name=Catalog
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.table.name=Table
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLGrant.privilegeNames.name=Privileges

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definition.name=Definition
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.checkOption.name=Check Option
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.updatable.name=Updatable
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.definer.name=Definer
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView$AdditionalInfo.algorithm.name=Algorithm
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.name.name=View Name
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLView.objectDefinitionText.name=Definition
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.name=ID
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.id.description=The SELECT identifier. This is the sequential number of the SELECT within the query
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.name=Select Type
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.selectType.description=The type of SELECT
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.name=Table
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.table.description=The table to which the row of output refers
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.name=Type
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.nodeType.description=The join type
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.name=Possible Keys
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.possibleKeys.description=Indicates which indexes MySQL can choose from use to find the rows in this table
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.name=Key
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.key.description=Key (index) that MySQL actually decided to use
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.name=Key Length
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.keyLength.description=Length of the key that MySQL decided to use
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.name=Ref
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.ref.description=Shows which columns or constants are compared to the index named in the key column to select rows from the table
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.name=Rows
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.rowCount.description=Number of rows MySQL believes it must examine to execute the query
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.name=Filtered
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.filtered.description=Estimated percentage of table rows that will be filtered by the table condition
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.name=Extra
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodePlain.extra.description=Additional information about how MySQL resolves the query
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeType.name = Type
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeDisplayName.name = Name
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeCost.name = Cost
meta.org.jkiss.dbeaver.ext.mysql.model.plan.MySQLPlanNodeJSON.nodeRowCount.name = Rows
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.name=PID
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.pid.description=Process ID
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.name=User
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.user.description=Database user
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.name=Host
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.host.description=Remote host
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.name=Database
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.db.description=The default database for the thread, or NULL if none has been selected.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.name=Command
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.command.description=Current command
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.name=Time
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.time.description=Command start time
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.name=State
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.state.description=An action, event, or state that indicates what the thread is doing.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.name=Active Query
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.activeQuery.description=Currently executing SQL query
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.source.name=Source
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.source.description=The source file and line number containing the instrumented code that produced the event.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.progress.name=Progress
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.progress.description=The percentage of work completed for stages that support progress reporting.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.fullScan.name=Full Scan
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.fullScan.description=The number of full table scans performed by the current statement.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatement.name=Last Statement
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatement.description=The last statement executed by the thread, if there is no currently executing statement or wait.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsSent.name=Rows Sent
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsSent.description=The number of rows returned by the current statement.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpDiskTables.name=Tmp Disk Tables
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpDiskTables.description=The number of internal on-disk temporary tables created by the current statement.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lockLatency.name=Lock Latency
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lockLatency.description=The time spent waiting for locks by the current statement.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsExamined.name=Rows Examined
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsExamined.description=The number of rows read from storage engines by the current statement.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsAffected.name=Rows affected
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.rowsAffected.description=The number of rows affected by the current statement.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpTables.name=Tmp Tables
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.tmpTables.description=The number of internal in-memory temporary tables created by the current statement.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.currentMemory.name=Current Memory
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.currentMemory.description=The number of bytes allocated by the thread.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxLatency.name=Trx Latency
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxLatency.description=The wait time of the current transaction for the thread.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.programName.name=Program Name
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.programName.description=The client program name.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxAutocommit.name=Trx Autocommit
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxAutocommit.description=Whether autocommit mode was enabled when the current transaction started.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxState.name=Trx State
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.trxState.description=The state for the current transaction for the thread.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.statementLatency.name=Statement Latency
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.statementLatency.description=How long the statement has been executing.
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatementLatency.name=Last Statement Latency
meta.org.jkiss.dbeaver.ext.mysql.model.session.MySQLSession.lastStatementLatency.description=How long the last statement executed.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.name.name=Name
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.description.name=Name
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.objectDefinitionText.name=Package Declaration
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLPackage.extendedDefinitionText.name=Package Body

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.name.name=Event Name
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.name=Definer
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.definer.description=The account of the user who created the event, in 'user_name'@'host_name' format.
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.timeZone.name=Time Zone
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventBody.name=Body
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventDefinition.name=Definition
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.eventType.name=Type
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.executeAt.name=Execute At
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalValue.name=Interval Value
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.intervalField.name=Interval Field
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.sqlMode.name=SQL Mode
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.starts.name=Starts
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.ends.name=Ends
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.status.name=Status
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.onCompletion.name=On Completion
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.created.name=Created
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastAltered.name=Altered
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.lastExecuted.name=Executed
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.description.name=Comment
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.originator.name=Originator
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.characterSetClient.name=Charset
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.collationConnection.name=Collation (connection)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.databaseCollation.name=Collation (database)
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLEvent.objectDefinitionText.name=Definition

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.name.name = Sequence name
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cycle.name = Is cycle
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.startValue.name = Start value
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.cache.name = Cache size
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.minValue.name = Minimum value
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.maxValue.name = Maximum value
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.incrementBy.name = Increment by
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLSequence.objectDefinitionText.name = DDL

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableCheckSettings.option.name = Check option

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.quick.name = Quick
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.extended.name = Extended
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepairSettings.useFRM.name = Use FRM

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.object.name = Table name
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.messageType.name = Status
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolWithStatus$ToolStatus.messageText.name = Message

meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.name = Check expression
meta.org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint.checkClause.description = Checks whether the data meets the given condition.

meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncateSettings.force.name = Force
meta.org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncateSettings.force.description = Truncate table with foreign key constraints

mysqlDatabaseRestore.confirmationMessage = You are about to restore database {0} from {1}.

dashboard.mysql.sessions.label = Server sessions
dashboard.mysql.sessions.description = Shows session grouped by command
org.jkiss.dbeaver.task.category.mysql.description = MySQL database task
org.jkiss.dbeaver.task.category.mysqlTool.name = Tools 
org.jkiss.dbeaver.task.category.mysqlTool.description = MySQL database tools
task.mysqlDatabaseBackup.name = MySQL dump
task.mysqlDatabaseBackup.description = MySQL database export task
task.mysqlDatabaseRestore.name = MySQL restore
task.mysqlDatabaseRestore.description = MySQL database import task
task.mysqlScriptExecute.name = MySQL script
task.mysqlScriptExecute.description = MySQL script execute
task.mysqlToolCheckTable.name = Check table
task.mysqlToolCheckTable.description = Check table(s)
task.mysqlToolRepairTable.name = Repair table
task.mysqlToolRepairTable.description = Repair table(s)
task.mysqlToolAnalyzeTable.name = Analyze table
task.mysqlToolAnalyzeTable.description = Analyze table(s)
task.mysqlToolOptimizeTable.name = Optimize table
task.mysqlToolOptimizeTable.description = Optimize table(s)
task.mysqlToolTruncateTable.name = Truncate table
task.mysqlToolTruncateTable.description = Truncate table(s)
