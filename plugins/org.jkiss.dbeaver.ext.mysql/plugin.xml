<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="org.jkiss.dbeaver.dataSourceProvider">
        <datasource
            class="org.jkiss.dbeaver.ext.mysql.MySQLDataSourceProvider"
            description="MySQL connector"
            icon="icons/mysql_icon.png"
            id="mysql"
            label="MySQL"
            dialect="mysql">
            <tree
                  icon="icons/mysql_icon.png"
                  label="MySQL data source"
                  path="mysql">
                <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog" label="%tree.databases.node.name" icon="#folder_schema" description="%tree.databases.node.tip">
                    <items label="%tree.database.node.name" path="database" property="catalogs" icon="#database">
                        <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLTable" label="%tree.tables.node.name" icon="#folder_table" description="%tree.tables.node.tip">
                            <items label="%tree.table.node.name" path="table" property="tables" icon="#table">
                                <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn" label="%tree.columns.node.name" icon="#columns" description="Table columns">
                                    <items label="%tree.column.node.name" path="attribute" property="attributes" icon="#column">
                                    </items>
                                </folder>
                                <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint" label="%tree.constraints.node.name" icon="#constraints" description="Table constraints">
                                    <items label="%tree.constraint.node.name" path="constraint" property="constraints" icon="#unique-key">
                                        <items label="%tree.constraint_columns.node.name" path="column" property="attributeReferences" navigable="false" inline="true" visibleIf="object.constraintType!='CHECK'">
                                        </items>
                                    </items>
                                </folder>
                                <!--<folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLTableCheckConstraint" label="%tree.check.constraints.node.name" icon="#check_constraints" description="Table check constraints" visibleIf = "object.dataSource.supportsCheckConstraints()">
                                    <items label="%tree.check.constraint.node.name" path="constraint" property="checkConstraints" icon="#constraint">
                                    </items>
                                </folder>-->
                                <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLTableForeignKey" label="%tree.foreign_keys.node.name" icon="#foreign-keys" description="Table foreign keys" visibleIf="object.dataSource.info.supportsReferentialIntegrity()">
                                    <items label="%tree.foreign_key.node.name" path="association" property="associations" icon="#foreign-key">
                                        <items label="%tree.foreign_key_columns.node.name" itemLabel="%tree.column.node.name" path="column" property="attributeReferences" navigable="false" inline="true">
                                        </items>
                                    </items>
                                </folder>
                                <folder label="%tree.references.node.name" icon="#references" description="Table references" virtual="true" visibleIf="object.dataSource.info.supportsReferentialIntegrity()">
                                    <items label="%tree.reference_key.node.name" path="referenceKey" property="references" icon="#reference" virtual="true">
                                        <items label="%tree.reference_key_columns.node.name" itemLabel="%tree.column.node.name" path="column" property="attributeReferences" navigable="false" inline="true" virtual="true">
                                        </items>
                                    </items>
                                </folder>
                                <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger" label="%tree.triggers.node.name" icon="#triggers" description="%tree.triggers.node.tip" visibleIf="object.dataSource.supportsTriggers()">
                                    <items label="%tree.trigger.node.name" path="trigger" property="triggers" icon="#trigger">
                                    </items>
                                </folder>
                                <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex" label="%tree.indexes.node.name" icon="#indexes" description="Table indexes">
                                    <items label="%tree.index.node.name" path="index" property="indexes" icon="#index">
                                        <items label="Index columns" itemLabel="%tree.column.node.name" path="column" property="attributeReferences" navigable="false" inline="true">
                                        </items>
                                    </items>
                                </folder>
                                <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLPartition" label="%tree.partitions.node.name" icon="#partitions" description="Table partitions" visibleIf="object.dataSource.supportsPartitions()">
                                    <items label="%tree.partition.node.name" path="partition" property="partitions" icon="#partition">
                                        <items label="%tree.subpartitions.node.name" itemLabel="%tree.subpartition.node.name" path="subpartition" property="subPartitions" navigable="false" inline="true">
                                        </items>
                                    </items>
                                </folder>
                            </items>
                        </folder>
                        <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLView" label="%tree.views.node.name" icon="#folder_view" description="%tree.views.node.tip">
                            <items label="%tree.view.node.name" path="view" property="views" icon="#view">
                                <folder label="%tree.columns.node.name" icon="#columns" description="View columns" >
                                    <items label="%tree.columns.node.name" itemLabel="%tree.column.node.name" path="column" property="attributes" icon="#column">
                                    </items>
                                </folder>
                            </items>
                        </folder>
                        <folder type="org.jkiss.dbeaver.model.struct.rdb.DBSTableIndex" label="%tree.indexes.node.name" icon="#indexes" description="%tree.indexes.node.tip" virtual="true">
                            <items label="%tree.index.node.name" path="index" property="indexes" icon="#index" virtual="true">
                                <items label="Index columns" itemLabel="%tree.column.node.name" path="column" property="attributeReferences" navigable="false" inline="true" virtual="true">
                                </items>
                            </items>
                        </folder>
                        <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure" label="%tree.procedures.node.name" icon="#procedures" description="%tree.procedures.node.tip">
                            <items label="%tree.procedure.node.name" path="procedure" property="procedures" icon="#procedure">
                                <folder label="%tree.procedure_columns.node.name" icon="#columns" description="Procedure columns" >
                                    <items label="%tree.procedure_columns.node.name" itemLabel="%tree.column.node.name" path="column" property="parameters" navigable="false" inline="true">
                                    </items>
                                </folder>
                            </items>
                        </folder>
                        <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLPackage" label="%tree.packages.node.name" icon="#packages" description="%tree.packages.node.description" visibleIf="object.dataSource.mariaDB">
                            <items label="%tree.package.node.name" path="package" property="packages" icon="#package">
                            </items>
                        </folder>
                        <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLSequence" label="%tree.sequences.node.name" icon="#sequences" description="%tree.sequences.node.tip" visibleIf="object.dataSource.supportsSequences()">
                            <items label="%tree.sequences.node.name" path="sequence" property="sequences" icon="#sequence">
                            </items>
                        </folder>
                        <folder type="org.jkiss.dbeaver.model.struct.rdb.DBSTrigger" label="%tree.triggers.node.name" icon="#triggers" description="%tree.triggers.node.tip" virtual="true" visibleIf="object.dataSource.supportsTriggers()">
                            <items label="%tree.trigger.node.name" path="trigger" property="triggers" icon="#trigger" virtual="true">
                            </items>
                        </folder>
                        <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLEvent" label="%tree.events.node.name" icon="#events" description="%tree.events.node.tip" visibleIf="object.dataSource.supportsEvents()">
                            <items label="%tree.event.node.name" path="event" property="events" icon="#event">
                            </items>
                        </folder>
                    </items>
                </folder>
                <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLUser" label="%tree.users.node.name" icon="#folder_user" description="%tree.users.node.tip" visibleIf="connected &amp;&amp; object.dataSource.supportsUserManagement()">
                    <items label="%tree.user.node.name" path="users" property="users" icon="#user">
                        <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLGrant" label="%tree.userGrants.node.name" description="%tree.userGrants.node.tip">
                            <items label="%tree.user.node.name" path="grants" property="grants"/>
                        </folder>
                    </items>
                </folder>
                <folder type="" label="%tree.administer.node.name" icon="#folder_admin" id="folderAdmin"
                        description="%tree.administer.node.tip">
                    <treeContribution category="connectionEditor"/>
                </folder>

                <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLInformation" label="%tree.system_info.node.name" icon="#folder_info"
                        id="folderInfo" description="%tree.system_info.node.tip">
                    <folder label="%tree.session_status.node.name" icon="#info" description="Session status">
                        <items label="%tree.variable.node.name" path="sessionStatus" property="sessionStatus" icon="#info" navigable="false" virtual="true"/>
                    </folder>
                    <folder label="%tree.global_status.node.name" icon="#info" description="Global status">
                        <items label="%tree.variable.node.name" path="globalStatus" property="globalStatus" icon="#info" navigable="false" virtual="true"/>
                    </folder>
                    <folder label="%tree.session_variables.node.name" icon="#info" description="Session variables">
                        <items label="%tree.variable.node.name" path="sessionVariables" property="sessionVariables" icon="#info" navigable="false" virtual="true"/>
                    </folder>
                    <folder label="%tree.global_variables.node.name" icon="#info" description="Global variables">
                        <items label="%tree.variable.node.name" path="globalVariables" property="globalVariables" icon="#info" navigable="false" virtual="true"/>
                    </folder>
                    <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLEngine" label="%tree.engines.node.name" icon="#info" description="Database engines">
                        <items label="%tree.engine.node.name" path="engines" property="engines" icon="#info" navigable="false" virtual="true"/>
                    </folder>
                    <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLCharset" label="%tree.charsets.node.name" icon="#info" description="Database charsets">
                        <items label="%tree.charset.node.name" path="charsets" property="charsets" icon="#info">
                            <items label="%tree.collation.node.name" path="collations" property="collations" icon="#info" navigable="false"/>
                        </items>
                    </folder>
                    <folder type="org.jkiss.dbeaver.model.access.DBAPrivilege" label="%tree.user_privileges.node.name" icon="#info" description="User privileges">
                        <items label="%tree.privilege.node.name" path="privileges" property="privileges" icon="#info" navigable="false" virtual="true"/>
                    </folder>
                    <folder type="org.jkiss.dbeaver.ext.mysql.model.MySQLPlugin" label="%tree.plugin.node.name" icon="#info" description="Database plugins" visibleIf="connected &amp;&amp; object.dataSource.supportsPlugins()">
                        <items label="%tree.plugin.node.name" path="plugins" property="plugins" icon="#info" navigable="false" virtual="true"/>
                    </folder>
                </folder>

            </tree>

            <driver-properties>
                <propertyGroup label="Parameters" description="Custom driver parameters">
                    <property id="cache-meta-data" label="%parameters.all.caches" description="%parameters.all.caches.tip" type="boolean" required="false" defaultValue="true"/>
                </propertyGroup>
            </driver-properties>
            <drivers managable="true">
                <driver id="mysql3"/>
                <driver
                        id="mysql5"
                        label="MySQL 5 (Legacy)"
                        icon="icons/mysql_icon.png"
                        iconBig="icons/mysql_icon_big.png"
                        class="com.mysql.jdbc.Driver"
                        sampleURL="jdbc:mysql://{host}[:{port}]/[{database}]"
                        useURL="false"
                        defaultPort="3306"
                        defaultUser="root"
                        webURL="http://www.mysql.com/products/connector/"
                        propertiesURL="https://dev.mysql.com/doc/connector-j/5.1/en/connector-j-reference-configuration-properties.html"
                        databaseDocumentationSuffixURL="Database-driver-MySQL"
                        description="Driver for MySQL 5.x+"
                        categories="sql">

                    <file type="jar" path="maven:/mysql:mysql-connector-java:RELEASE[5.1.49]" bundle="!drivers.mysql"/>
                    <file type="license" path="licenses/external/lgpl-2.0.txt"/>
                    <file type="jar" path="drivers/mysql/mysql5" bundle="drivers.mysql"/>

                    <property name="connectTimeout" value="20000"/>
                    <property name="rewriteBatchedStatements" value="true"/>
                    <property name="enabledTLSProtocols" value="TLSv1,TLSv1.1,TLSv1.2,TLSv1.3"/>
                    <property name="@dbeaver-default-resultset.maxrows.sql" value="true"/>
                    <property name="@dbeaver-default-resultset.format.datetime.native" value="true"/>
                    <property name="@dbeaver-default-dataformat.type.timestamp.pattern" value="yyyy-MM-dd HH:mm:ss"/>
                </driver>

                <driver
                        id="mysql8"
                        label="MySQL"
                        icon="icons/mysql_icon.png"
                        iconBig="icons/mysql_icon_big.png"
                        class="com.mysql.cj.jdbc.Driver"
                        sampleURL="jdbc:mysql://{host}[:{port}]/[{database}]"
                        useURL="false"
                        defaultPort="3306"
                        defaultUser="root"
                        webURL="https://www.mysql.com/products/connector/"
                        propertiesURL="https://dev.mysql.com/doc/connector-j/en/connector-j-reference-configuration-properties.html"
                        databaseDocumentationSuffixURL="Database-driver-MySQL"
                        description="Driver for MySQL 8 and later"
                        promoted="1"
                        categories="sql">
                    <replace provider="mysql" driver="mysql3"/>

                    <file type="jar" path="maven:/com.mysql:mysql-connector-j:RELEASE[8.2.0]" bundle="!drivers.mysql"/>
                    <file type="license" path="licenses/external/gnu3.txt"/>
                    <file type="jar" path="drivers/mysql/mysql8" bundle="drivers.mysql"/>
                    <property name="connectTimeout" value="20000"/>
                    <property name="rewriteBatchedStatements" value="true"/>
                    <!-- https://dev.mysql.com/doc/connector-j/8.0/en/connector-j-reference-using-ssl.html -->
                    <property name="enabledTLSProtocols" value="TLSv1,TLSv1.1,TLSv1.2,TLSv1.3"/>
                    <property name="@dbeaver-default-resultset.maxrows.sql" value="true"/>
                    <property name="@dbeaver-default-resultset.format.datetime.native" value="true"/>
                    <property name="@dbeaver-default-dataformat.type.timestamp.pattern" value="yyyy-MM-dd HH:mm:ss"/>
                </driver>
                <driver
                    id="mysql_ndb"
                    label="NDB Cluster"
                    icon="icons/mysql_icon.png"
                    iconBig="icons/mysql_icon_big.png"
                    class="com.mysql.jdbc.Driver"
                    sampleURL="**********************://{host}[:{port}]/[{database}]"
                    useURL="false"
                    defaultPort="3306"
                    defaultUser="root"
                    webURL="https://dev.mysql.com/doc/ndbapi/en/mccj-using-connectorj.html"
                    propertiesURL="https://dev.mysql.com/doc/connector-j/en/connector-j-reference-configuration-properties.html"
                    databaseDocumentationSuffixURL="Database-driver-MySQL"
                    description="Driver for MySQL NDB cluster"
                    categories="sql">
                    <file type="jar" path="maven:/com.mysql:mysql-connector-j:RELEASE[8.2.0]" bundle="!drivers.mysql"/>
                    <file type="license" path="licenses/external/gnu3.txt"/>
                    <file type="jar" path="drivers/mysql/mysql8" bundle="drivers.mysql"/>
                    <property name="connectTimeout" value="20000"/>
                    <property name="@dbeaver-default-resultset.maxrows.sql" value="true"/>
                    <property name="@dbeaver-default-resultset.format.datetime.native" value="true"/>
                    <property name="@dbeaver-default-dataformat.type.timestamp.pattern" value="yyyy-MM-dd HH:mm:ss"/>
                </driver>

                <driver
                    id="mariaDB"
                    label="MariaDB"
                    icon="icons/mariadb_icon.png"
                    iconBig="icons/mariadb_icon_big.png"
                    class="org.mariadb.jdbc.Driver"
                    sampleURL="jdbc:mariadb://{host}[:{port}]/[{database}]"
                    defaultPort="3306"
                    defaultUser="root"
                    webURL="https://mariadb.com/kb/en/mariadb/about-mariadb-connector-j/"
                    propertiesURL="https://mariadb.com/kb/en/library/about-mariadb-connector-j/#optional-url-parameters"
                    databaseDocumentationSuffixURL="Database-driver-MariaDB"
                    description="MariaDB JDBC driver"
                    promoted="1"
                    categories="sql">
                    <file type="jar" path="maven:/org.mariadb.jdbc:mariadb-java-client:RELEASE[3.5.2]" bundle="!drivers.mariadb"/>
                    <file type="license" path="licenses/external/lgpl-2.1.txt"/>
                    <file type="jar" path="drivers/mariadb" bundle="drivers.mariadb"/>

                    <property name="@dbeaver-default-resultset.maxrows.sql" value="true"/>
                    <!--<property name="@dbeaver-default-resultset.format.datetime.native" value="true"/>-->
                    <parameter name="krb5.enabled" value="true"/>
                </driver>
                <driver
                        id="starRocks"
                        label="StarRocks"
                        icon="icons/starRocks_icon.png"
                        iconBig="icons/starRocks_icon_big.png"
                        logoImage="icons/starRocks_logo.png"
                        class="com.mysql.cj.jdbc.Driver"
                        sampleURL="jdbc:mysql://{host}[:{port}]/[{database}]"
                        useURL="false"
                        defaultPort="9030"
                        defaultUser="root"
                        webURL="https://docs.starrocks.io/docs/quick_start/shared-nothing/"
                        description="Driver for MySQL 8 and StarRocks"
                        categories="sql">

                    <file type="jar" path="maven:/com.mysql:mysql-connector-j:RELEASE[8.3.0]" bundle="!drivers.mysql"/>
                    <file type="license" path="licenses/external/gnu3.txt"/>
                    <file type="jar" path="drivers/mysql/mysql8" bundle="drivers.mysql"/>
                    <property name="connectTimeout" value="20000"/>
                    <parameter name="supports-partitions" value="false"/>
                    <parameter name="supports-references" value="false"/>
                    <parameter name="supports-triggers" value="false"/>
                    <parameter name="supports-events" value="false"/>
                    <parameter name="supports-users" value="false"/>
                    <parameter name="supports-charsets" value="false"/>
                    <parameter name="supports-collations" value="false"/>
                    <parameter name="supportsClients" value="false"/>
                </driver>
                <provider-properties drivers="*">
                    <propertyGroup label="Advanced">
                        <property id="@dbeaver-serverTimezone@" label="Server Time Zone" type="string"/>
                    </propertyGroup>
                </provider-properties>
            </drivers>

            <nativeClients>
                <client id="mariadb_client" label="MariaDB Binaries">
                    <dist os="win32" targetPath="clients/mariadb/win" remotePath="repo:/drivers/mariadb/client/win" resourcePath="clients/mariadb/win">
                        <driver id="mariaDB"/>
                        <file type="exec" name="mariadb.exe"/>
                        <file type="exec" name="mariadb-dump.exe"/>
                    </dist>
                </client>
                <client id="mysql_client" label="MySQL Binaries">
                    <dist os="win32" targetPath="clients/mysql_8/win" remotePath="repo:/drivers/mysql/client_8/win" resourcePath="clients/mysql_8/win">
                        <file type="exec" name="mysql.exe"/>
                        <file type="exec" name="mysqldump.exe"/>
                        <file type="lib" name="libssl-1_1-x64.dll"/>
                        <file type="lib" name="libcrypto-1_1-x64.dll"/>
                    </dist>
                </client>
                <client id="mysql5_client" label="MySQL 5 Binaries">
                    <dist os="win32" targetPath="clients/mysql/win" remotePath="repo:/drivers/mysql/client/win" resourcePath="clients/mysql/win">
                        <file type="exec" name="mysql.exe"/>
                        <file type="exec" name="mysqldump.exe"/>
                    </dist>
                </client>
            </nativeClients>
        </datasource>
    </extension>

    <extension point="org.jkiss.dbeaver.objectManager">
        <manager class="org.jkiss.dbeaver.ext.mysql.edit.MySQLDatabaseManager" objectType="org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog" label="%manager.catalog.name"/>
        <manager class="org.jkiss.dbeaver.ext.mysql.edit.MySQLTableManager" objectType="org.jkiss.dbeaver.ext.mysql.model.MySQLTable"/>
        <manager class="org.jkiss.dbeaver.ext.mysql.edit.MySQLTableColumnManager" objectType="org.jkiss.dbeaver.ext.mysql.model.MySQLTableColumn"/>
        <manager class="org.jkiss.dbeaver.ext.mysql.edit.MySQLConstraintManager" objectType="org.jkiss.dbeaver.ext.mysql.model.MySQLTableConstraint"/>
        <manager class="org.jkiss.dbeaver.ext.mysql.edit.MySQLForeignKeyManager" objectType="org.jkiss.dbeaver.ext.mysql.model.MySQLTableForeignKey"/>
        <manager class="org.jkiss.dbeaver.ext.mysql.edit.MySQLIndexManager" objectType="org.jkiss.dbeaver.ext.mysql.model.MySQLTableIndex"/>
        <manager class="org.jkiss.dbeaver.ext.mysql.edit.MySQLViewManager" objectType="org.jkiss.dbeaver.ext.mysql.model.MySQLView"/>
        <manager class="org.jkiss.dbeaver.ext.mysql.edit.MySQLTriggerManager" objectType="org.jkiss.dbeaver.ext.mysql.model.MySQLTrigger"/>
        <manager class="org.jkiss.dbeaver.ext.mysql.edit.MySQLProcedureManager" objectType="org.jkiss.dbeaver.ext.mysql.model.MySQLProcedure"/>
        <manager class="org.jkiss.dbeaver.ext.mysql.edit.MySQLEventManager" objectType="org.jkiss.dbeaver.ext.mysql.model.MySQLEvent"/>
        <manager class="org.jkiss.dbeaver.ext.mysql.edit.MySQLSequenceManager" objectType="org.jkiss.dbeaver.ext.mysql.model.MySQLSequence"/>
        <manager class="org.jkiss.dbeaver.ext.mysql.edit.MySQLPartitionTableManager" objectType="org.jkiss.dbeaver.ext.mysql.model.MySQLPartition"/>
    </extension>

    <extension point="org.jkiss.dbeaver.dataTypeProvider">
        <provider
            class="org.jkiss.dbeaver.ext.mysql.data.MySQLValueHandlerProvider"
            description="MySQL data types provider"
            id="MySQLValueHandlerProvider"
            label="MySQL data types provider">

            <datasource id="mysql"/>
            <type name="FLOAT"/>
            <type name="DOUBLE"/>
            <type name="DOUBLE PRECISION"/>
            <type name="ENUM"/>
            <type name="SET"/>
            <type standard="DATE"/>
            <type standard="TIME"/>
            <type standard="TIMESTAMP"/>
            <type standard="BIT"/>
            <type name="JSON"/>
            <type name="UUID"/>
            <!-- Spatial -->
            <type name="GEOMETRY"/>
            <type name="POINT"/>
            <type name="LINESTRING"/>
            <type name="POLYGON"/>
            <type name="MULTIPOINT"/>
            <type name="MULTILINESTRING"/>
            <type name="MULTIPOLYGON"/>
            <type name="GEOMETRYCOLLECTION"/>
            <type name="GEOGRAPHY"/>
            <type name="GEOGRAPHYPOINT"/>

        </provider>
    </extension>

    <extension point="org.jkiss.dbeaver.networkHandler">
        <handler
                type="config"
                id="mysql_ssl"
                codeName="SSL"
                label="SSL"
                description="Secure socket layer"
                desktop="false"
                secured="false"
                order="2"
                handlerClass="org.jkiss.dbeaver.ext.mysql.model.net.MySQLSSLHandlerImpl">
            <propertyGroup label="SSL Settings">
                <property id="ssl.ca.cert.value" label="CA Certificate" type="file" description="Choose CA certificate file" features="secured" length="MULTILINE"/>
                <property id="ssl.client.cert.value" label="Client Certificate" type="file" description="Choose client certificate file" features="secured" length="MULTILINE"/>
                <property id="ssl.client.key.value" label="Client Private Key" type="file" description="Choose client private key file" features="secured,password" length="MULTILINE"/>
                <property id="ssl.cipher.suites" label="Cipher Suites (optional)" description="Specify a comma-separated list of cipher suites" type="string" features="secured,password"/>
            </propertyGroup>
            <propertyGroup label="Additional SSL Settings">
                <property id="ssl.require" label="Require SSL" type="boolean" description="Require server support of SSL connection."/>
                <property id="ssl.verify.server" label="Verify server certificate" type="boolean" description="Should the driver verify the server's certificate? When using this feature, the explicit certificate parameters should be specified, rather than system properties." defaultValue="true"/>
                <property id="ssl.public.key.retrieve" label="Allow public key retrieval" type="boolean" description="Allows special handshake roundtrip to get server RSA public key directly from server."/>
            </propertyGroup>
            <objectType name="org.jkiss.dbeaver.ext.mysql.MySQLDataSourceProvider"/>
        </handler>
    </extension>

    <extension point="org.jkiss.dbeaver.sqlCommand">
        <command id="mysql.source" class="org.jkiss.dbeaver.ui.editors.sql.commands.SQLCommandInclude" label="Include" description="Include another MySQL script file"/>
    </extension>

    <extension point="org.jkiss.dbeaver.sqlInsertMethod">
        <method id="mysqlInsertIgnore" class="org.jkiss.dbeaver.ext.mysql.model.MySQLInsertReplaceMethodIgnore" label="INSERT IGNORE" description="Insert ignore duplicate key value"/>
        <method id="mysqlReplaceIgnore" class="org.jkiss.dbeaver.ext.mysql.model.MySQLInsertReplaceMethod" label="REPLACE INTO" description="Insert replace duplicate key value"/>
        <method id="mysqlReplaceIgnoreUpdate" class="org.jkiss.dbeaver.ext.mysql.model.MySQLInsertReplaceMethodUpdate" label="ON DUPLICATE KEY UPDATE" description="Insert update duplicate key value"/>
    </extension>

    <extension point="org.jkiss.dbeaver.sqlDialect">
        <dialect id="mysql" parent="basic" class="org.jkiss.dbeaver.ext.mysql.model.MySQLDialect" label="MySQL" description="MySQL dialect." icon="icons/mysql_icon.png">
            <property name="insertMethods" value="mysqlInsertIgnore,mysqlReplaceIgnore,mysqlReplaceIgnoreUpdate"/>
            <keywords value=""/>
            <execKeywords value=""/>
            <ddlKeywords value=""/>
            <dmlKeywords value=""/>
            <functions value=""/>
            <types value=""/>

            <property name="" value=""/>
        </dialect>
    </extension>


    <extension point="org.eclipse.core.runtime.adapters">
        <factory adaptableType="org.jkiss.dbeaver.ext.mysql.model.MySQLDialect" class="org.jkiss.dbeaver.ext.mysql.sql.MySQLDialectAdapterFactory">
            <adapter type="org.jkiss.dbeaver.model.text.parser.TPRuleProvider"/>
        </factory>
    </extension>

    <extension point="org.jkiss.dbeaver.dashboard">

        <mapQuery id="mysql.query.status" updatePeriod="1000">SHOW GLOBAL STATUS</mapQuery>

        <dashboard id="mysql.traffic" mapQuery="mysql.query.status" label="Traffic" defaultView="timeseries" group="Status"
            calc="delta" value="bytes" mapKeys="Bytes_sent,Bytes_received"  mapLabels="Sent,Received" measure="Kb/s"
            showByDefault="true" description="Server outbound traffic">
            <datasource id="mysql"/>
        </dashboard>

        <dashboard id="mysql.innodb.data" mapQuery="mysql.query.status" label="InnoDB data" defaultView="timeseries" group="Status"
                   calc="delta" value="bytes" mapKeys="Innodb_data_read,Innodb_data_written" mapLabels="Read,Write" measure="Kb/s"
                   showByDefault="true" description="InnoDB data stats">
            <datasource id="mysql"/>
        </dashboard>

        <dashboard id="mysql.innodb.memory" mapQuery="mysql.query.status" label="InnoDB memory" defaultView="timeseries" group="Status"
                   calc="value" value="bytes" mapKeys="Innodb_mem_total,Innodb_mem_dictionary,Innodb_mem_adaptive_hash" mapLabels="Total,Dict,Hash"
                   showByDefault="false" description="InnoDB memory stats">
            <datasource id="mysql"/>
        </dashboard>

        <dashboard id="mysql.com.queries" mapQuery="mysql.query.status" label="Queries" defaultView="timeseries" group="Status"
                   calc="delta" value="integer" mapKeys="Com_select,Com_insert,Com_update,Com_delete" mapLabels="Select,Insert,Update,Delete"
                   showByDefault="true" description="Queries stats">
            <datasource id="mysql"/>
        </dashboard>

        <dashboard id="mysql.key_efficiency" mapQuery="mysql.query.status" label="Key Efficiency" defaultView="timeseries" group="Status" updatePeriod="1000"
            calc="value" value="percent" mapFormula="100 - ((map.key_reads * 100 / map.key_read_requests) / 3)" mapLabels="Key Efficiency" measure="%"
            showByDefault="false" description="Key read efficiency">
            <datasource id="mysql"/>
        </dashboard>

        <dashboard id="mysql.sessions" label="%dashboard.mysql.sessions.label" defaultView="timeseries" group="Standard" updatePeriod="2000" calc="value" value="integer" fetch="rows" showByDefault="true" description="%dashboard.mysql.sessions.description">
            <datasource id="mysql"/>
            <query>SELECT Command,count(*) from information_schema.PROCESSLIST GROUP BY Command</query>
        </dashboard>

    </extension>

    <extension point="org.jkiss.dbeaver.task">
        <category id="mysql" name="MySQL" description="%org.jkiss.dbeaver.task.category.mysql.description" icon="icons/mysql_icon.png"/>
        <category id="mysqlTool" parent="mysql" name="%org.jkiss.dbeaver.task.category.mysqlTool.name" description="%org.jkiss.dbeaver.task.category.mysqlTool.description" icon="icons/mysql_icon.png"/>

        <task id="mysqlDatabaseBackup" name="%task.mysqlDatabaseBackup.name" description="%task.mysqlDatabaseBackup.description"
              icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/file/export.svg" type="mysql"
              handler="org.jkiss.dbeaver.ext.mysql.tasks.MySQLDatabaseExportHandler" requiresExportPrivileges="true">
            <datasource id="mysql"/>
            <objectType name="org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog" if="object.dataSource.supportsNativeClients()"/>
            <objectType name="org.jkiss.dbeaver.ext.mysql.model.MySQLTableBase" if="object.dataSource.supportsNativeClients()"/>
        </task>
        <task id="mysqlDatabaseRestore" name="%task.mysqlDatabaseRestore.name" description="%task.mysqlDatabaseRestore.description"
              icon="platform:/plugin/org.jkiss.dbeaver.ui/icons/file/import.svg" type="mysql"
              handler="org.jkiss.dbeaver.ext.mysql.tasks.MySQLScriptExecuteHandler" requiresMutability="true"
              confirmationMessage="%mysqlDatabaseRestore.confirmationMessage">
            <datasource id="mysql"/>
            <objectType name="org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog" if="object.dataSource.supportsNativeClients()"/>
        </task>
        <task id="mysqlScriptExecute" name="%task.mysqlScriptExecute.name" description="%task.mysqlScriptExecute.description" icon="platform:/plugin/org.jkiss.dbeaver.model/icons/tree/script.svg" type="mysql" handler="org.jkiss.dbeaver.ext.mysql.tasks.MySQLScriptExecuteHandler">
            <datasource id="mysql"/>
            <objectType name="org.jkiss.dbeaver.ext.mysql.model.MySQLCatalog" if="object.dataSource.supportsNativeClients()"/>
        </task>

        <!-- SQL tools -->
        <task id="mysqlToolCheckTable" name="%task.mysqlToolCheckTable.name" description="%task.mysqlToolCheckTable.description" icon="platform:/plugin/org.jkiss.dbeaver.model/icons/tree/admin.svg" type="mysqlTool" handler="org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableCheck">
            <datasource id="mysql"/>
            <objectType name="org.jkiss.dbeaver.ext.mysql.model.MySQLTable"/>
        </task>
        <task id="mysqlToolRepairTable" name="%task.mysqlToolRepairTable.name" description="%task.mysqlToolRepairTable.description" icon="platform:/plugin/org.jkiss.dbeaver.model/icons/tree/admin.svg" type="mysqlTool" handler="org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableRepair">
            <datasource id="mysql"/>
            <objectType name="org.jkiss.dbeaver.ext.mysql.model.MySQLTable"/>
        </task>
        <task id="mysqlToolAnalyzeTable" name="%task.mysqlToolAnalyzeTable.name" description="%task.mysqlToolAnalyzeTable.description" icon="platform:/plugin/org.jkiss.dbeaver.model/icons/tree/admin.svg" type="mysqlTool" handler="org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableAnalyze">
            <datasource id="mysql"/>
            <objectType name="org.jkiss.dbeaver.ext.mysql.model.MySQLTable"/>
        </task>
        <task id="mysqlToolOptimizeTable" name="%task.mysqlToolOptimizeTable.name" description="%task.mysqlToolOptimizeTable.description" icon="platform:/plugin/org.jkiss.dbeaver.model/icons/tree/admin.svg" type="mysqlTool" handler="org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableOptimize">
            <datasource id="mysql"/>
            <objectType name="org.jkiss.dbeaver.ext.mysql.model.MySQLTable"/>
        </task>
        <task id="mysqlToolTruncateTable" name="%task.mysqlToolTruncateTable.name" description="%task.mysqlToolTruncateTable.description" icon="platform:/plugin/org.jkiss.dbeaver.model/icons/tree/admin.svg" type="mysqlTool" handler="org.jkiss.dbeaver.ext.mysql.tasks.MySQLToolTableTruncate">
            <datasource id="mysql"/>
            <objectType name="org.jkiss.dbeaver.ext.mysql.model.MySQLTable"/>
        </task>

    </extension>

</plugin>
