<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.2"?>

<plugin>

    <extension point="org.jkiss.dbeaver.generic.meta">
        <meta id="vertica" class="org.jkiss.dbeaver.ext.vertica.model.VerticaMetaModel" driverClass="com.vertica.jdbc.Driver"/>
    </extension>

    <extension point="org.jkiss.dbeaver.dataSourceProvider">
        <datasource
            class="org.jkiss.dbeaver.ext.vertica.VerticaDataSourceProvider"
            description="Vertica JDBC connector"
            id="vertica"
            parent="generic"
            label="Vertica"
            icon="icons/vertica_icon.png"
            dialect="vertica">

            <tree path="vertica" label="Vertica data source">
                <folder type="org.jkiss.dbeaver.ext.vertica.model.VerticaSchema" label="%tree.schemas.node.name" icon="#folder_schema" description="Schemas">
                    <items label="#schema" path="schema" property="schemaList" icon="#schema" optional="true">
                        <icon if="object.system" icon="#schema_system"/>
                        <folder type="org.jkiss.dbeaver.ext.vertica.model.VerticaTable" label="%tree.tables.node.name" icon="#folder_table" description="Tables">
                            <items label="%tree.table.node.name" path="table" property="physicalTables" icon="#table">
                                <folder type="org.jkiss.dbeaver.ext.vertica.model.VerticaTableColumn" label="%tree.columns.node.name" icon="#columns" description="Table columns">
                                    <items label="%tree.column.node.name" path="attribute" property="attributes" icon="#column">
                                    </items>
                                </folder>
                                <folder type="org.jkiss.dbeaver.ext.vertica.model.VerticaConstraint" label="%tree.uni_keys.node.name" icon="#constraints" description="Table unique keys" visibleIf="!object.view">
                                    <items label="%tree.uni_key.node.name" path="uniqueKey" property="constraints" icon="#unique-key">
                                        <items label="%tree.uni_key.columns.node.name" itemLabel="%tree.column.node.name" path="column" property="attributeReferences" navigable="false" inline="true">
                                        </items>
                                    </items>
                                </folder>
                                <folder type="org.jkiss.dbeaver.ext.generic.model.GenericTableForeignKey" label="%tree.foreign_keys.node.name" icon="#foreign-keys" description="Table foreign keys" visibleIf="!object.view &amp;&amp; object.dataSource.info.supportsReferentialIntegrity()">
                                    <items label="%tree.foreign_key.node.name" path="association" property="associations" icon="#foreign-key">
                                        <items label="%tree.foreign_key_columns.node.name" itemLabel="%tree.column.node.name" path="column" property="attributeReferences" navigable="false" inline="true">
                                        </items>
                                    </items>
                                </folder>
<!--
                                <folder type="org.jkiss.dbeaver.ext.generic.model.GenericTableIndex" label="%tree.indexes.node.name" icon="#indexes" description="Table indexes" visibleIf="object.dataSource.info.supportsIndexes()">
                                    <items label="%tree.index.node.name" path="index" property="indexes" icon="#index">
                                        <items label="%tree.index_columns.node.name" path="column" property="attributeReferences" icon="#column" navigable="false" inline="true">
                                        </items>
                                    </items>
                                </folder>
-->
                                <folder label="%tree.references.node.name" icon="#references" description="Table references" visibleIf="!object.view &amp;&amp; object.dataSource.info.supportsReferentialIntegrity()" virtual="true">
                                    <items label="%tree.reference.node.name" path="reference" property="references" icon="#reference" virtual="true">
                                        <items label="%tree.reference_columns.node.name" itemLabel="%tree.column.node.name" path="column" property="attributeReferences" navigable="false" inline="true" virtual="true">
                                        </items>
                                    </items>
                                </folder>
                                <folder type="org.jkiss.dbeaver.ext.generic.model.GenericTrigger" label="%tree.triggers.node.name" icon="#triggers" description="Table triggers" visibleIf="object.dataSource.metaModel.supportsTriggers(object.dataSource)">
                                    <items label="%tree.trigger.node.name" path="trigger" property="triggers" icon="#trigger"/>
                                </folder>
                            </items>
                        </folder>
                        <folder type="org.jkiss.dbeaver.ext.vertica.model.VerticaFlexTable" label="%tree.flexTables.node.name" icon="#folder_table" description="Flex tables">
                            <items label="%tree.flexTable.node.name" path="flexTable" property="flexTables" icon="#table_link">
                                <folder type="org.jkiss.dbeaver.ext.generic.model.GenericTableColumn" label="%tree.columns.node.name" icon="#columns" description="Flex table columns">
                                    <items label="%tree.column.node.name" path="attribute" property="attributes" icon="#column">
                                    </items>
                                </folder>
                            </items>
                        </folder>
                        <folder type="org.jkiss.dbeaver.ext.vertica.model.VerticaView" label="%tree.tviews.node.name" icon="#folder_view" description="Views">
                            <items label="%tree.tview.node.name" path="view" property="views" icon="#view">
                                <folder type="org.jkiss.dbeaver.ext.generic.model.GenericTableColumn" label="%tree.columns.node.name" icon="#columns" description="View columns">
                                    <items label="%tree.column.node.name" path="attribute" property="attributes" icon="#column">
                                    </items>
                                </folder>
                            </items>
                        </folder>
                        <folder type="org.jkiss.dbeaver.ext.vertica.model.VerticaProjection" label="%tree.projections.node.name" icon="#folder_projection" description="Projections">
                            <items label="%tree.projection.node.name" path="projection" property="projections" icon="#table_index">
                                <folder type="org.jkiss.dbeaver.ext.vertica.model.VerticaProjectionColumn" label="%tree.columns.node.name" icon="#columns" description="Projection columns">
                                    <items label="%tree.column.node.name" path="attribute" property="attributes" icon="#column">
                                    </items>
                                </folder>
                            </items>
                        </folder>
                        <folder type="org.jkiss.dbeaver.ext.generic.model.GenericProcedure" label="%tree.procedures.node.name" icon="#procedures" description="Procedures" visibleIf="object.dataSource.splitProceduresAndFunctions()">
                            <items label="%tree.procedures.node.name" itemLabel="%tree.procedure.node.name" path="procedure" property="proceduresOnly" icon="#procedure">
                                <items label="%tree.procedure_columns.node.name" itemLabel="%tree.column.node.name" path="column" property="parameters" navigable="false"/>
                            </items>
                        </folder>
                        <folder label="%tree.functions.node.name" icon="#functions" description="Functions/UDFs">
                            <items label="%tree.functions.node.name" itemLabel="%tree.function.node.name" path="function" property="functionsOnly" icon="#function">
                                <items label="%tree.function_columns.node.name" itemLabel="%tree.column.node.name" path="column" property="parameters" navigable="false"/>
                            </items>
                        </folder>
                        <folder type="org.jkiss.dbeaver.ext.vertica.model.VerticaSequence" label="%tree.sequences.node.name" icon="#sequences" description="Sequences" >
                            <items label="%tree.sequence.node.name" path="sequence" property="sequences" icon="#sequence"/>
                        </folder>
                    </items>
                </folder>
                <folder label="%tree.admin.node.name" icon="#folder_admin" id="folderAdmin" description="%tree.admin.node.description">
                    <folder type="org.jkiss.dbeaver.model.struct.DBSDataType" label="%tree.dataTypes.node.name" icon="#data_types" description="Global data types">
                        <items label="%tree.dataType.node.name" path="dataType" property="dataTypes" icon="#data_type"/>
                    </folder>
                    <folder type="org.jkiss.dbeaver.ext.vertica.model.VerticaNode" label="%tree.nodes.node.name" icon="#folder_server" description="Nodes">
                        <items label="%tree.node.node.name" path="clusterNodes" property="clusterNodes" icon="#server">
                        </items>
                    </folder>
                </folder>
            </tree>

            <drivers manageable="true">
                <!-- Legacy drivers -->
                <driver id="vertica-jdbc"/>
                <driver id="vertica-jdbc-6"/>
                <!-- Real driver -->
                <driver
                    id="vertica-jdbc-8"
                    label="Vertica"
                    icon="icons/vertica_icon.png"
                    iconBig="icons/vertica_icon_big.png"
                    class="com.vertica.jdbc.Driver"
                    sampleURL="jdbc:vertica://{host}:{port}/[{database}]"
                    defaultPort="5433"
                    webURL="https://www.vertica.com/download/vertica/client-drivers/"
                    description="JDBC driver for Vertica database"
                    supportedConfigurationTypes="MANUAL,URL"
                    categories="sql,analytic,columns">
                    <replace provider="generic" driver="vertica"/>
                    <replace provider="vertica" driver="vertica-jdbc"/>
                    <replace provider="vertica" driver="vertica-jdbc-6"/>

                    <file type="license" path="drivers/vertica/LICENSE.txt" bundle="drivers.vertica"/>
                    <file type="jar" path="maven:/com.vertica.jdbc:vertica-jdbc:RELEASE" bundle="!drivers.vertica"/>
                    <file type="jar" path="drivers/vertica" bundle="drivers.vertica"/>

                    <parameter name="query-get-active-db" value="select current_schema()"/>
                    <parameter name="query-set-active-db" value="SET search_path = ?,&quot;$user&quot;,public,v_catalog,v_monitor,v_internal"/>
                    <parameter name="active-entity-type" value="schema"/>

                    <parameter name="ddl-drop-column-short" value="true"/>
                    <parameter name="supports-scroll" value="false"/>
                    <!-- Limits affect INSERT INTO and other DML queries-->
                    <parameter name="supports-limits" value="false"/>

                    <property name="@dbeaver-default-resultset.maxrows.sql" value="true"/>
                </driver>
                <provider-properties drivers="*">
                    <propertyGroup label="Settings">
                        <property id="disable-comments-reading@" label="Disable objects comments reading" type="boolean" description="Disable objects (tables/sequences/projections, etc.) comments reading. Reading this type of information can dramatically reduce data loading speed." supportedConfigurationTypes="MANUAL,URL"/>
                        <property id="dollar-quotes-as-string@" label="Show $$ quotes as String" type="boolean" supportedConfigurationTypes="MANUAL,URL"/>
                    </propertyGroup>
                </provider-properties>
            </drivers>

        </datasource>
    </extension>


<!--
    FIXME: it is not official and it doesn't support https
    <extension point="org.jkiss.dbeaver.mavenRepository">
        <repository id="vertica-maven-repo" name="Vertica Repository" url="https://maven.icm.edu.pl/artifactory/repo/" order="10">
            <scope group="com.vertica"/>
        </repository>
    </extension>
-->

    <extension point="org.jkiss.dbeaver.objectManager">
        <manager class="org.jkiss.dbeaver.ext.vertica.edit.VerticaFlexTableManager" objectType="org.jkiss.dbeaver.ext.vertica.model.VerticaFlexTable"/>
        <manager class="org.jkiss.dbeaver.ext.vertica.edit.VerticaTableColumnManager" objectType="org.jkiss.dbeaver.ext.vertica.model.VerticaTableColumn"/>
        <manager class="org.jkiss.dbeaver.ext.vertica.edit.VerticaProjectionColumnManager" objectType="org.jkiss.dbeaver.ext.vertica.model.VerticaProjectionColumn"/>
        <manager class="org.jkiss.dbeaver.ext.vertica.edit.VerticaConstraintManager" objectType="org.jkiss.dbeaver.ext.vertica.model.VerticaConstraint"/>
        <manager class="org.jkiss.dbeaver.ext.vertica.edit.VerticaSequenceManager" objectType="org.jkiss.dbeaver.ext.vertica.model.VerticaSequence"/>
    </extension>

    <extension point="org.jkiss.dbeaver.sqlDialect">
        <dialect id="vertica" parent="generic" class="org.jkiss.dbeaver.ext.vertica.model.VerticaSQLDialect" label="Vertica" description="Vertica SQL dialect." icon="icons/vertica_icon.png">
        </dialect>
    </extension>

    <extension point="org.jkiss.dbeaver.dataTypeProvider">
        <provider
          id="VerticaValueHandlerProvider"
          parent="GenericValueHandlerProvider"
          class="org.jkiss.dbeaver.ext.vertica.model.data.VerticaValueHandlerProvider"
          label="Vertica data types provider"
          description="Vertica data types provider">

            <datasource id="vertica"/>
            <type name="UUID"/>
        </provider>
    </extension>

</plugin>
