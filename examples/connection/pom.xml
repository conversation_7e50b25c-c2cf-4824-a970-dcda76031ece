<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.dbeaver.jdbc</groupId>
    <artifactId>libsql-test-connection</artifactId>
    <version>0.0.1-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.dbeaver.jdbc</groupId>
            <artifactId>com.dbeaver.jdbc.driver.libsql</artifactId>
            <version>1.0.4</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <mainClass>com.dbeaver.test.libsql.LibSqlTest</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
