<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.dbeaver.jdbc</groupId>
    <artifactId>jdbc-libsql</artifactId>
    <version>1.0.5-SNAPSHOT</version>

    <packaging>pom</packaging>
    <name>DBeaver LibSQL JDBC Project</name>
    <description>LibSQL JDBC driver</description>
    <url>https://github.com/dbeaver/dbeaver-jdbc-libsql</url>
    <properties>
        <java.version>${java.version.drivers}</java.version>
    </properties>

    <parent>
        <groupId>com.dbeaver.common</groupId>
        <artifactId>com.dbeaver.common.main</artifactId>
        <version>2.5.0-SNAPSHOT</version>
        <relativePath>../dbeaver-common</relativePath>
    </parent>

    <profiles>
        <profile>
            <id>sonatypeDeploy</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.sonatype.central</groupId>
                        <artifactId>central-publishing-maven-plugin</artifactId>
                        <extensions>true</extensions>
                        <configuration>
                            <publishingServerId>central</publishingServerId>
                            <autoPublish>false</autoPublish>
                            <waitUntil>validated</waitUntil>
                            <excludeArtifacts>jdbc-libsql</excludeArtifacts>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <modules>
        <module>com.dbeaver.jdbc.driver.libsql</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-runner</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <sourceDirectory>${project.basedir}/src/main/java</sourceDirectory>
        <testSourceDirectory>${project.basedir}/src/test/java</testSourceDirectory>

        <plugins>
            <!-- Use minimum supported Java -->
            <plugin>
                <groupId>org.eclipse.tycho</groupId>
                <artifactId>tycho-compiler-plugin</artifactId>
                <version>${tycho-version}</version>
                <configuration>
                    <useProjectSettings>false</useProjectSettings>
                    <source>${java.version.min}</source>
                    <target>${java.version.min}</target>
                    <compilerVersion>${java.version.min}</compilerVersion>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <executions>
                    <execution>
                        <id>test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>test</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
